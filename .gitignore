# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/
# If you're building an application, you may want to check-in your pubspec.lock
pubspec.lock

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Generated code
# make sure to exclude these files from the test coverage, see "dart pub global run remove_from_coverage:remove_from_coverage"
**/*.freezed.dart
**/*.g.dart
**/*.gr.dart
**/*.gen.dart

# dotenv environment variables file
.env*

# FVM Version Cache
.fvm/

.vscode/settings.json
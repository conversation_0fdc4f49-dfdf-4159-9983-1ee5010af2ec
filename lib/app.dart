import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/domain/online_user_model.dart';
import 'package:smart_team_web/core/environment/environment_banner.dart';
import 'package:smart_team_web/core/managers/online_user_manager/online_user_manager.dart';
import 'package:smart_team_web/core/router/auto_router.dart';
import 'package:smart_team_web/i18n/strings.g.dart';
import 'package:smart_team_web/localization/app_localization_provider.dart';
import 'package:smart_team_web/src/shared/constants/app_path.dart';
import 'package:smart_team_web/src/shared/constants/constants.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/theme/theme_provider.dart';
import 'package:smart_team_web/src/widgets/base_async_provider_widget.dart';
import 'package:smart_team_web/src/widgets/global_overlays/global_overlays_wrapper.dart';
import 'package:smart_team_web/src/widgets/image/asset_image.dart';

final GlobalKey<NavigatorState> appNavigatorKey = GlobalKey<NavigatorState>();

class App extends StatefulHookConsumerWidget {
  const App({
    super.key,
  });

  @override
  ConsumerState<App> createState() => _AppState();
}

class _AppState extends ConsumerState<App> {
  late final AppRouter _appRouter;

  @override
  void initState() {
    super.initState();
    _appRouter = ref.read(routerProvider);
  }

  @override
  Widget build(BuildContext context) {
    final onlineUserManager = ref.watch(onlineUserManagerProvider);
    final locale = ref.watch(languageProvider);

    Widget buildLoading() {
      return Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            color: AColor.primaryColor,
          ),
          child: const Center(
            child: AImage(imgPath: APath.appLogoWhite),
          ),
        ),
      );
    }

    return Portal(
      child: EnvironmentBanner(
        child: TranslationProvider(
          child: MaterialApp.router(
            key: appNavigatorKey,
            title: kAppTitle,
            debugShowCheckedModeBanner: false,
            restorationScopeId: 'smart_team_web',
            supportedLocales: AppLocaleUtils.supportedLocales,
            locale: Locale(locale.languageCode),
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
              CountryLocalizations.delegate,
            ],
            builder: (context, child) {
              return BaseAsyncProviderWidget<OnlineUserModel?>(
                value: onlineUserManager,
                loadingWidget: buildLoading(),
                builder: (_) =>
                    AEScaffold(child: GlobalOverlaysWrapper(child: child!)),
              );
            },
            theme: lightTheme,
            themeMode: ThemeMode.light,
            routerConfig: _appRouter.config(),
          ),
        ),
      ),
    );
  }
}

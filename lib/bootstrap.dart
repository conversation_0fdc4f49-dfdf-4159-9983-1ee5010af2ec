import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_web_plugins/url_strategy.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/environment/environment.dart';
import 'package:smart_team_web/i18n/strings.g.dart';

Future<void> bootstrap(FutureOr<Widget> Function() builder) async {
  WidgetsFlutterBinding.ensureInitialized();
  usePathUrlStrategy();

  // Initialize slang
  await LocaleSettings.useDeviceLocale();

  final providerContainer = ProviderContainer();
  final env = providerContainer.read(environmentProvider);

  await Supabase.initialize(
    url: env.supabaseUrl,
    anonKey: env.supabaseAnonKey,
  );

  final cache = providerContainer.read(cacheManagerProvider);
  await cache.initialize();

  await GoogleFonts.pendingFonts(
    [GoogleFonts.rubik(), GoogleFonts.rubikTextTheme()],
  );

  final appWidget = await builder();

  runApp(
    UncontrolledProviderScope(
      container: providerContainer,
      child: appWidget,
    ),
  );
}

import 'package:smart_team_common/smart_team_common.dart';

class AppStorageKeys {
  const AppStorageKeys._();

  /// theme mode → stored as int (ThemeMode.index)
  static final StorageKey<int> themeMode =
      StorageKey.create('themeMode', defaultValue: 0);

  /// language id → stored as int
  static final StorageKey<int> languageId =
      StorageKey.create('languageId', defaultValue: 0);

  static final StorageKey<String?> selectedCompanyId =
      StorageKey<String?>.create('selectedCompanyId');

  static final StorageKey<Company?> selectedCompany =
      StorageKey<Company?>.create('selectedCompany');
}

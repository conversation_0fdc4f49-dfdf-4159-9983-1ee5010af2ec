import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:smart_team_common/smart_team_common.dart';

part 'online_user_model.freezed.dart';

@freezed
abstract class OnlineUserModel with _$OnlineUserModel {
  const factory OnlineUserModel({
    User? user,
    UserRole? userRole,
  }) = _OnlineUserModel;

  const OnlineUserModel._();

  bool get isAdminUser => userRole?.role.isAdmin ?? false;


}

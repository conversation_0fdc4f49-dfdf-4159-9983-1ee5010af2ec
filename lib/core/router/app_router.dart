class RoutePaths {
  static const String dashboard = 'dashboard';
  static const String main = '/main';
  static const String auth = '/auth';
  static const String viewForm = '/view-form/:templateId';
  static const String reports = 'reports';
  static const String calendar = 'calendar';
  static const String jobList = 'job-list';
  static const String forms = 'forms';
  static const String settings = 'settings';
  static const String profile = 'profile';
  static const String map = 'map';
  static const String forgetPassword = '/forget-password';
  static const String admin = 'admin';
  static const String performance = 'performance';
  static const String permits = 'permits';
  static const String costs = 'costs';
  static const String personalManagement = 'personal-management';
  static const String personalDetail = 'detail';
  static const String editForm = 'edit-form/:formTemplateId';
}

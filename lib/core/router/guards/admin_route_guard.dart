import 'package:auto_route/auto_route.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/core/managers/online_user_manager/online_user_manager.dart';
import 'package:smart_team_web/core/router/auto_router.gr.dart';
import 'package:smart_team_web/src/shared/extensions/riverpod_extensions.dart';

class AdminRouteGuard extends AutoRouteGuard {
  AdminRouteGuard(this.ref);

  final Ref ref;

  @override
  void onNavigation(NavigationResolver resolver, StackRouter router) {
    final onlineUser = ref.futureValue(onlineUserManagerProvider);
    if (onlineUser == null) {
      router.replaceAll([const AuthRoute()]);
      return;
    }

    if (onlineUser.isAdminUser) {
      resolver.next();
    } else {
      router.replaceAll([const DashboardRoute()]);
    }
  }
}

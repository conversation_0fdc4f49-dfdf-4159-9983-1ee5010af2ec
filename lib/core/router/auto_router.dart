import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/online_user_manager/online_user_manager.dart';
import 'package:smart_team_web/core/router/app_router.dart';
import 'package:smart_team_web/core/router/auto_router.gr.dart';
import 'package:smart_team_web/core/router/guards/guards.dart';
import 'package:smart_team_web/src/shared/extensions/riverpod_extensions.dart';

import 'package:smart_team_web/src/shared/extensions/riverpod_extensions.dart';

const formShellRoute = EmptyShellRoute('formShellRoute');

final routerProvider = Provider<AppRouter>((ref) {
  return AppRouter(ref: ref);
});

@AutoRouterConfig()
class AppRouter extends RootStackRouter {
  AppRouter({required this.ref});

  final Ref ref;

  @override
  List<AutoRouteGuard> get guards => [AutoRouteGuard.simple(_onNavigation)];

  final routesAllowed = [ViewFormRoute.name];

  // These routes are allowed to be accessed without authentication
  final routesAllowedWithoutAuth = [
    AuthRoute.name,
    ForgetPasswordRoute.name,
    ViewFormRoute.name,
  ];

  void _onNavigation(NavigationResolver resolver, StackRouter router) {
    if (routesAllowed.contains(resolver.route.name)) {
      resolver.next();
      return;
    }

    final onlineUser = ref.futureValue(onlineUserManagerProvider);
    final isAuthenticated = onlineUser != null;

    if (routesAllowedWithoutAuth.contains(resolver.route.name)) {
      if (isAuthenticated) {
        resolver.redirectUntil(
          onlineUser.isAdminUser
              ? const AdminRoute()
              : const DashboardRoute(),
        );
      } else {
        resolver.next();
      }
      return;
    }

    if (!isAuthenticated) {
      resolver.redirectUntil(const AuthRoute());
      return;
    }

    resolver.next();
  }

  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          page: AuthRoute.page,
          initial: true,
        ),
        AutoRoute(
          page: ForgetPasswordRoute.page,
          path: RoutePaths.forgetPassword,
        ),
        AutoRoute(
          page: ViewFormRoute.page,
          path: RoutePaths.viewForm,
        ),
        AutoRoute(
          page: MainRoute.page,
          path: RoutePaths.main,
          children: [
            AutoRoute(
              page: DashboardRoute.page,
              path: RoutePaths.dashboard,
            ),
            AutoRoute(page: ReportsRoute.page, path: RoutePaths.reports),
            AutoRoute(page: CalendarRoute.page, path: RoutePaths.calendar),
            AutoRoute(page: JobListRoute.page, path: RoutePaths.jobList),
            AutoRoute(
              page: formShellRoute.page,
              path: RoutePaths.forms,
              children: [
                AutoRoute(page: FormsRoute.page, path: ''),
                AutoRoute(page: EditFormRoute.page, path: RoutePaths.editForm),
              ],
            ),
            AutoRoute(page: SettingsRoute.page, path: RoutePaths.settings),
            AutoRoute(page: MyProfileRoute.page, path: RoutePaths.profile),
            AutoRoute(page: DynamicMapRoute.page, path: RoutePaths.map),
            AutoRoute(page: CostsRoute.page, path: RoutePaths.costs, maintainState: false),
            AutoRoute(
              page: PersonalManagementRoute.page,
              path: RoutePaths.personalManagement,
            ),
            AutoRoute(
              page: PersonalDetailRoute.page,
              path: RoutePaths.personalDetail,
            ),
            AutoRoute(
              page: AdminRoute.page,
              path: RoutePaths.admin,
              guards: [
                AdminRouteGuard(ref),
              ],
            ),
            AutoRoute(
              page: PerformanceRoute.page,
              path: RoutePaths.performance,
            ),
            AutoRoute(page: PermitsRoute.page, path: RoutePaths.permits),
          ],
        ),
      ];
}

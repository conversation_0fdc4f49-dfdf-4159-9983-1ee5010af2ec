import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/domain/online_user_model.dart';
import 'package:smart_team_web/core/router/auto_router.dart';
import 'package:smart_team_web/core/router/auto_router.gr.dart';
import 'package:smart_team_web/src/auth/repository/auth_repository.dart';

final onlineUserManagerProvider = AsyncNotifierProvider<OnlineUserManager, OnlineUserModel?>(
  OnlineUserManager.new,
);

class OnlineUserManager extends AsyncNotifier<OnlineUserModel?> {
  @override
  FutureOr<OnlineUserModel?> build() async {
    final user = await getOnlineUser();
    return user;
  }

  Future<OnlineUserModel?> getOnlineUser() async {
    try {
      final currentUserId = ref.read(userRepositoryProvider).getCurrentOnlineUserId();
      if (currentUserId == null) return null;
      final user = await ref
          .read(userRepositoryProvider)
          .fetchUserById(currentUserId)
          .timeout(const Duration(seconds: 10));
      final userRole = await ref.read(userRoleRepositoryProvider).fetchUserRoleById(currentUserId);
      return OnlineUserModel(
        user: user,
        userRole: userRole,
      );
    } catch (e, stackTrace) {
      debugPrint("<debug> error NOTIFIER: $e");
      debugPrint("<debug> stackTrace NOTIFIER: $stackTrace");
      return null;
    }
  }

  bool isLoggedIn() {
    return state.value != null;
  }

  Future<bool> signIn(String email, String password) async {
    final authRepository = ref.read(authRepositoryProvider);
    await authRepository.signIn(email, password);
    final user = await getOnlineUser();
    state = AsyncData(user);
    return user != null;
  }

  Future<void> signOutAndNavigateToLogin() async {
    await ref.read(authRepositoryProvider).logOut();
    state = const AsyncData(null);
    await ref.read(routerProvider).replaceAll([const AuthRoute()]);
  }
}

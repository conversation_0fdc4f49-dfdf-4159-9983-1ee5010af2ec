import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/toast/toast_manager.dart';
import 'package:url_launcher/url_launcher.dart';

final urlManagerProvider = Provider.autoDispose<UrlManager>(UrlManager.new);

class UrlManager {
  UrlManager(this.ref);
  final Ref ref;

  String getBaseUrlWithUri() {
    final uri = Uri.base;
    // omit port if it's the default for the scheme
    final portPart = (uri.hasPort && uri.port != 80 && uri.port != 443)
        ? ':${uri.port}'
        : '';
    return '${uri.scheme}://${uri.host}$portPart';
  }

  Future<bool> goToLink(String link, {bool isNewTab = true}) async {
    try {
      await launchUrl(
        Uri.parse(link),
        webOnlyWindowName: isNewTab ? '_blank' : '_self',
      );
      return true;
    } catch (e) {
      ref.read(toastManagerProvider).showToastErrorWithMessage(
            'Link açılamadı'.hardcoded,
          );
      return false;
    }
  }
}

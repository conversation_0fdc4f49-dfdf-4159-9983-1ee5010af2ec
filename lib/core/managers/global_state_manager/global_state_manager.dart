import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/local_storage/storage_key.dart';
import 'package:smart_team_web/core/managers/global_state_manager/global_state.dart';

final globalStateManagerProvider = NotifierProvider<GlobalStateManager, GlobalState>(
  GlobalStateManager.new,
);

class GlobalStateManager extends Notifier<GlobalState> {
  late CacheManager _cacheManager;

  @override
  GlobalState build() {
    _cacheManager = ref.read(cacheManagerProvider);
    final selectedCompany = _cacheManager.read<Company?>(key: AppStorageKeys.selectedCompany);
    return GlobalState(selectedCompany: selectedCompany);
  }

  Future<void> setSelectedCompany(Company company) async {
    state = state.copyWith(selectedCompany: company);
    await _cacheManager.write(key: AppStorageKeys.selectedCompany, value: company);
  }
}

import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/toast/toast_manager.dart';

final mediaManagerProvider = Provider.autoDispose<_MediaManager>(
  _MediaManager.new,
);

class _MediaManager {
  _MediaManager(this.ref);

  final Ref ref;

  Future<List<XFile>?> pickFiles() async {
    final pickedFiles = await ref.read(mediaServiceProvider).pickFile();
    return pickedFiles;
  }

  Future<List<XFile>?> pickFile({
    bool allowMultiple = false,
    List<String>? allowExtensions,
    int? maxFileSize,
  }) async {
    try {
      final pickedFiles = await ref.read(mediaServiceProvider).pickFile(
            allowMultiple: allowMultiple,
            allowExtensions: allowExtensions,
            maxFileSize: maxFileSize,
          );
      return pickedFiles;
    } catch (e, stackTrace) {
      debugPrint(e.toString());
      debugPrint(stackTrace.toString());

      if (e is FileSizeLimitException) {
        ref
            .read(toastManagerProvider)
            .showToastErrorWithMessage('File size limit exceeded');
      }
      return null;
    }
  }

  Future<void> createBucketIfNeeded({
    required StorageBucket bucket,
  }) async {
    final isBucketExists =
        await ref.read(uploadServiceProvider).checkBucketExists(bucket: bucket);
    if (!isBucketExists) {
      await ref.read(uploadServiceProvider).createBucket(bucket: bucket);
    }
  }

  Future<List<String>?> uploadFileList({
    required StorageBucket bucket,
    required List<XFile> files,
    String? additionalPath,
  }) async {
    final urlList = <String>[];
    for (final file in files) {
      final downloadUrl =
          await ref.read(uploadServiceProvider).uploadFileToSupabase(
                bucket: bucket,
                file: file,
                additionalPath: additionalPath,
              );
      if (downloadUrl != null) {
        urlList.add(downloadUrl);
      }
    }
    return urlList;
  }

  Future<String?> uploadFile({
    required StorageBucket bucket,
    required XFile file,
    String? additionalPath,
  }) async {
    final uploadResult =
        await ref.read(uploadServiceProvider).uploadFileToSupabase(
              bucket: bucket,
              file: file,
              additionalPath: additionalPath,
            );
    return uploadResult;
  }

  Future<bool> deleteFile({
    required StorageBucket bucket,
    required String fileName,
    String? additionalPath,
  }) async {
    final isDeleted =
        await ref.read(uploadServiceProvider).deleteFile(
              bucket: bucket,
              fileName: fileName,
              additionalPath: additionalPath,
            );
    return isDeleted;
  }
}

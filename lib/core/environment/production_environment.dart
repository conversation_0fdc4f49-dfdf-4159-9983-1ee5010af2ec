part of 'environment.dart';

final class _ProductionEnvironment extends _EnvironmentBase {
  @override
  Color get bannerColor => Colors.green;

  @override
  String get bannerName => 'PROD';

  @override
  String get apiBaseUrl => '';

  @override
  String get supabaseUrl => '';

  @override
  String get supabaseAnonKey => '';

  @override
  // TODO(Levent): Change to production url
  String get firebaseFunctionsUrl =>
      'https://us-central1-smart-team-8d124.cloudfunctions.net';
      
  @override
  String get webUrl => 'https://smart-team-8d124.web.app';
}

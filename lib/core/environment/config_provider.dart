import 'package:hooks_riverpod/hooks_riverpod.dart';

final configProvider = StateProvider<String>((ref) {
  // You can use a default value here, or read it from your app's configuration
  // Alternatively, set it as an environment variable when initializing your app.
  // For now, we're using 'development' as the default.
  return const String.fromEnvironment('envConfig', defaultValue: 'development');
});

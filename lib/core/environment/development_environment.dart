part of 'environment.dart';

final class _DevelopmentEnvironment extends _EnvironmentBase {
  @override
  Color get bannerColor => Colors.red;

  @override
  String get bannerName => 'DEV';

  @override
  String get apiBaseUrl => '';

  @override
  String get supabaseUrl => 'https://unounzqgbpebxnkjgndg.supabase.co';

  @override
  String get supabaseAnonKey => 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVub3VuenFnYnBlYnhua2pnbmRnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ2MjA5NTAsImV4cCI6MjA2MDE5Njk1MH0.thMiDvsW_GaBJ335l6mM62bmxVp6e-VgJ4bCEN7UXFc';

  @override
  String get firebaseFunctionsUrl =>
      'https://us-central1-smart-team-8d124.cloudfunctions.net';

  @override
  String get webUrl => 'http://localhost:8000';
}

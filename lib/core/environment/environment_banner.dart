import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/core/environment/environment.dart';

@immutable
class EnvironmentBanner extends ConsumerWidget {
  const EnvironmentBanner({
    required this.child,
    super.key,
  });

  final Widget child;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final env = ref.watch(environmentProvider);
    if (env.isProd && !kDebugMode) return child;

    Widget buildBanner(BuildContext context) {
      return Banner(
        location: BannerLocation.bottomEnd,
        message: env.bannerName,
        color: env.bannerColor,
        layoutDirection: TextDirection.ltr,
        textDirection: TextDirection.ltr,
        child: child,
      );
    }

    return buildBanner(context);
  }
}

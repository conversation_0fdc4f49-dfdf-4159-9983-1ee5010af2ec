import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/environment/environment_interface.dart';

part 'development_environment.dart';
part 'production_environment.dart';

/// Returns correct environment depending on the [envConfig] that we pass while running the app
/// make the enviroment private to expose only via the [environmentProvider]
final environmentProvider = Provider<IEnvironment>((_) {
  const envConfig = String.fromEnvironment('envConfig'); //development
  return _EnvironmentFactory.create(envConfig);
});

extension EnvExt on IEnvironment {
  bool get isDev => this is _DevelopmentEnvironment;
  bool get isProd => this is _ProductionEnvironment;
}

/// Sealed base class for the environment, not meant to be used directly
sealed class _EnvironmentBase implements IEnvironment {
  @override
  String get unchangedVariable => '';

  @override
  String get googleMapsApiKey => 'AIzaSyAYqiQm8dMF2g7XmfE7d2uZg33srEDFJDs';

  @override
  String get countryStateCityApiKey =>
      'WHBEZk96OVJxSDlIUHFJZ052WmNKT0lFSUFtbHp0cTBKR3hYNldYVw==';

  @override
  String get googlePlacesApiKey => 'AIzaSyCdiTZpfvei2BGw4RiYkeI3mSYlgxNwczk';

  @override
  String get cloudFunctionsBaseUrl =>
      'https://us-central1-smart-team-8d124.cloudfunctions.net';

  @override
  String get googleTextSearchPlaceUrl => '';

  @override
  String get googleAutoCompleteUrl =>
      'https://us-central1-smart-team-8d124.cloudfunctions.net/autocompletePlace';
}

/// Factory to control the creation of environment instances
class _EnvironmentFactory {
  static IEnvironment create(String envConfig) {
    switch (envConfig) {
      case 'production':
        return _ProductionEnvironment();
      default:
        return _DevelopmentEnvironment();
    }
  }
}

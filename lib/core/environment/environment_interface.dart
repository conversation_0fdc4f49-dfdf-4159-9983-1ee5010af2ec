import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';

abstract interface class IEnvironment {
  String get bannerName;
  String get webUrl;
  Color get bannerColor;
  String get apiBaseUrl;
  String get unchangedVariable;
  String get supabaseUrl;
  String get supabaseAnonKey;
  String get googleMapsApiKey;
  String get countryStateCityApiKey;
  String get firebaseFunctionsUrl;
  String get googlePlacesApiKey;
  String get googleTextSearchPlaceUrl;
  String get googleAutoCompleteUrl;
  String get cloudFunctionsBaseUrl;
}

import 'package:hooks_riverpod/hooks_riverpod.dart';

// Define a state type, e.g., String
final appProvider = StateNotifierProvider<AppNotifier, String>(
  (ref) => AppNotifier(
    ref: ref,
    initialState: 'Initial Value', // Provide an initial state
  ),
);

class AppNotifier extends StateNotifier<String> {
  Ref ref; // Keep the reference if needed

  AppNotifier({
    required this.ref,
    required String initialState,
  }) : super(initialState); // Initialize the state

  // Example function to update state
  void updateState(String newState) {
    state = newState;
  }
}

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/app.dart';
import 'package:smart_team_web/i18n/strings.g.dart';

final languageProvider =
    StateNotifierProvider<LanguageNotifier, AppLocale>((ref) {
  return LanguageNotifier();
});

class LanguageNotifier extends StateNotifier<AppLocale> {
  LanguageNotifier() : super(_getInitialLocale()) {
    // Initialize slang with the initial locale
    LocaleSettings.setLocale(state);
  }

  static AppLocale _getInitialLocale() {
    // Try to get device locale, fallback to Turkish
    try {
      final deviceLocale = LocaleSettings.currentLocale;
      return deviceLocale;
    } catch (e) {
      return AppLocale.tr;
    }
  }

  void toggleLocale() {
    final newLocale = state == AppLocale.en ? AppLocale.tr : AppLocale.en;
    state = newLocale;
    LocaleSettings.setLocale(newLocale);
  }

  void setLocale(AppLocale locale) {
    state = locale;
    LocaleSettings.setLocale(locale);
  }
}

Locale get appLocale {
  assert(
    appNavigatorKey.currentContext != null,
    'Do not use appLocale without a valid context!',
  );
  return Localizations.localeOf(appNavigatorKey.currentContext!);
}

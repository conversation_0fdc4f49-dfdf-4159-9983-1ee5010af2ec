{"@@locale": "tr", "appTitle": "Akıllı Ekip Web", "@appTitle": {"description": "The title of the application"}, "start": "Başla", "termsOfServiceAndPrivacyPolicy": "<link href=\"termsOfService\">Hizmet Koşulları</link> ve <link href=\"privacyPolicy\">Gizlilik Sözleşmesi</link>", "registerAgreement": "<link>Üyelik Sözleşmesini</link>, <link>Veri Koruma ve İşletme Politikasını</link>,\n<link>Müşteri Aydınlatma Metnini</link>, <link>Gizlilik ve Çerez Politikasını</link>\nokuduğumu ve kabul ettiğimi onaylıyorum.", "minCharacterError": "<PERSON><PERSON><PERSON> sayısı en az {charLength} olabilir", "@minCharacterError": {"placeholders": {"charLength": {"type": "int"}}}, "dashboard": "Panel", "customerId": "Müşteri Id", "rules": "<PERSON><PERSON><PERSON>", "ruleList": "<PERSON><PERSON>", "title": "Başlık", "addNewRule": "<PERSON><PERSON>", "newRule": "<PERSON><PERSON>", "select": "Seçiniz...", "speedViolation": "Aşırı Hız <PERSON>ali", "stopTimeViolation": "Anlık Durma Süresi İhlali", "movementTimeViolation": "Anlık Hareket Süresi İhlali", "dailyMaxStopTimeViolation": "Günlük Maksimum Durma Süresi İhlali", "dailyMovementTimeViolation": "Günlük Hareket Süresi İhlali", "areaEntryExitViolation": "<PERSON>/<PERSON><PERSON><PERSON><PERSON><PERSON>", "suddenAcceleration": "<PERSON><PERSON>", "suddenSlowdown": "<PERSON><PERSON>", "phoneCall": "Telefon Görüşmesi", "workingTimeDelayViolation": "Mesai Zamanı Gecikme İ<PERSON>ali", "fatigueViolation": "Yorgunluk İhlali", "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customerList": "Müşteri <PERSON>esi", "customers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customerName": "Müşteri Adı", "contactName": "Kontak Adı", "deviceName": "<PERSON><PERSON><PERSON>", "gsm": "GSM", "email": "Email", "province": "İl", "district": "İlçe", "addNewCustomer": "<PERSON><PERSON>", "type": "Tip", "active": "Aktif", "potential": "Po<PERSON><PERSON><PERSON><PERSON>", "authorizedName": "Yet<PERSON><PERSON> Adı", "authorizedEmail": "<PERSON><PERSON><PERSON>", "contactGSM": "Kontak GSM", "neighborhood": "Semt", "authorizedGSM": "Yetkili GSM", "contactEmail": "Kontak Email", "location": "Lokasyon", "postalCode": "Posta Kodu", "address": "<PERSON><PERSON>", "devices": "Cihazlar", "deviceList": "<PERSON><PERSON><PERSON>", "user": "Kullanıcı", "applicationInstalled": "Uygulama <PERSON>", "brand": "<PERSON><PERSON>", "model": "Model", "version": "Versiyon", "newDevice": "<PERSON><PERSON>", "locationClosureAuthorization": "Lokasyon Kapatma İzni", "companyName": "Firma Adı", "closedPortfolio": "Kapalı Portföy", "nameSurname": "Ad Soyad", "device": "Cihaz", "group": "Grup", "calendarColor": "<PERSON><PERSON><PERSON><PERSON>", "reports": "<PERSON><PERSON><PERSON>", "calendar": "Takvim", "jobList": "İş Listesi", "forms": "Formlar", "settings": "<PERSON><PERSON><PERSON>", "userName": "Kullanıcı Adı", "password": "Şifre", "otp": "OTP", "signingIn": "<PERSON><PERSON><PERSON>lıyo<PERSON>", "enter": "<PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON>", "cancel": "İptal", "wholeTeam": "<PERSON><PERSON><PERSON>", "team": "Ekip", "exportExcel": "Excel'e Aktar", "exportPdf": "PDF'e Aktar", "status": "Durum", "teamGroup": "Ekip/Grup", "dataTime": "<PERSON><PERSON>", "headAddress": "Baş. Adres", "speed": "Hız", "drivingTot": "Sürüş Top.(km)", "movingTot": "Hareketli Top.(km)", "person": "<PERSON><PERSON><PERSON>", "animation": "Animasyon", "search": "<PERSON><PERSON>", "noEventsToday": "Bu gün i<PERSON>in et<PERSON> yok!", "instantStatus": "Anlık Durum", "instantStop": "<PERSON><PERSON><PERSON><PERSON>", "instantAct": "Anlık\nHareket", "searchNameTask": "<PERSON><PERSON> (İsim veya Görev)", "add": "<PERSON><PERSON>", "newTask": "<PERSON><PERSON>", "newUser": "<PERSON><PERSON>", "task": "<PERSON><PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "name": "İsim", "allJobs": "<PERSON><PERSON><PERSON>", "reportType": "<PERSON><PERSON>", "importantJobs": "<PERSON>ne<PERSON>li <PERSON>", "completedJobs": "<PERSON><PERSON><PERSON><PERSON>", "unfinishedJobs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "movementReport": "Hareket Raporu", "timeReport": "<PERSON><PERSON>", "startTime": "Başlangıç <PERSON>", "endTime": "Bitiş Zamanı", "apply": "<PERSON><PERSON><PERSON><PERSON>", "monday": "<PERSON><PERSON><PERSON>", "tuesday": "Salı", "wednesday": "Çarşamba", "thursday": "Perşembe", "friday": "<PERSON><PERSON>", "saturday": "<PERSON><PERSON><PERSON><PERSON>", "sunday": "Pazar", "outOfHours": "Mesai Dışı", "pick": "Seç", "selectColor": "Renk Seç", "ok": "<PERSON><PERSON>", "save": "<PERSON><PERSON>", "myProfile": "Profilim", "exit": "Çıkış", "phone": "Telefon", "changePassword": "<PERSON><PERSON><PERSON>", "oldPass": "Eski Şifre", "newPass": "<PERSON><PERSON>", "newPassAgain": "<PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON><PERSON>", "dailyMovementDistanceInKm": "Günlük Hareket Mesafesi (km)", "distance": "Mesafe", "duration": "<PERSON><PERSON><PERSON>", "maxSpeedInKm": "Ma<PERSON><PERSON>um Hız(km)", "dailyDrivingDistanceInKm": "Günlük Sürüş Mesafesi (km)", "numberDailyAct": "Günlük Aktivite Adedi", "actType": "Aktivite Türü", "eventType": "Etkinlik Türü", "contentType": "İçerik Tipi", "conclusion": "<PERSON><PERSON><PERSON>", "content": "İçerik", "contact": "Kontak", "company": "Firma", "beginning": "Başlangıç", "finish": "Bitiş", "totalViolation": "Toplam İhlal", "numberDevicesNoData": "Veri Gelmeyen Cihaz Adedi", "time": "Zaman", "reason": "Sebep", "gps": "GPS", "inAppLocationSharing": "Uygulama içi Konum Paylaşımı", "jan": "Ocak", "feb": "Ş<PERSON><PERSON>", "march": "Mart", "april": "<PERSON><PERSON>", "may": "<PERSON><PERSON><PERSON>", "june": "Haziran", "july": "Temmuz", "aug": "<PERSON><PERSON><PERSON><PERSON>", "sep": "<PERSON><PERSON><PERSON><PERSON>", "oct": "<PERSON><PERSON>", "nov": "Kasım", "dec": "Aralık", "newJob": "<PERSON><PERSON>", "showDevices": "Cihazları Göster", "customer": "Müş<PERSON>i", "priority": "Öncelik", "low": "Düşük", "mid": "Orta", "high": "<PERSON><PERSON><PERSON><PERSON>", "critical": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "fieldRequired": "<PERSON>", "enterValidId": "Lütfen geçerli bir müşteri numarası giriniz.", "enterValidEmail": "Lütfen geçerli bir email adresi giriniz.", "enterValidPass": "Lütfen geçerli bir şifre giriniz.", "enterValidOTP": "Lütfen geçerli bir OTP kodu giriniz.", "enterValidPostalCode": "Lütfen geçerli bir posta kodu giriniz.", "enterValidName": "Lütfen geçerli bir isim giri<PERSON>.", "enterValidPhone": "Lütfen geçerli bir telefon numarası giriniz.", "enterValidWebAddress": "Lütfen geçerli bir web adresi giriniz.", "enterValidIpAddress": "Lütfen geçerli bir server IP giriniz.", "enterValidServerPort": "Lütfen geçerli bir web server port giriniz.", "enterValidLatLng": "Lütfen geçerli bir LatLng formatı giriniz.", "enterValidBuildingDoor": "Lütfen geçerli bir Bina/Kapı No giriniz.", "users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "userDevice": "Kullanıcı Cihazı", "userType": "Kullanıcı Tipi", "passive": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "roleType": "Rol Tipi", "passAgain": "Şifre(Tekrar)", "generatePass": "<PERSON><PERSON><PERSON>", "noDataFound": "<PERSON><PERSON> bulunamadı", "previous": "<PERSON><PERSON><PERSON>", "page": "Say<PERSON>", "record": "kayıt", "next": "<PERSON><PERSON><PERSON>", "day": "<PERSON><PERSON><PERSON>", "week": "<PERSON><PERSON><PERSON>", "month": "Ay", "today": "<PERSON><PERSON><PERSON><PERSON>", "forgetPass": "<PERSON><PERSON><PERSON>", "waiting": "Bekliyor", "accepted": "Kabul edildi", "declined": "Reddedildi", "done": "Tamamlandı", "lastDataTime": "<PERSON>", "lastAddress": "<PERSON>", "dayStartAddress": "<PERSON><PERSON><PERSON>", "dailyDowntime": "Günlük Durma Süresi", "dailyWalkingTime": "Günlük Yürüyüş Süresi", "dailyWalkingDistance": "Günlük Yürüyüş Mesafesi (km)", "dailyDrivingTime": "Günlük Sürüş Süresi", "dailyDrivingDistance": "Günlük Sürüş Mesafesi (km)", "vehicleType": "<PERSON><PERSON>", "vehicleTotalDistance": "Araç <PERSON>lam Mesafe (km)", "lastHour": "Son {number} saat", "timeInterval": "Zaman Aralığı", "showBusinessHours": "İş saatlerini göster", "showAllDay": "<PERSON><PERSON><PERSON> gün <PERSON>", "list": "Listele", "addNew": "<PERSON><PERSON>", "customerTransactions": "Müşteri İşlemleri", "open": "Aç", "commercial": "<PERSON><PERSON><PERSON>", "individual": "<PERSON><PERSON><PERSON><PERSON>", "companyType": "Müşteri Tipi", "taxOffice": "<PERSON><PERSON><PERSON>", "taxNumber": "Vergi No", "customerPhone": "Müşteri Tel", "webAddress": "Web Adresi", "animationColors": "<PERSON><PERSON><PERSON><PERSON>", "serviceIp": "Servis <PERSON>", "servicePort": "Servis Port", "companyUpdate": "Müşteri Güncelle", "regionalTime": "Bölgesel Saat", "avenueStreet": "Cadde/Sokak", "buildingDoor": "Bina/Kapı No", "addressDirections": "<PERSON><PERSON>", "country": "<PERSON><PERSON><PERSON>", "kmh": "Km/s", "performance": "Performans", "permits": "<PERSON><PERSON><PERSON>", "costs": "Ma<PERSON>ra<PERSON><PERSON>", "selectDevice": "C<PERSON>az <PERSON>", "contracted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shortTerm": "<PERSON><PERSON><PERSON>", "fullTime": "<PERSON>", "partTime": "Yarı Zamanlı"}
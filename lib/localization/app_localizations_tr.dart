// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Turkish (`tr`).
class AppLocalizationsTr extends AppLocalizations {
  AppLocalizationsTr([String locale = 'tr']) : super(locale);

  @override
  String get appTitle => 'Akıllı Ekip Web';

  @override
  String get start => 'Başla';

  @override
  String get termsOfServiceAndPrivacyPolicy =>
      '<link href=\"termsOfService\">Hizmet Koşulları</link> ve <link href=\"privacyPolicy\">Gizlilik Sözleşmesi</link>';

  @override
  String get registerAgreement =>
      '<link>Üyelik Sözleşmesini</link>, <link>Veri Koruma ve İşletme Politikasını</link>,\n<link>Müşteri Aydınlatma Metnini</link>, <link>Gizlilik ve Çerez Politikasını</link>\nokuduğumu ve kabul ettiğimi onaylıyorum.';

  @override
  String minCharacterError(int charLength) {
    return 'Karakter sayısı en az $charLength olabilir';
  }

  @override
  String get dashboard => 'Panel';

  @override
  String get customerId => 'Müşteri Id';

  @override
  String get rules => 'Kurallar';

  @override
  String get ruleList => 'Kural Listesi';

  @override
  String get title => 'Başlık';

  @override
  String get addNewRule => 'Yeni Kural Ekle';

  @override
  String get newRule => 'Yeni Kural';

  @override
  String get select => 'Seçiniz...';

  @override
  String get speedViolation => 'Aşırı Hız İhlali';

  @override
  String get stopTimeViolation => 'Anlık Durma Süresi İhlali';

  @override
  String get movementTimeViolation => 'Anlık Hareket Süresi İhlali';

  @override
  String get dailyMaxStopTimeViolation => 'Günlük Maksimum Durma Süresi İhlali';

  @override
  String get dailyMovementTimeViolation => 'Günlük Hareket Süresi İhlali';

  @override
  String get areaEntryExitViolation => 'Alan Giriş/Çıkış İhlali';

  @override
  String get suddenAcceleration => 'Ani Hızlanma';

  @override
  String get suddenSlowdown => 'Ani Yavaşlama';

  @override
  String get phoneCall => 'Telefon Görüşmesi';

  @override
  String get workingTimeDelayViolation => 'Mesai Zamanı Gecikme İhlali';

  @override
  String get fatigueViolation => 'Yorgunluk İhlali';

  @override
  String get notifications => 'Bildirimler';

  @override
  String get customerList => 'Müşteri Listesi';

  @override
  String get customers => 'Müşteriler';

  @override
  String get customerName => 'Müşteri Adı';

  @override
  String get contactName => 'Kontak Adı';

  @override
  String get deviceName => 'Cihaz İsmi';

  @override
  String get gsm => 'GSM';

  @override
  String get email => 'Email';

  @override
  String get province => 'İl';

  @override
  String get district => 'İlçe';

  @override
  String get addNewCustomer => 'Yeni Müşteri Ekle';

  @override
  String get type => 'Tip';

  @override
  String get active => 'Aktif';

  @override
  String get potential => 'Potansiyel';

  @override
  String get authorizedName => 'Yetkili Adı';

  @override
  String get authorizedEmail => 'Yetkili Email';

  @override
  String get contactGSM => 'Kontak GSM';

  @override
  String get neighborhood => 'Semt';

  @override
  String get authorizedGSM => 'Yetkili GSM';

  @override
  String get contactEmail => 'Kontak Email';

  @override
  String get location => 'Lokasyon';

  @override
  String get postalCode => 'Posta Kodu';

  @override
  String get address => 'Adres';

  @override
  String get devices => 'Cihazlar';

  @override
  String get deviceList => 'Cihaz Listesi';

  @override
  String get user => 'Kullanıcı';

  @override
  String get applicationInstalled => 'Uygulama Yüklü';

  @override
  String get brand => 'Marka';

  @override
  String get model => 'Model';

  @override
  String get version => 'Versiyon';

  @override
  String get newDevice => 'Yeni Cihaz';

  @override
  String get locationClosureAuthorization => 'Lokasyon Kapatma İzni';

  @override
  String get companyName => 'Firma Adı';

  @override
  String get closedPortfolio => 'Kapalı Portföy';

  @override
  String get nameSurname => 'Ad Soyad';

  @override
  String get device => 'Cihaz';

  @override
  String get group => 'Grup';

  @override
  String get calendarColor => 'Takvim Rengi';

  @override
  String get reports => 'Raporlar';

  @override
  String get calendar => 'Takvim';

  @override
  String get jobList => 'İş Listesi';

  @override
  String get forms => 'Formlar';

  @override
  String get settings => 'Ayarlar';

  @override
  String get userName => 'Kullanıcı Adı';

  @override
  String get password => 'Şifre';

  @override
  String get otp => 'OTP';

  @override
  String get signingIn => 'Giriş Yapılıyor';

  @override
  String get enter => 'Giriş';

  @override
  String get error => 'Hata';

  @override
  String get cancel => 'İptal';

  @override
  String get wholeTeam => 'Tüm Ekip';

  @override
  String get team => 'Ekip';

  @override
  String get exportExcel => 'Excel\'e Aktar';

  @override
  String get exportPdf => 'PDF\'e Aktar';

  @override
  String get status => 'Durum';

  @override
  String get teamGroup => 'Ekip/Grup';

  @override
  String get dataTime => 'Veri Zamanı';

  @override
  String get headAddress => 'Baş. Adres';

  @override
  String get speed => 'Hız';

  @override
  String get drivingTot => 'Sürüş Top.(km)';

  @override
  String get movingTot => 'Hareketli Top.(km)';

  @override
  String get person => 'Kişi';

  @override
  String get animation => 'Animasyon';

  @override
  String get search => 'Arama';

  @override
  String get noEventsToday => 'Bu gün için etkinlik yok!';

  @override
  String get instantStatus => 'Anlık Durum';

  @override
  String get instantStop => 'Anlık\nDurma';

  @override
  String get instantAct => 'Anlık\nHareket';

  @override
  String get searchNameTask => 'Arama (İsim veya Görev)';

  @override
  String get add => 'Ekle';

  @override
  String get newTask => 'Yeni Görev';

  @override
  String get newUser => 'Yeni Kullanıcı';

  @override
  String get task => 'Görev';

  @override
  String get date => 'Tarih';

  @override
  String get name => 'İsim';

  @override
  String get allJobs => 'Tüm İşler';

  @override
  String get reportType => 'Rapor Türü';

  @override
  String get importantJobs => 'Önemli İşler';

  @override
  String get completedJobs => 'Tamamlanan İşler';

  @override
  String get unfinishedJobs => 'Tamamlanmayanan İşler';

  @override
  String get movementReport => 'Hareket Raporu';

  @override
  String get timeReport => 'Mesai Raporu';

  @override
  String get startTime => 'Başlangıç Zamanı';

  @override
  String get endTime => 'Bitiş Zamanı';

  @override
  String get apply => 'Uygula';

  @override
  String get monday => 'Pazartesi';

  @override
  String get tuesday => 'Salı';

  @override
  String get wednesday => 'Çarşamba';

  @override
  String get thursday => 'Perşembe';

  @override
  String get friday => 'Cuma';

  @override
  String get saturday => 'Cumartesi';

  @override
  String get sunday => 'Pazar';

  @override
  String get outOfHours => 'Mesai Dışı';

  @override
  String get pick => 'Seç';

  @override
  String get selectColor => 'Renk Seç';

  @override
  String get ok => 'Tamam';

  @override
  String get save => 'Kaydet';

  @override
  String get myProfile => 'Profilim';

  @override
  String get exit => 'Çıkış';

  @override
  String get phone => 'Telefon';

  @override
  String get changePassword => 'Parola Değiştir';

  @override
  String get oldPass => 'Eski Şifre';

  @override
  String get newPass => 'Yeni Şifre';

  @override
  String get newPassAgain => 'Yeni Şifre Tekrar';

  @override
  String get required => 'Gerekli';

  @override
  String get dailyMovementDistanceInKm => 'Günlük Hareket Mesafesi (km)';

  @override
  String get distance => 'Mesafe';

  @override
  String get duration => 'Süre';

  @override
  String get maxSpeedInKm => 'Maksimum Hız(km)';

  @override
  String get dailyDrivingDistanceInKm => 'Günlük Sürüş Mesafesi (km)';

  @override
  String get numberDailyAct => 'Günlük Aktivite Adedi';

  @override
  String get actType => 'Aktivite Türü';

  @override
  String get eventType => 'Etkinlik Türü';

  @override
  String get contentType => 'İçerik Tipi';

  @override
  String get conclusion => 'Sonuç';

  @override
  String get content => 'İçerik';

  @override
  String get contact => 'Kontak';

  @override
  String get company => 'Firma';

  @override
  String get beginning => 'Başlangıç';

  @override
  String get finish => 'Bitiş';

  @override
  String get totalViolation => 'Toplam İhlal';

  @override
  String get numberDevicesNoData => 'Veri Gelmeyen Cihaz Adedi';

  @override
  String get time => 'Zaman';

  @override
  String get reason => 'Sebep';

  @override
  String get gps => 'GPS';

  @override
  String get inAppLocationSharing => 'Uygulama içi Konum Paylaşımı';

  @override
  String get jan => 'Ocak';

  @override
  String get feb => 'Şubat';

  @override
  String get march => 'Mart';

  @override
  String get april => 'Nisan';

  @override
  String get may => 'Mayıs';

  @override
  String get june => 'Haziran';

  @override
  String get july => 'Temmuz';

  @override
  String get aug => 'Ağustos';

  @override
  String get sep => 'Eylül';

  @override
  String get oct => 'Ekim';

  @override
  String get nov => 'Kasım';

  @override
  String get dec => 'Aralık';

  @override
  String get newJob => 'Yeni İş';

  @override
  String get showDevices => 'Cihazları Göster';

  @override
  String get customer => 'Müşteri';

  @override
  String get priority => 'Öncelik';

  @override
  String get low => 'Düşük';

  @override
  String get mid => 'Orta';

  @override
  String get high => 'Yüksek';

  @override
  String get critical => 'Kritik';

  @override
  String get description => 'Açıklama';

  @override
  String get fieldRequired => 'Alan Gereklidir';

  @override
  String get enterValidId => 'Lütfen geçerli bir müşteri numarası giriniz.';

  @override
  String get enterValidEmail => 'Lütfen geçerli bir email adresi giriniz.';

  @override
  String get enterValidPass => 'Lütfen geçerli bir şifre giriniz.';

  @override
  String get enterValidOTP => 'Lütfen geçerli bir OTP kodu giriniz.';

  @override
  String get enterValidPostalCode => 'Lütfen geçerli bir posta kodu giriniz.';

  @override
  String get enterValidName => 'Lütfen geçerli bir isim giriniz.';

  @override
  String get enterValidPhone => 'Lütfen geçerli bir telefon numarası giriniz.';

  @override
  String get enterValidWebAddress => 'Lütfen geçerli bir web adresi giriniz.';

  @override
  String get enterValidIpAddress => 'Lütfen geçerli bir server IP giriniz.';

  @override
  String get enterValidServerPort =>
      'Lütfen geçerli bir web server port giriniz.';

  @override
  String get enterValidLatLng => 'Lütfen geçerli bir LatLng formatı giriniz.';

  @override
  String get enterValidBuildingDoor =>
      'Lütfen geçerli bir Bina/Kapı No giriniz.';

  @override
  String get users => 'Kullanıcılar';

  @override
  String get userDevice => 'Kullanıcı Cihazı';

  @override
  String get userType => 'Kullanıcı Tipi';

  @override
  String get passive => 'Pasif';

  @override
  String get edit => 'Düzenle';

  @override
  String get roleType => 'Rol Tipi';

  @override
  String get passAgain => 'Şifre(Tekrar)';

  @override
  String get generatePass => 'Şifre Üret';

  @override
  String get noDataFound => 'Veri bulunamadı';

  @override
  String get previous => 'Önceki';

  @override
  String get page => 'Sayfa';

  @override
  String get record => 'kayıt';

  @override
  String get next => 'Sonraki';

  @override
  String get day => 'Gün';

  @override
  String get week => 'Hafta';

  @override
  String get month => 'Ay';

  @override
  String get today => 'Bugün';

  @override
  String get forgetPass => 'Şifre unuttum';

  @override
  String get waiting => 'Bekliyor';

  @override
  String get accepted => 'Kabul edildi';

  @override
  String get declined => 'Reddedildi';

  @override
  String get done => 'Tamamlandı';

  @override
  String get lastDataTime => 'Son Veri Zamanı';

  @override
  String get lastAddress => 'Son Adres';

  @override
  String get dayStartAddress => 'Gün Başlama Adresi';

  @override
  String get dailyDowntime => 'Günlük Durma Süresi';

  @override
  String get dailyWalkingTime => 'Günlük Yürüyüş Süresi';

  @override
  String get dailyWalkingDistance => 'Günlük Yürüyüş Mesafesi (km)';

  @override
  String get dailyDrivingTime => 'Günlük Sürüş Süresi';

  @override
  String get dailyDrivingDistance => 'Günlük Sürüş Mesafesi (km)';

  @override
  String get vehicleType => 'Araç Tipi';

  @override
  String get vehicleTotalDistance => 'Araç Toplam Mesafe (km)';

  @override
  String lastHour(Object number) {
    return 'Son $number saat';
  }

  @override
  String get timeInterval => 'Zaman Aralığı';

  @override
  String get showBusinessHours => 'İş saatlerini göster';

  @override
  String get showAllDay => 'Tüm gün göster';

  @override
  String get list => 'Listele';

  @override
  String get addNew => 'Yeni Ekle';

  @override
  String get customerTransactions => 'Müşteri İşlemleri';

  @override
  String get open => 'Aç';

  @override
  String get commercial => 'Ticari';

  @override
  String get individual => 'Bireysel';

  @override
  String get companyType => 'Müşteri Tipi';

  @override
  String get taxOffice => 'Vergi Dairesi';

  @override
  String get taxNumber => 'Vergi No';

  @override
  String get customerPhone => 'Müşteri Tel';

  @override
  String get webAddress => 'Web Adresi';

  @override
  String get animationColors => 'Animasyon Renkleri';

  @override
  String get serviceIp => 'Servis IP';

  @override
  String get servicePort => 'Servis Port';

  @override
  String get companyUpdate => 'Müşteri Güncelle';

  @override
  String get regionalTime => 'Bölgesel Saat';

  @override
  String get avenueStreet => 'Cadde/Sokak';

  @override
  String get buildingDoor => 'Bina/Kapı No';

  @override
  String get addressDirections => 'Adres Tarifi';

  @override
  String get country => 'Ülke';

  @override
  String get kmh => 'Km/s';

  @override
  String get performance => 'Performans';

  @override
  String get permits => 'İzinler';

  @override
  String get costs => 'Masraflar';

  @override
  String get selectDevice => 'Cihaz Seç';

  @override
  String get contracted => 'Sözleşmeli';

  @override
  String get shortTerm => 'Kısa Dönem';

  @override
  String get fullTime => 'Tam Zamanlı';

  @override
  String get partTime => 'Yarı Zamanlı';
}

{"@@locale": "en", "appTitle": "Smart Team Web", "@appTitle": {"description": "The title of the application"}, "start": "Get Started", "termsOfServiceAndPrivacyPolicy": "<link href=\"termsOfService\">Terms of Service</link> and <link href=\"privacyPolicy\">Privacy Policy</link>", "registerAgreement": "I confirm that I have read and accepted the <link>Membership Agreement</link>, <link>Data Protection and Business Policy</link>, <link>Customer Clarification Text</link>, <link>Privacy and Cookie Policy</link>.", "minCharacterError": "The number of characters can be at least {charLength}", "@minCharacterError": {"placeholders": {"charLength": {"type": "int"}}}, "dashboard": "Dashboard", "customerId": "Customer Id", "rules": "Rules", "ruleList": "Rule List", "title": "Title", "addNewRule": "Add New Rule", "newRule": "New Rule", "select": "Select...", "speedViolation": "Excessive Speed Violation", "stopTimeViolation": "Instant Stop Time Violation", "movementTimeViolation": "Instantaneous Movement Time Violation", "dailyMaxStopTimeViolation": "Daily Maximum Stopping Time Violation", "dailyMovementTimeViolation": "Daily Movement Time Violation", "areaEntryExitViolation": "Area Entry/Exit Violation", "suddenAcceleration": "Sudden Acceleration", "suddenSlowdown": "Sudden Slowdown", "phoneCall": "Phone Call", "workingTimeDelayViolation": "Working Time Delay Violation", "fatigueViolation": "Fatigue Violation", "notifications": "Notifications", "customerList": "Customer List", "customers": "Customers", "customerName": "Customer Name", "contactName": "Contact Name", "deviceName": "Device Name", "gsm": "GSM", "email": "Email", "province": "Province", "district": "District", "addNewCustomer": "Add New Customer", "type": "Type", "active": "Active", "potential": "Potential", "authorizedName": "Authorized Name", "authorizedEmail": "Authorized Email", "contactGSM": "Contact GSM", "neighborhood": "Neighborhood", "authorizedGSM": "Authorized GSM", "contactEmail": "Contact Email", "location": "Location", "postalCode": "Postal Code", "address": "Address", "devices": "Devices", "deviceList": "Device List", "user": "User", "applicationInstalled": "Application Installed", "brand": "Brand", "model": "Model", "version": "Version", "newDevice": "New Device", "locationClosureAuthorization": "Location Closure Authorization", "companyName": "Company Name", "closedPortfolio": "Closed Portfolio", "nameSurname": "Name Surname", "device": "<PERSON><PERSON>", "group": "Group", "calendarColor": "Calendar Color", "reports": "Reports", "calendar": "Calendar", "jobList": "Job List", "forms": "Forms", "settings": "Settings", "userName": "User Name", "password": "Password", "otp": "OTP", "signingIn": "Signing In", "enter": "Enter", "error": "Error", "cancel": "Cancel", "wholeTeam": "Whole Team", "team": "Team", "exportExcel": "Export to Excel", "exportPdf": "Export to PDF", "status": "Status", "teamGroup": "Team/Group", "dataTime": "Data Time", "headAddress": "Head Address", "speed": "Speed", "drivingTot": "Driving Tot.(km)", "movingTot": "Moving Tot.(km)", "person": "Person", "animation": "Animation", "search": "Search", "noEventsToday": "There are no events for this day!", "instantStatus": "Instant Status", "instantStop": "Instant\nStop", "instantAct": "Instant\nAction", "searchNameTask": "Search (Name or Task)", "add": "Add", "newTask": "New Task", "newUser": "New User", "task": "Task", "date": "Date", "name": "Name", "allJobs": "All Jobs", "reportType": "Report Type", "importantJobs": "Important Jobs", "completedJobs": "Completed Jobs", "unfinishedJobs": "Unfinished Business", "movementReport": "Movement Report", "timeReport": "Time Report", "startTime": "Start Time", "endTime": "End Time", "apply": "Apply", "monday": "monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "outOfHours": "Out of Hours", "pick": "Select", "selectColor": "Select Color", "ok": "Okay", "save": "Save", "myProfile": "My Profile", "exit": "Exit", "phone": "Phone", "changePassword": "Change Password", "oldPass": "Old Password", "newPass": "New Password", "newPassAgain": "New Password Again", "required": "Required", "dailyMovementDistanceInKm": "Daily Movement Distance (km)", "distance": "Distance", "duration": "Duration", "maxSpeedInKm": "Maximum Speed(km)", "dailyDrivingDistanceInKm": "Daily Driving Distance (km)", "numberDailyAct": "Number of Daily Activities", "actType": "Activity Type", "eventType": "Event Type", "contentType": "Content Type", "conclusion": "Conclusion", "content": "Content", "contact": "Contact", "company": "Company", "beginning": "Beginning", "finish": "End", "totalViolation": "Total Violation", "numberDevicesNoData": "Number of Devices with No Data", "time": "Time", "reason": "Reason", "gps": "GPS", "inAppLocationSharing": "In-app Location Sharing", "jan": "January", "feb": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "aug": "August", "sep": "September", "oct": "October", "nov": "November", "dec": "December", "newJob": "New Job", "showDevices": "Show Devices", "customer": "Customer", "priority": "Priority", "low": "Low", "mid": "Middle", "high": "High", "critical": "Critical", "description": "Description", "fieldRequired": "Field Required", "enterValidId": "Please enter a valid customer number.", "enterValidEmail": "Please enter a valid email address.", "enterValidPass": "Please enter a valid password.", "enterValidOTP": "Please enter a valid OTP code.", "enterValidPostalCode": "Please enter a valid postal code.", "enterValidName": "Please enter a valid name.", "enterValidPhone": "Please enter a valid phone number.", "enterValidWebAddress": "Please enter a valid web address.", "enterValidIpAddress": "Please enter a valid server IP.", "enterValidServerPort": "Please enter a valid server port.", "enterValidLatLng": "Please enter a valid lat. long. format.", "enterValidBuildingDoor": "Please enter a valid building door no.", "users": "Users", "userDevice": "User Device", "userType": "User Type", "passive": "Passive", "edit": "Edit", "roleType": "Role Type", "passAgain": "Password(Again)", "generatePass": "Generate Password", "noDataFound": "No data found", "previous": "Previous", "page": "Page", "record": "record", "next": "Next", "day": "Day", "week": "Week", "month": "Month", "today": "Today", "forgetPass": "Forget Password", "waiting": "Waiting", "accepted": "Accepted", "declined": "Declined", "done": "Done", "lastDataTime": "Last Data Time", "lastAddress": "Last Address", "dayStartAddress": "Day Start Address", "dailyDowntime": "Daily Downtime", "dailyWalkingTime": "Daily Walking Time", "dailyWalkingDistance": "Daily Walking Distance (km)", "dailyDrivingTime": "Daily Driving Time", "dailyDrivingDistance": "Daily Driving Distance (km)", "vehicleType": "Vehicle Type", "vehicleTotalDistance": "Vehicle Total Distance (km)", "lastHour": "Last {number} hour", "timeInterval": "Time Interval", "showBusinessHours": "Show business hours", "showAllDay": "Show all day", "list": "List", "addNew": "Add New", "customerTransactions": "Customer Transactions", "open": "Open", "commercial": "Commercial", "individual": "Individual", "companyType": "Company Type", "taxOffice": "Tax Office", "taxNumber": "Tax Number", "customerPhone": "Customer Phone", "webAddress": "Web Address", "animationColors": "Animation Colors", "serviceIp": "Service IP", "servicePort": "Service Port", "companyUpdate": "Company Update", "regionalTime": "Regional Time", "avenueStreet": "Avenue/Street", "buildingDoor": "Building/Door No", "addressDirections": "Address Directions", "country": "Country", "kmh": "Km/h", "performance": "Performance", "permits": "Permits", "costs": "Costs", "selectDevice": "Select Device", "contracted": "Contracted", "shortTerm": "Short Term", "fullTime": "Full Time", "partTime": "Part Time"}
// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Smart Team Web';

  @override
  String get start => 'Get Started';

  @override
  String get termsOfServiceAndPrivacyPolicy =>
      '<link href=\"termsOfService\">Terms of Service</link> and <link href=\"privacyPolicy\">Privacy Policy</link>';

  @override
  String get registerAgreement =>
      'I confirm that I have read and accepted the <link>Membership Agreement</link>, <link>Data Protection and Business Policy</link>, <link>Customer Clarification Text</link>, <link>Privacy and Cookie Policy</link>.';

  @override
  String minCharacterError(int charLength) {
    return 'The number of characters can be at least $charLength';
  }

  @override
  String get dashboard => 'Dashboard';

  @override
  String get customerId => 'Customer Id';

  @override
  String get rules => 'Rules';

  @override
  String get ruleList => 'Rule List';

  @override
  String get title => 'Title';

  @override
  String get addNewRule => 'Add New Rule';

  @override
  String get newRule => 'New Rule';

  @override
  String get select => 'Select...';

  @override
  String get speedViolation => 'Excessive Speed Violation';

  @override
  String get stopTimeViolation => 'Instant Stop Time Violation';

  @override
  String get movementTimeViolation => 'Instantaneous Movement Time Violation';

  @override
  String get dailyMaxStopTimeViolation =>
      'Daily Maximum Stopping Time Violation';

  @override
  String get dailyMovementTimeViolation => 'Daily Movement Time Violation';

  @override
  String get areaEntryExitViolation => 'Area Entry/Exit Violation';

  @override
  String get suddenAcceleration => 'Sudden Acceleration';

  @override
  String get suddenSlowdown => 'Sudden Slowdown';

  @override
  String get phoneCall => 'Phone Call';

  @override
  String get workingTimeDelayViolation => 'Working Time Delay Violation';

  @override
  String get fatigueViolation => 'Fatigue Violation';

  @override
  String get notifications => 'Notifications';

  @override
  String get customerList => 'Customer List';

  @override
  String get customers => 'Customers';

  @override
  String get customerName => 'Customer Name';

  @override
  String get contactName => 'Contact Name';

  @override
  String get deviceName => 'Device Name';

  @override
  String get gsm => 'GSM';

  @override
  String get email => 'Email';

  @override
  String get province => 'Province';

  @override
  String get district => 'District';

  @override
  String get addNewCustomer => 'Add New Customer';

  @override
  String get type => 'Type';

  @override
  String get active => 'Active';

  @override
  String get potential => 'Potential';

  @override
  String get authorizedName => 'Authorized Name';

  @override
  String get authorizedEmail => 'Authorized Email';

  @override
  String get contactGSM => 'Contact GSM';

  @override
  String get neighborhood => 'Neighborhood';

  @override
  String get authorizedGSM => 'Authorized GSM';

  @override
  String get contactEmail => 'Contact Email';

  @override
  String get location => 'Location';

  @override
  String get postalCode => 'Postal Code';

  @override
  String get address => 'Address';

  @override
  String get devices => 'Devices';

  @override
  String get deviceList => 'Device List';

  @override
  String get user => 'User';

  @override
  String get applicationInstalled => 'Application Installed';

  @override
  String get brand => 'Brand';

  @override
  String get model => 'Model';

  @override
  String get version => 'Version';

  @override
  String get newDevice => 'New Device';

  @override
  String get locationClosureAuthorization => 'Location Closure Authorization';

  @override
  String get companyName => 'Company Name';

  @override
  String get closedPortfolio => 'Closed Portfolio';

  @override
  String get nameSurname => 'Name Surname';

  @override
  String get device => 'Device';

  @override
  String get group => 'Group';

  @override
  String get calendarColor => 'Calendar Color';

  @override
  String get reports => 'Reports';

  @override
  String get calendar => 'Calendar';

  @override
  String get jobList => 'Job List';

  @override
  String get forms => 'Forms';

  @override
  String get settings => 'Settings';

  @override
  String get userName => 'User Name';

  @override
  String get password => 'Password';

  @override
  String get otp => 'OTP';

  @override
  String get signingIn => 'Signing In';

  @override
  String get enter => 'Enter';

  @override
  String get error => 'Error';

  @override
  String get cancel => 'Cancel';

  @override
  String get wholeTeam => 'Whole Team';

  @override
  String get team => 'Team';

  @override
  String get exportExcel => 'Export to Excel';

  @override
  String get exportPdf => 'Export to PDF';

  @override
  String get status => 'Status';

  @override
  String get teamGroup => 'Team/Group';

  @override
  String get dataTime => 'Data Time';

  @override
  String get headAddress => 'Head Address';

  @override
  String get speed => 'Speed';

  @override
  String get drivingTot => 'Driving Tot.(km)';

  @override
  String get movingTot => 'Moving Tot.(km)';

  @override
  String get person => 'Person';

  @override
  String get animation => 'Animation';

  @override
  String get search => 'Search';

  @override
  String get noEventsToday => 'There are no events for this day!';

  @override
  String get instantStatus => 'Instant Status';

  @override
  String get instantStop => 'Instant\nStop';

  @override
  String get instantAct => 'Instant\nAction';

  @override
  String get searchNameTask => 'Search (Name or Task)';

  @override
  String get add => 'Add';

  @override
  String get newTask => 'New Task';

  @override
  String get newUser => 'New User';

  @override
  String get task => 'Task';

  @override
  String get date => 'Date';

  @override
  String get name => 'Name';

  @override
  String get allJobs => 'All Jobs';

  @override
  String get reportType => 'Report Type';

  @override
  String get importantJobs => 'Important Jobs';

  @override
  String get completedJobs => 'Completed Jobs';

  @override
  String get unfinishedJobs => 'Unfinished Business';

  @override
  String get movementReport => 'Movement Report';

  @override
  String get timeReport => 'Time Report';

  @override
  String get startTime => 'Start Time';

  @override
  String get endTime => 'End Time';

  @override
  String get apply => 'Apply';

  @override
  String get monday => 'monday';

  @override
  String get tuesday => 'Tuesday';

  @override
  String get wednesday => 'Wednesday';

  @override
  String get thursday => 'Thursday';

  @override
  String get friday => 'Friday';

  @override
  String get saturday => 'Saturday';

  @override
  String get sunday => 'Sunday';

  @override
  String get outOfHours => 'Out of Hours';

  @override
  String get pick => 'Select';

  @override
  String get selectColor => 'Select Color';

  @override
  String get ok => 'Okay';

  @override
  String get save => 'Save';

  @override
  String get myProfile => 'My Profile';

  @override
  String get exit => 'Exit';

  @override
  String get phone => 'Phone';

  @override
  String get changePassword => 'Change Password';

  @override
  String get oldPass => 'Old Password';

  @override
  String get newPass => 'New Password';

  @override
  String get newPassAgain => 'New Password Again';

  @override
  String get required => 'Required';

  @override
  String get dailyMovementDistanceInKm => 'Daily Movement Distance (km)';

  @override
  String get distance => 'Distance';

  @override
  String get duration => 'Duration';

  @override
  String get maxSpeedInKm => 'Maximum Speed(km)';

  @override
  String get dailyDrivingDistanceInKm => 'Daily Driving Distance (km)';

  @override
  String get numberDailyAct => 'Number of Daily Activities';

  @override
  String get actType => 'Activity Type';

  @override
  String get eventType => 'Event Type';

  @override
  String get contentType => 'Content Type';

  @override
  String get conclusion => 'Conclusion';

  @override
  String get content => 'Content';

  @override
  String get contact => 'Contact';

  @override
  String get company => 'Company';

  @override
  String get beginning => 'Beginning';

  @override
  String get finish => 'End';

  @override
  String get totalViolation => 'Total Violation';

  @override
  String get numberDevicesNoData => 'Number of Devices with No Data';

  @override
  String get time => 'Time';

  @override
  String get reason => 'Reason';

  @override
  String get gps => 'GPS';

  @override
  String get inAppLocationSharing => 'In-app Location Sharing';

  @override
  String get jan => 'January';

  @override
  String get feb => 'February';

  @override
  String get march => 'March';

  @override
  String get april => 'April';

  @override
  String get may => 'May';

  @override
  String get june => 'June';

  @override
  String get july => 'July';

  @override
  String get aug => 'August';

  @override
  String get sep => 'September';

  @override
  String get oct => 'October';

  @override
  String get nov => 'November';

  @override
  String get dec => 'December';

  @override
  String get newJob => 'New Job';

  @override
  String get showDevices => 'Show Devices';

  @override
  String get customer => 'Customer';

  @override
  String get priority => 'Priority';

  @override
  String get low => 'Low';

  @override
  String get mid => 'Middle';

  @override
  String get high => 'High';

  @override
  String get critical => 'Critical';

  @override
  String get description => 'Description';

  @override
  String get fieldRequired => 'Field Required';

  @override
  String get enterValidId => 'Please enter a valid customer number.';

  @override
  String get enterValidEmail => 'Please enter a valid email address.';

  @override
  String get enterValidPass => 'Please enter a valid password.';

  @override
  String get enterValidOTP => 'Please enter a valid OTP code.';

  @override
  String get enterValidPostalCode => 'Please enter a valid postal code.';

  @override
  String get enterValidName => 'Please enter a valid name.';

  @override
  String get enterValidPhone => 'Please enter a valid phone number.';

  @override
  String get enterValidWebAddress => 'Please enter a valid web address.';

  @override
  String get enterValidIpAddress => 'Please enter a valid server IP.';

  @override
  String get enterValidServerPort => 'Please enter a valid server port.';

  @override
  String get enterValidLatLng => 'Please enter a valid lat. long. format.';

  @override
  String get enterValidBuildingDoor => 'Please enter a valid building door no.';

  @override
  String get users => 'Users';

  @override
  String get userDevice => 'User Device';

  @override
  String get userType => 'User Type';

  @override
  String get passive => 'Passive';

  @override
  String get edit => 'Edit';

  @override
  String get roleType => 'Role Type';

  @override
  String get passAgain => 'Password(Again)';

  @override
  String get generatePass => 'Generate Password';

  @override
  String get noDataFound => 'No data found';

  @override
  String get previous => 'Previous';

  @override
  String get page => 'Page';

  @override
  String get record => 'record';

  @override
  String get next => 'Next';

  @override
  String get day => 'Day';

  @override
  String get week => 'Week';

  @override
  String get month => 'Month';

  @override
  String get today => 'Today';

  @override
  String get forgetPass => 'Forget Password';

  @override
  String get waiting => 'Waiting';

  @override
  String get accepted => 'Accepted';

  @override
  String get declined => 'Declined';

  @override
  String get done => 'Done';

  @override
  String get lastDataTime => 'Last Data Time';

  @override
  String get lastAddress => 'Last Address';

  @override
  String get dayStartAddress => 'Day Start Address';

  @override
  String get dailyDowntime => 'Daily Downtime';

  @override
  String get dailyWalkingTime => 'Daily Walking Time';

  @override
  String get dailyWalkingDistance => 'Daily Walking Distance (km)';

  @override
  String get dailyDrivingTime => 'Daily Driving Time';

  @override
  String get dailyDrivingDistance => 'Daily Driving Distance (km)';

  @override
  String get vehicleType => 'Vehicle Type';

  @override
  String get vehicleTotalDistance => 'Vehicle Total Distance (km)';

  @override
  String lastHour(Object number) {
    return 'Last $number hour';
  }

  @override
  String get timeInterval => 'Time Interval';

  @override
  String get showBusinessHours => 'Show business hours';

  @override
  String get showAllDay => 'Show all day';

  @override
  String get list => 'List';

  @override
  String get addNew => 'Add New';

  @override
  String get customerTransactions => 'Customer Transactions';

  @override
  String get open => 'Open';

  @override
  String get commercial => 'Commercial';

  @override
  String get individual => 'Individual';

  @override
  String get companyType => 'Company Type';

  @override
  String get taxOffice => 'Tax Office';

  @override
  String get taxNumber => 'Tax Number';

  @override
  String get customerPhone => 'Customer Phone';

  @override
  String get webAddress => 'Web Address';

  @override
  String get animationColors => 'Animation Colors';

  @override
  String get serviceIp => 'Service IP';

  @override
  String get servicePort => 'Service Port';

  @override
  String get companyUpdate => 'Company Update';

  @override
  String get regionalTime => 'Regional Time';

  @override
  String get avenueStreet => 'Avenue/Street';

  @override
  String get buildingDoor => 'Building/Door No';

  @override
  String get addressDirections => 'Address Directions';

  @override
  String get country => 'Country';

  @override
  String get kmh => 'Km/h';

  @override
  String get performance => 'Performance';

  @override
  String get permits => 'Permits';

  @override
  String get costs => 'Costs';

  @override
  String get selectDevice => 'Select Device';

  @override
  String get contracted => 'Contracted';

  @override
  String get shortTerm => 'Short Term';

  @override
  String get fullTime => 'Full Time';

  @override
  String get partTime => 'Part Time';
}

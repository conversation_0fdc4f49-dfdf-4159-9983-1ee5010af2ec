part of '../permits_screen_view.dart';

class PermitsCalendarMonthView extends HookConsumerWidget {
  const PermitsCalendarMonthView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDay = ref.watch(permitsCalendarSelectedDayProvider);
    final month = DateTime(selectedDay.year, selectedDay.month);
    final daysInMonth = DateTime(month.year, month.month + 1, 0).day;
    final permits = ref.watch(permitsForCalendarViewProvider(month));

    final employees = ref.watch(paginatedEmployeesProvider);
    final currentPage = ref.watch(currentPageProvider);
    final totalCount = ref.watch(totalEmployeesCountProvider);
    final totalPages = ref.watch(totalPagesProvider);

    const double empColW = 180;
    const double borderW = 1;

    void goFirst() => ref.read(currentPageProvider.notifier).state = 1;
    void goPrev() =>
        ref.read(currentPageProvider.notifier).state = currentPage - 1;
    void goNext() =>
        ref.read(currentPageProvider.notifier).state = currentPage + 1;
    void goLast() => ref.read(currentPageProvider.notifier).state = totalPages;

    final calendarGrid = LayoutBuilder(
      builder: (context, constraints) {
        final availableW = math.max(0, constraints.maxWidth - empColW);
        final dayInner = (availableW - borderW * daysInMonth) / daysInMonth;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _Header(
              month: month,
              daysInMonth: daysInMonth,
              empWidth: empColW,
              dayInner: dayInner,
              borderW: borderW,
            ),
            Expanded(
              child: SingleChildScrollView(
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: employees.length,
                  itemBuilder: (ctx, idx) {
                    final employee = employees[idx];
                    final employeePermits = permits
                        .where((p) => p.employeeName == employee)
                        .toList();

                    return _buildMonthPermitRow(
                      employeeName: employee,
                      employeePermits: employeePermits,
                      month: month,
                      daysInMonth: daysInMonth,
                      empColW: empColW,
                      dayInner: dayInner,
                      borderW: borderW,
                      showBottomBorder: idx != employees.length - 1,
                    );
                  },
                ),
              ),
            ),
          ],
        );
      },
    );

    return Column(
      children: [
        Expanded(child: calendarGrid),
        BottomNavigationButtons(
          onFirst: currentPage > 1 ? goFirst : null,
          onPrevious: goPrev,
          onNext: goNext,
          onLast: currentPage < totalPages ? goLast : null,
          currentPage: currentPage,
          totalPages: totalPages,
          totalCount: totalCount,
        ),
      ],
    );
  }

  Widget _buildMonthPermitRow({
    required String employeeName,
    required List<Permit> employeePermits,
    required DateTime month,
    required int daysInMonth,
    required double empColW,
    required double dayInner,
    required double borderW,
    required bool showBottomBorder,
  }) {
    final bottomBorder = showBottomBorder
        ? BorderSide(color: Colors.grey.shade300)
        : BorderSide.none;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: empColW,
          height: 36,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            border: Border(
              bottom: bottomBorder,
              right: BorderSide(color: Colors.grey.shade300, width: borderW),
            ),
          ),
          child: TextWidget(
            employeeName,
            style: ATextStyle.semiSmall,
          ),
        ),
        Expanded(
          child: Row(
            children: List.generate(daysInMonth, (i) {
              final today = DateTime(month.year, month.month, i + 1);
              final nextDay = today.add(const Duration(days: 1));

              final isApproved = employeePermits.any(
                (p) =>
                    p.status == PermitStatus.approved &&
                    !today.isBefore(p.startDate) &&
                    !today.isAfter(p.endDate),
              );

              final nextApproved = employeePermits.any(
                (p) =>
                    p.status == PermitStatus.approved &&
                    !nextDay.isBefore(p.startDate) &&
                    !nextDay.isAfter(p.endDate),
              );

              return Container(
                width: dayInner,
                height: 36,
                decoration: BoxDecoration(
                  color: isApproved
                      ? AColor.primaryColor.withValues(alpha: 0.6)
                      : AColor.transparent,
                  border: Border(
                    bottom: bottomBorder,
                    right: BorderSide(
                      color: (isApproved && nextApproved)
                          ? AColor.transparent
                          : Colors.grey.shade300,
                      width: borderW,
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
      ],
    );
  }
}

class _Header extends StatelessWidget {
  const _Header({
    required this.month,
    required this.daysInMonth,
    required this.empWidth,
    required this.dayInner,
    required this.borderW,
  });

  final DateTime month;
  final int daysInMonth;
  final double empWidth;
  final double dayInner;
  final double borderW;

  @override
  Widget build(BuildContext context) {
    const double monthRowH = 28;
    const double dayRowH = 36;

    return Row(
      children: [
        Container(
          width: empWidth,
          height: monthRowH + dayRowH,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Colors.grey.shade300),
              right: BorderSide(color: Colors.grey.shade300, width: borderW),
            ),
          ),
          child: TextWidget(
            'Personeller'.hardcoded,
            style: ATextStyle.text12SemiBold,
          ),
        ),
        Expanded(
          child: Column(
            children: [
              Container(
                height: monthRowH,
                alignment: Alignment.center,
                child: TextWidget(
                  '${monthName(context, month.month)} ${month.year}',
                  style: ATextStyle.mediumRegular,
                ),
              ),
              Align(
                alignment: Alignment.centerLeft,
                child: Container(
                  height: dayRowH,
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Colors.grey.shade300),
                      bottom: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: List.generate(daysInMonth, (i) {
                      return Container(
                        width: dayInner,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(
                              color: Colors.grey.shade300,
                              width: borderW,
                            ),
                          ),
                        ),
                        child: TextWidget(
                          (i + 1).toString().padLeft(2, '0'),
                          style: ATextStyle.small,
                        ),
                      );
                    }),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

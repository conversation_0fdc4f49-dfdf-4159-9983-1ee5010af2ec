part of '../permits_screen_view.dart';

class PermitsCalendarDayView extends HookConsumerWidget {
  const PermitsCalendarDayView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDay = ref.watch(permitsCalendarSelectedDayProvider);
    final monday = selectedDay.subtract(
      Duration(days: selectedDay.weekday - DateTime.monday),
    );
    final weekPermits = ref.watch(permitsForCalendarWeekProvider(monday));

    final employees = ref.watch(paginatedEmployeesProvider);
    final currentPage = ref.watch(currentPageProvider);
    final totalCount = ref.watch(totalEmployeesCountProvider);
    final totalPages = ref.watch(totalPagesProvider);

    void goFirst() => ref.read(currentPageProvider.notifier).state = 1;
    void goPrev() =>
        ref.read(currentPageProvider.notifier).state = currentPage - 1;
    void goNext() =>
        ref.read(currentPageProvider.notifier).state = currentPage + 1;
    void goLast() => ref.read(currentPageProvider.notifier).state = totalPages;

    const double empColW = 180;
    const double borderW = 1;
    const double rowHeight = 36;

    return Column(
      children: [
        Expanded(
          child: LayoutBuilder(
            builder: (context, constraints) {
              final hours = List.generate(24, (h) => h);
              final availableW = math.max(0, constraints.maxWidth - empColW);
              final hourCellW =
                  (availableW - borderW * hours.length) / hours.length;

              return Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // HEADER
                  Row(
                    children: [
                      Container(
                        width: empColW,
                        height: rowHeight,
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        alignment: Alignment.centerLeft,
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(color: Colors.grey.shade300),
                            right: BorderSide(
                              color: Colors.grey.shade300,
                            ),
                          ),
                        ),
                        child: TextWidget(
                          'Personeller'.hardcoded,
                          style: ATextStyle.text12SemiBold,
                        ),
                      ),
                      Expanded(
                        child: Row(
                          children: hours.map((h) {
                            final label = h < 23
                                ? '${h.toString().padLeft(2, '0')}:00'
                                : '23:59';
                            return Container(
                              width: hourCellW,
                              height: rowHeight,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                border: Border(
                                  right: BorderSide(
                                    color: Colors.grey.shade300,
                                  ),
                                  bottom:
                                      BorderSide(color: Colors.grey.shade300),
                                ),
                              ),
                              child: TextWidget(
                                label,
                                style: ATextStyle.small,
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ],
                  ),

                  // BODY
                  Expanded(
                    child: ListView.builder(
                      itemCount: employees.length,
                      itemBuilder: (context, idx) {
                        final employee = employees[idx];
                        final empPermits = weekPermits
                            .where(
                              (p) =>
                                  p.employeeName == employee &&
                                  !selectedDay.isBefore(p.startDate) &&
                                  !selectedDay.isAfter(p.endDate),
                            )
                            .toList();

                        return Row(
                          children: [
                            // Personel adı
                            Container(
                              width: empColW,
                              height: rowHeight,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12),
                              alignment: Alignment.centerLeft,
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom:
                                      BorderSide(color: Colors.grey.shade300),
                                  right: BorderSide(
                                    color: Colors.grey.shade300,
                                  ),
                                ),
                              ),
                              child: TextWidget(
                                employee,
                                style: ATextStyle.semiSmall,
                              ),
                            ),

                            // Saat dilimleri
                            Expanded(
                              child: Row(
                                children: hours.map((h) {
                                  final slotStart = DateTime(
                                    selectedDay.year,
                                    selectedDay.month,
                                    selectedDay.day,
                                    h,
                                  );
                                  final slotEnd = slotStart
                                      .add(const Duration(hours: 1))
                                      .subtract(const Duration(minutes: 1));

                                  final covered = empPermits.any(
                                    (p) =>
                                        !slotStart.isAfter(p.endDate) &&
                                        !slotEnd.isBefore(p.startDate),
                                  );

                                  return Container(
                                    width: hourCellW,
                                    height: rowHeight,
                                    decoration: BoxDecoration(
                                      color: covered
                                          ? _getPermitColor(
                                              empPermits.firstWhere(
                                                (p) =>
                                                    !slotStart
                                                        .isAfter(p.endDate) &&
                                                    !slotEnd
                                                        .isBefore(p.startDate),
                                              ),
                                            )
                                          : AColor.transparent,
                                      border: Border(
                                        right: BorderSide(
                                          color: Colors.grey.shade300,
                                        ),
                                        bottom: BorderSide(
                                          color: Colors.grey.shade300,
                                        ),
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ],
              );
            },
          ),
        ),

        // PAGINATION
        BottomNavigationButtons(
          onFirst: currentPage > 1 ? goFirst : null,
          onPrevious: goPrev,
          onNext: goNext,
          onLast: currentPage < totalPages ? goLast : null,
          currentPage: currentPage,
          totalPages: totalPages,
          totalCount: totalCount,
        ),
      ],
    );
  }

  Color _getPermitColor(Permit permit) {
    return permit.status == PermitStatus.approved
        ? Colors.red.shade600
        : AColor.transparent;
  }
}

part of '../permits_screen_view.dart';

class PermitsCalendarWeekView extends HookConsumerWidget {
  const PermitsCalendarWeekView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Determine the Monday of the current week
    final selectedDay = ref.watch(permitsCalendarSelectedDayProvider);
    final monday = selectedDay.subtract(
      Duration(days: selectedDay.weekday - DateTime.monday),
    );
    final days = List.generate(7, (i) => monday.add(Duration(days: i)));

    // Permits spanning this week
    final permits = ref.watch(permitsForCalendarWeekProvider(monday));

    // Paginated employees & navigation state
    final employees = ref.watch(paginatedEmployeesProvider);
    final currentPage = ref.watch(currentPageProvider);
    final totalCount = ref.watch(totalEmployeesCountProvider);
    final totalPages = ref.watch(totalPagesProvider);

    // Navigation callbacks
    void goFirstEmp() => ref.read(currentPageProvider.notifier).state = 1;
    void goPrevEmp() =>
        ref.read(currentPageProvider.notifier).state = currentPage - 1;
    void goNextEmp() =>
        ref.read(currentPageProvider.notifier).state = currentPage + 1;
    void goLastEmp() =>
        ref.read(currentPageProvider.notifier).state = totalPages;

    const double empWidth = 180;
    const double borderW = 1;

    // Build the grid for this week
    final calendarGrid = LayoutBuilder(
      builder: (context, constraints) {
        final availableW = math.max(0, constraints.maxWidth - empWidth);
        // each of 7 columns takes equal share
        final colW = availableW / 7;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _WeekHeader(days: days, empWidth: empWidth, borderW: borderW),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    for (var i = 0; i < employees.length; i++)
                      _buildWeekPermitRow(
                        employeeName: employees[i],
                        permits: permits
                            .where((p) => p.employeeName == employees[i])
                            .toList(),
                        days: days,
                        empWidth: empWidth,
                        borderW: borderW,
                        colW: colW,
                        showBottomBorder: i != employees.length - 1,
                      ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );

    return Column(
      children: [
        Expanded(child: calendarGrid),
        BottomNavigationButtons(
          onFirst: currentPage > 1 ? goFirstEmp : null,
          onPrevious: goPrevEmp,
          onNext: goNextEmp,
          onLast: currentPage < totalPages ? goLastEmp : null,
          currentPage: currentPage,
          totalPages: totalPages,
          totalCount: totalCount,
        ),
      ],
    );
  }

  Widget _buildWeekPermitRow({
    required String employeeName,
    required List<Permit> permits,
    required List<DateTime> days,
    required double empWidth,
    required double borderW,
    required double colW,
    required bool showBottomBorder,
  }) {
    final bottomBorder = showBottomBorder
        ? BorderSide(color: Colors.grey.shade300)
        : BorderSide.none;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Employee name column
        Container(
          width: empWidth,
          height: 36,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            border: Border(
              bottom: bottomBorder,
              right: BorderSide(color: Colors.grey.shade300, width: borderW),
            ),
          ),
          child: TextWidget(
            employeeName,
            style: ATextStyle.semiSmall,
          ),
        ),

        // Seven day columns
        for (var i = 0; i < days.length; i++)
          Container(
            width: colW,
            height: 36,
            decoration: BoxDecoration(
              color: permits.any(
                (p) =>
                    !days[i].isBefore(p.startDate) &&
                    !days[i].isAfter(p.endDate),
              )
                  ? AColor.primaryColor.withValues(alpha: 0.6)
                  : AColor.transparent,
              border: Border(
                bottom: bottomBorder,
                right: BorderSide(color: Colors.grey.shade300, width: borderW),
              ),
            ),
          ),
      ],
    );
  }
}

class _WeekHeader extends StatelessWidget {
  const _WeekHeader({
    required this.days,
    required this.empWidth,
    required this.borderW,
  });

  final List<DateTime> days;
  final double empWidth;
  final double borderW;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: empWidth,
          height: 28 + 36,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            border: Border(
              right: BorderSide(color: Colors.grey.shade300, width: borderW),
              bottom: BorderSide(color: Colors.grey.shade300),
            ),
          ),
          child: TextWidget(
            'Personeller'.hardcoded,
            style: ATextStyle.text12SemiBold,
          ),
        ),
        Expanded(
          child: Column(
            children: [
              Container(
                height: 28,
                alignment: Alignment.center,
                child: TextWidget(
                  context.tr.week,
                  style: ATextStyle.mediumRegular,
                ),
              ),
              Container(
                height: 36,
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(color: Colors.grey.shade300),
                    bottom: BorderSide(color: Colors.grey.shade300),
                  ),
                ),
                child: Row(
                  children: [
                    for (final day in days)
                      Expanded(
                        child: Container(
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            border: Border(
                              right: BorderSide(
                                color: Colors.grey.shade300,
                                width: borderW,
                              ),
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              TextWidget(
                                weekName(context, day.weekday),
                                style: ATextStyle.small,
                              ),
                              TextWidget(
                                day.day.toString().padLeft(2, '0'),
                                style: ATextStyle.small,
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

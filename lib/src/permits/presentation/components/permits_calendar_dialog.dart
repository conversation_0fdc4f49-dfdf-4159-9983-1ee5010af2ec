part of '../permits_screen_view.dart';

class PermitsCalendarDialog extends HookConsumerWidget {
  const PermitsCalendarDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentView = ref.watch(permitsCalendarTypeProvider);
    final selectedDate = ref.watch(permitsCalendarSelectedDayProvider);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const _LeftFilterPanel(),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildTopNavBar(context, ref, selectedDate),
                const SizedBox(height: 12),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: <PERSON>uilder(
                      builder: (_) {
                        switch (currentView) {
                          case CalendarType.day:
                            return const PermitsCalendarDayView();
                          case CalendarType.week:
                            return const PermitsCalendarWeekView();
                          case CalendarType.month:
                            return const PermitsCalendarMonthView();
                        }
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

Widget _buildTopNavBar(
  BuildContext context,
  WidgetRef ref,
  DateTime selectedDate,
) {
  final currentType = ref.read(permitsCalendarTypeProvider);
  final current = ref.read(permitsCalendarSelectedDayProvider);

  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Row(
        spacing: 16,
        children: [
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(2),
            ),
            child: Row(
              children: [
                // Today
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    elevation: 0,
                    backgroundColor: Colors.grey.shade50,
                    side: BorderSide(color: Colors.grey.shade300),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  onPressed: () {
                    ref
                        .read(permitsCalendarSelectedDayProvider.notifier)
                        .state = DateTime.now();
                  },
                  child: TextWidget(context.tr.today),
                ),

                // Previous
                SizedBox(
                  width: 25,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      elevation: 0,
                      backgroundColor: Colors.grey.shade50,
                      side: BorderSide(color: Colors.grey.shade300),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(2),
                      ),
                      padding: EdgeInsets.zero,
                    ),
                    onPressed: () {
                      switch (currentType) {
                        case CalendarType.day:
                          ref
                              .read(permitsCalendarSelectedDayProvider.notifier)
                              .state = current.subtract(const Duration(days: 1));
                        case CalendarType.week:
                          ref
                              .read(permitsCalendarSelectedDayProvider.notifier)
                              .state = current.subtract(const Duration(days: 7));
                        case CalendarType.month:
                          ref
                              .read(permitsCalendarSelectedDayProvider.notifier)
                              .state = DateTime(
                            current.year,
                            current.month - 1,
                            current.day,
                          );
                      }
                    },
                    child: const Icon(
                      Icons.arrow_left,
                      size: 20,
                      color: AColor.doveGray,
                    ),
                  ),
                ),

                // Next
                SizedBox(
                  width: 25,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      elevation: 0,
                      backgroundColor: Colors.grey.shade50,
                      side: BorderSide(color: Colors.grey.shade300),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(2),
                      ),
                      padding: EdgeInsets.zero,
                    ),
                    onPressed: () {
                      switch (currentType) {
                        case CalendarType.day:
                          ref
                              .read(permitsCalendarSelectedDayProvider.notifier)
                              .state = current.add(const Duration(days: 1));
                        case CalendarType.week:
                          ref
                              .read(permitsCalendarSelectedDayProvider.notifier)
                              .state = current.add(const Duration(days: 7));
                        case CalendarType.month:
                          ref
                              .read(permitsCalendarSelectedDayProvider.notifier)
                              .state = DateTime(
                            current.year,
                            current.month + 1,
                            current.day,
                          );
                      }
                    },
                    child: const Icon(
                      Icons.arrow_right,
                      size: 20,
                      color: AColor.doveGray,
                    ),
                  ),
                ),
              ],
            ),
          ),
          ACalendarDropDown(
            initialDate: selectedDate,
            onDateSelected: (date) {
              ref.read(permitsCalendarSelectedDayProvider.notifier).state =
                  date;
            },
          ),
        ],
      ),
      permitsCalendarTypeSelector(ref, context),
    ],
  );
}

class _LeftFilterPanel extends HookConsumerWidget {
  const _LeftFilterPanel();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    useState<List<PermitFilter>>([]);

    return Container(
      width: 300,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 8,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 4,
            children: [
              TextWidget(
                'Personel Adı'.hardcoded,
                style: ATextStyle.small.copyWith(color: AColor.textColor),
              ),
              SearchField(
                onChanged: (value) {},
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 4,
            children: [
              TextWidget(
                'Departman'.hardcoded,
                style: ATextStyle.small.copyWith(color: AColor.textColor),
              ),
              MultiSelectFilterDropDown<WebTeamMemberModel>(
                placeholder: context.tr.select,
                itemList: DashboardMockData.getMembers(),
                selectedItems: [],
                onSelected: (members) {},
                width: double.infinity,
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 4,
            children: [
              TextWidget(
                'İzin Türü'.hardcoded,
                style: ATextStyle.small.copyWith(color: AColor.textColor),
              ),
              MultiSelectFilterDropDown<DropdownMenuItem>(
                placeholder: context.tr.select,
                itemList: ['Yıllık', 'Doğum', 'Babalık']
                    .map((t) => DropdownMenuItem(value: t, child: Text(t)))
                    .toList(),
                selectedItems: [],
                onSelected: (members) {},
                width: double.infinity,
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 4,
            children: [
              TextWidget(
                'İzin Başlangıç'.hardcoded,
                style: const TextStyle(color: AColor.textColor),
              ),
              DateRangeDropDown(
                placeholder: 'Seçiniz...'.hardcoded,
                selectedDates: [DateTime.now()],
                onSelected: (singleList) {
                  final date = singleList.first;
                },
                singleDate: true,
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 4,
            children: [
              TextWidget(
                'İzin Bitiş'.hardcoded,
                style: const TextStyle(color: AColor.textColor),
              ),
              DateRangeDropDown(
                placeholder: 'Seçiniz...'.hardcoded,
                selectedDates: [DateTime.now()],
                onSelected: (singleList) {
                  final date = singleList.first;
                },
                singleDate: true,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              LoadingElevatedButton(
                height: 35,
                onPressed: () async {},
                text: 'Tüm Filtreleri Sil'.hardcoded,
                backgroundColor: Colors.white,
                textColor: AColor.primaryColor,
                borderColor: AColor.primaryColor,
              ),
              LoadingElevatedButton(
                height: 35,
                onPressed: () async {},
                text: 'Filtrele'.hardcoded,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

part of '../permits_screen_view.dart';

Widget _statusTabs<T extends Object>(
  BuildContext context,
  ValueNotifier<T> selectedFilter, {
  required int allCount,
  required int pendingCount,
  required int rejectedCount,
  required int approvedCount,
}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      Container(
        height: 35,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.all(4),
          child: Row(
            spacing: 16,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildStatusTab(
                label: PermitFilter.all.label(context),
                count: allCount,
                isSelected: selectedFilter.value == PermitFilter.all,
                onTap: () => selectedFilter.value = PermitFilter.all as T,
              ),
              _buildStatusTab(
                label: PermitFilter.pending.label(context),
                count: pendingCount,
                isSelected: selectedFilter.value == PermitFilter.pending,
                onTap: () => selectedFilter.value = PermitFilter.pending as T,
              ),
              _buildStatusTab(
                label: PermitFilter.approved.label(context),
                count: approvedCount,
                isSelected: selectedFilter.value == PermitFilter.approved,
                onTap: () => selectedFilter.value = PermitFilter.approved as T,
              ),
              _buildStatusTab(
                label: PermitFilter.rejected.label(context),
                count: rejectedCount,
                isSelected: selectedFilter.value == PermitFilter.rejected,
                onTap: () => selectedFilter.value = PermitFilter.rejected as T,
              ),
            ],
          ),
        ),
      ),
    ],
  );
}

Widget _buildStatusTab({
  required String label,
  required int count,
  required bool isSelected,
  required VoidCallback onTap,
}) {
  return InkWell(
    onTap: onTap,
    child: Container(
      padding: const EdgeInsets.symmetric(horizontal: 36),
      height: 30,
      decoration: BoxDecoration(
        color: isSelected ? AColor.white : Colors.grey[300],
        borderRadius:
            isSelected ? BorderRadius.circular(10) : BorderRadius.zero,
      ),
      child: Row(
        spacing: 16,
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Text(
            label,
            style: ATextStyle.text12.copyWith(
              fontWeight: FontWeight.w400,
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AColor.primaryColor,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              '$count',
              style: ATextStyle.small.copyWith(
                color: AColor.white,
              ),
            ),
          ),
        ],
      ),
    ),
  );
}

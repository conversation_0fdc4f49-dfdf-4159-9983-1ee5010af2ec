import 'dart:math' as math;

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:smart_team_common/smart_team_common.dart' hide TeamMemberModel;
import 'package:smart_team_web/src/calendar/presentation/calendar_view.dart';
import 'package:smart_team_web/src/home/<USER>/team_member.dart';
import 'package:smart_team_web/src/home/<USER>/mock_data.dart';
import 'package:smart_team_web/src/permits/application/permit_calendar_provider.dart';
import 'package:smart_team_web/src/permits/domain/permit_model.dart';
import 'package:smart_team_web/src/permits/enum/permit_filter_enum.dart';
import 'package:smart_team_web/src/personal_management/presentation/personal_management_view.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/enums/calendar_type_enum.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/app_dialog/app_dialog.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/calendar_dropdown/a_calendar_dropdown.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_column_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_row_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/st_data_table.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/widgets/bottom_navigation_buttons.dart';
import 'package:smart_team_web/src/widgets/multi_select_filter_dropdown/date_range_dropdown.dart';
import 'package:smart_team_web/src/widgets/multi_select_filter_dropdown/multi_select_filter_dropdown_widget.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

part 'components/permits_calendar_dialog.dart';
part 'components/status_tabs.dart';

part 'widgets/permits_calendar_day_view.dart';
part 'widgets/permits_calendar_week_view.dart';
part 'widgets/permits_calendar_month_view.dart';

@RoutePage(name: 'PermitsRoute')
class PermitsScreenView extends HookConsumerWidget {
  const PermitsScreenView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedFilter = useState<PermitFilter>(PermitFilter.all);

    final permits = ref.watch(allPermitsProvider);

    final allCount = permits.length;
    final pendingCount =
        permits.where((p) => p.status == PermitStatus.pending).length;
    final approvedCount =
        permits.where((p) => p.status == PermitStatus.approved).length;
    final rejectedCount =
        permits.where((p) => p.status == PermitStatus.rejected).length;

    final filtered = selectedFilter.value.toStatus() == null
        ? permits
        : permits
            .where((p) => p.status == selectedFilter.value.toStatus())
            .toList();

    final tabs = _statusTabs<PermitFilter>(
      context,
      selectedFilter,
      allCount: allCount,
      pendingCount: pendingCount,
      rejectedCount: rejectedCount,
      approvedCount: approvedCount,
    );

    final tableColumns = <TableColumnModel>[
      TableColumnModel(columnName: 'Çalışan'.hardcoded),
      TableColumnModel(columnName: 'İzin Türü'.hardcoded),
      TableColumnModel(columnName: 'İzin Süresi'.hardcoded),
      TableColumnModel(columnName: 'İzin Başlangıç'.hardcoded),
      TableColumnModel(columnName: 'İzin Bitiş'.hardcoded),
      TableColumnModel(columnName: 'Onay Tarihi'.hardcoded),
      TableColumnModel(
        columnName: 'Belge Görüntüle'.hardcoded,
        width: 100,
        ignoreWhenExporting: true,
        filterable: false,
        sortable: false,
      ),
      TableColumnModel(
        columnName: 'İşlem'.hardcoded,
        width: 100,
        ignoreWhenExporting: true,
        filterable: false,
        sortable: false,
      ),
    ];

    final rows = List<List<RowDataModel>>.generate(
      filtered.length,
      (index) {
        final p = filtered[index];
        final days = p.endDate.difference(p.startDate).inDays + 1;
        final fmt = DateFormat('dd.MM.yyyy');
        final start = fmt.format(p.startDate);
        final end = fmt.format(p.endDate);
        final approval =
            p.approvalDate != null ? fmt.format(p.approvalDate!) : '-';

        return [
          RowDataModel<String>(
            columnName: 'Çalışan'.hardcoded,
            value: p.employeeName,
            cellBuilder: () =>
                TextWidget(p.employeeName, style: ATextStyle.semiSmallRegular),
          ),
          RowDataModel<String>(
            columnName: 'İzin Türü'.hardcoded,
            value: p.type,
            cellBuilder: () =>
                TextWidget(p.type, style: ATextStyle.semiSmallRegular),
          ),
          RowDataModel<String>(
            columnName: 'İzin Süresi'.hardcoded,
            value: '$days gün',
            cellBuilder: () =>
                TextWidget('$days gün', style: ATextStyle.semiSmallRegular),
          ),
          RowDataModel<String>(
            columnName: 'İzin Başlangıç'.hardcoded,
            value: start,
            cellBuilder: () =>
                TextWidget(start, style: ATextStyle.semiSmallRegular),
          ),
          RowDataModel<String>(
            columnName: 'İzin Bitiş'.hardcoded,
            value: end,
            cellBuilder: () =>
                TextWidget(end, style: ATextStyle.semiSmallRegular),
          ),
          RowDataModel<String>(
            columnName: 'Onay Tarihi'.hardcoded,
            value: approval,
            cellBuilder: () =>
                TextWidget(approval, style: ATextStyle.semiSmallRegular),
          ),
          RowDataModel<String>(
            columnName: 'Belge Görüntüle'.hardcoded,
            value: '',
            cellBuilder: () => IconButton(
              icon: const Icon(Icons.search, size: 24, color: AColor.textColor),
              onPressed: () {},
            ),
          ),
          RowDataModel<String>(
            columnName: 'İşlem'.hardcoded,
            value: '',
            cellBuilder: () => Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  iconSize: 24,
                  icon: const Icon(Icons.cancel_outlined, color: AColor.red),
                  onPressed: () {/* TODO: iptal */},
                ),
                IconButton(
                  iconSize: 24,
                  icon: const Icon(Icons.check_circle_outline,
                      color: AColor.green),
                  onPressed: () {/* TODO: onay */},
                ),
              ],
            ),
          ),
        ];
      },
    );
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          SizedBox(
            height: 35,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Align(
                  child: tabs,
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      LoadingElevatedButton(
                        onPressed: () async {},
                        text: 'İzin Türleri'.hardcoded,
                      ),
                      const SizedBox(width: 16),
                      LoadingElevatedButton(
                        onPressed: () => AppDialog.show<void>(
                          context: context,
                          title: 'İzin Takvimi'.hardcoded,
                          width: context.width * .95,
                          height: context.height * .95,
                          child: const PermitsCalendarDialog(),
                        ),
                        text: 'İzin Takvimi'.hardcoded,
                      ),
                      const SizedBox(width: 8),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: SmartTeamDataTable(
              columns: tableColumns,
              rowData: rows,
            ),
          ),
        ],
      ),
    );
  }
}

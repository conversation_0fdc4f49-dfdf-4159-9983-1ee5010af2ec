import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/enums/cost_status_enum.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';

enum PermitFilter {
  all,
  pending,
  approved,
  rejected,
}

extension PermitFilterX on PermitFilter {
  String label(BuildContext context) {
    switch (this) {
      case PermitFilter.all:
        return 'Tümü'.hardcoded;
      case PermitFilter.pending:
        // Reuse your existing CostStatus labels
        return CostStatus.pending.label(context);
      case PermitFilter.approved:
        return CostStatus.approved.label(context);
      case PermitFilter.rejected:
        return CostStatus.rejected.label(context);
    }
  }

  /// Maps filter to the matching CostStatus, or null for “all”
  CostStatus? toStatus() {
    switch (this) {
      case PermitFilter.all:
        return null;
      case PermitFilter.pending:
        return CostStatus.pending;
      case PermitFilter.approved:
        return CostStatus.approved;
      case PermitFilter.rejected:
        return CostStatus.rejected;
    }
  }
}

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/permits/domain/permit_model.dart';
import 'package:smart_team_web/src/shared/enums/calendar_type_enum.dart';

/// ─────────── EMPLOYEE DATA & PAGINATION ───────────

/// Mock list of all employees (replace with your real API call)
final allEmployeesProvider = Provider<List<String>>((ref) {
  return [
    '<PERSON><PERSON> Yılmaz',
    '<PERSON><PERSON><PERSON><PERSON>mir',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    'Hatice Özdemir',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON> Yılmaz',
    'Ayşe Demir',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>ice Özdemir',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
  ];
});

/// How many employees to show per page
final employeesPerPageProvider = StateProvider<int>((ref) => 15);

/// Current page index (1-based)
final currentPageProvider = StateProvider<int>((ref) => 1);

/// Total number of employees
final totalEmployeesCountProvider = Provider<int>((ref) {
  final all = ref.watch(allEmployeesProvider);
  return all.length;
});

/// Total number of pages = ceil(totalCount / perPage)
final totalPagesProvider = Provider<int>((ref) {
  final total = ref.watch(totalEmployeesCountProvider);
  final per = ref.watch(employeesPerPageProvider);
  return (total / per).ceil();
});

/// Subset of employees for the current page
final paginatedEmployeesProvider = Provider<List<String>>((ref) {
  final all = ref.watch(allEmployeesProvider);
  final page = ref.watch(currentPageProvider);
  final per = ref.watch(employeesPerPageProvider);
  final start = (page - 1) * per;
  final end = (start + per) > all.length ? all.length : start + per;
  return all.sublist(start, end);
});

/// ─────────── CALENDAR SELECTION ───────────

/// Currently selected day in the calendar
final permitsCalendarSelectedDayProvider = StateProvider<DateTime>((ref) {
  return DateTime.now();
});

/// Which view to show: month / week / day
final permitsCalendarTypeProvider = StateProvider<CalendarType>((ref) {
  return CalendarType.month;
});

/// ─────────── PERMITS DATA ───────────

/// Mock list of all permits (replace with real API call)
final allPermitsProvider = Provider<List<Permit>>((ref) {
  final now = DateTime.now();
  // For simplicity, approvalDate = endDate or null
  return [
    Permit(
      id: '1',
      employeeName: 'Ahmet Yılmaz',
      startDate: now.subtract(const Duration(days: 5)),
      endDate: now.subtract(const Duration(days: 3)),
      type: 'Yıllık İzin',
      status: PermitStatus.approved,
    ),
    Permit(
      id: '2',
      employeeName: 'Ayşe Demir',
      startDate: now.subtract(const Duration(days: 2)),
      endDate: now,
      type: 'Doğum İzni',
      status: PermitStatus.approved,
      approvalDate: now.subtract(const Duration(days: 1)),
    ),
    Permit(
      id: '3',
      employeeName: 'Mehmet Kaya',
      startDate: now,
      endDate: now.add(const Duration(days: 2)),
      type: 'Mazeret İzni',
      status: PermitStatus.approved,
      approvalDate: now,
    ),
    Permit(
      id: '4',
      employeeName: 'Zeynep Çelik',
      startDate: now.add(const Duration(days: 1)),
      endDate: now.add(const Duration(days: 4)),
      type: 'Yıllık İzin',
      status: PermitStatus.approved,
    ),
  ];
});

/// Permits overlapping a given month
final permitsForCalendarViewProvider =
    Provider.family<List<Permit>, DateTime>((ref, month) {
  final all = ref.watch(allPermitsProvider);
  final monthStart = DateTime(month.year, month.month);
  final monthEnd = DateTime(month.year, month.month + 1, 0);
  return all.where((p) {
    return !p.startDate.isAfter(monthEnd) && !p.endDate.isBefore(monthStart);
  }).toList();
});

/// Permits overlapping a 7-day week starting at [weekStart]
final permitsForCalendarWeekProvider =
    Provider.family<List<Permit>, DateTime>((ref, weekStart) {
  final all = ref.watch(allPermitsProvider);
  final weekEnd = weekStart.add(const Duration(days: 6));
  return all.where((p) {
    return !p.startDate.isAfter(weekEnd) && !p.endDate.isBefore(weekStart);
  }).toList();
});

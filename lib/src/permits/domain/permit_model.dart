import 'package:freezed_annotation/freezed_annotation.dart';
part 'permit_model.freezed.dart';
part 'permit_model.g.dart';

enum PermitStatus { pending, approved, rejected }

@freezed
abstract class Permit with _$Permit {
  const factory Permit({
    required String id,
    required String employeeName,
    required DateTime startDate,
    required DateTime endDate,
    required String type,
    @Default(PermitStatus.pending) PermitStatus status,
    String? description,
    DateTime? approvalDate,
  }) = _Permit;

  factory Permit.fromJson(Map<String, dynamic> json) => _$PermitFromJson(json);
}

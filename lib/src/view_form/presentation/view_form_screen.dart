import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/widgets/question_builder/question_builder.dart';

@RoutePage()
class ViewFormScreen extends HookConsumerWidget {
  const ViewFormScreen({
    @pathParam required this.templateId,
    super.key,
  });

  final String templateId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: SizedBox.expand(
        child: Align(
              alignment: Alignment.topCenter,
              child: Container(
                margin: const EdgeInsets.all(12),
                constraints: const BoxConstraints(maxWidth: 1200),
                child: QuestionBuilder(
                  formTemplateId: templateId,
                  isEditModeOn: false,
                  onlyAllowPublic: true,
              ),
            ),
          ),
        ),
    );
  }
}

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

@RoutePage(name: 'SettingsRoute')
class SettingsView extends StatelessWidget {
  const SettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        spacing: 16,
        children: [
          TextWidget(
            context.tr.settings,
          ),
        ],
      ),
    );
  }
}

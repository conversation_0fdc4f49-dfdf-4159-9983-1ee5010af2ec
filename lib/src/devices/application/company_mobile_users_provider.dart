import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/global_state_manager/global_state_manager.dart';

final companyMobileUsersAsyncNotifierProvider =
    AutoDisposeAsyncNotifierProvider<CompanyMobileUsersAsyncNotifier, PaginatedDataModel<CompanyMobileUserModel>>(
  CompanyMobileUsersAsyncNotifier.new,
);

class CompanyMobileUsersAsyncNotifier extends AutoDisposeAsyncNotifier<PaginatedDataModel<CompanyMobileUserModel>> with PaginationMixin<CompanyMobileUserModel> {
  Company? company;
  @override
  Future<PaginatedDataModel<CompanyMobileUserModel>> build() async {
    company = ref.watch(globalStateManagerProvider.select((state) => state.selectedCompany));
    return getData(filters: {'company_id': company!.id});
  }

  Future<void> fetchPage(int page) async {
    final newData = await getData(filters: {'company_id': company!.id}, page: page);
    state = AsyncData(newData);
  }
}

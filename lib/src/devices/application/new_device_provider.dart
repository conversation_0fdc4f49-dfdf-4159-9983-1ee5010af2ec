import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';

final groupsProvider =
    StateNotifierProvider<GroupsNotifier, List<String>>((ref) {
  return GroupsNotifier();
});

class GroupsNotifier extends StateNotifier<List<String>> {
  GroupsNotifier()
      : super(<String>[
          'Group A'.hardcoded,
          'Group B'.hardcoded,
          'Group C'.hardcoded,
          'Group D'.hardcoded,
        ]);

  void addGroup(String groupName) {
    state = [...state, groupName];
  }
}

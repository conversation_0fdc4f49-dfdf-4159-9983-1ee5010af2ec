import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/global_state_manager/global_state_manager.dart';
import 'package:smart_team_web/src/devices/domain/add_mobile_user_model.dart';
import 'package:smart_team_web/src/shared/extensions/list_extensions.dart';

final addMobileUserProvider =
    AutoDisposeNotifierProviderFamily<AddMobileUserProvider, AddMobileUserModel, CompanyMobileUserModel?>(
  AddMobileUserProvider.new,
);

class AddMobileUserProvider extends AutoDisposeFamilyNotifier<AddMobileUserModel, CompanyMobileUserModel?> {
  @override
  AddMobileUserModel build(CompanyMobileUserModel? companyMobileUser) {
    final company = ref.watch(globalStateManagerProvider.select((state) => state.selectedCompany))!;
    if (companyMobileUser != null) {
      return AddMobileUserModel.fromCompanyMobileUserAndCompany(companyMobileUser, company);
    }
    return _initAddMobileUserModel(company);
  }

  AddMobileUserModel _initAddMobileUserModel(Company company) {
    final uuid = UUIDGenerator.generate();
    return AddMobileUserModel(
      id: uuid,
      company: company,
      workingPeriods: List.generate(
        7,
        (index) => WorkingPeriod(dayOfWeek: DayOfWeekEnum.values[index], id: UUIDGenerator.generate()),
      ),
      config: MobileUserConfig(
        mobileUserId: uuid,
      ),
    );
  }

  void setName(String name) {
    state = state.copyWith(name: name);
  }

  void setSurname(String surname) {
    state = state.copyWith(surname: surname);
  }

  void setCountryCode(String countryCode) {
    state = state.copyWith(countryCode: countryCode);
  }

  void setPhoneNumber(String phoneNumber) {
    state = state.copyWith(phoneNumber: phoneNumber);
  }

  void setIsAppInstalled({required bool isAppInstalled}) {
    state = state.copyWith.config(appRegistered: isAppInstalled);
  }

  void setLocationDisablePermission({required bool locationDisablePermission}) {
    state = state.copyWith.config(perDisableLocationTracking: locationDisablePermission);
  }

  void setCompanyVisit({required bool companyVisit}) {
    state = state.copyWith.config(validateVisitCustomers: companyVisit);
  }

  void setClosedPortfolio({required bool closedPortfolio}) {
    state = state.copyWith.config(perDisablePushNotification: closedPortfolio);
  }

  void setStartTimeForDay(int index, DateTime startTime) {
    final workingPeriod = state.workingPeriods[index];
    var newEndTime = workingPeriod.endTime;
    if (workingPeriod.endTime != null && startTime.isAfter(workingPeriod.endTime!)) {
      newEndTime = null;
    }

    final newWorkingPeriod = workingPeriod.copyWith(startTime: startTime, endTime: newEndTime);
    final updatedWorkingPeriods = [...state.workingPeriods]..replaceAt(index, newWorkingPeriod);
    state = state.copyWith(workingPeriods: updatedWorkingPeriods);
  }

  void setEndTimeForDay(int index, DateTime endTime) {
    final workingPeriod = state.workingPeriods[index];
    var newStartTime = workingPeriod.startTime;
    if (workingPeriod.startTime != null && endTime.isBefore(workingPeriod.startTime!)) {
      newStartTime = null;
    }

    final newWorkingPeriod = workingPeriod.copyWith(startTime: newStartTime, endTime: endTime);
    final updatedWorkingPeriods = [...state.workingPeriods]..replaceAt(index, newWorkingPeriod);
    state = state.copyWith(workingPeriods: updatedWorkingPeriods);
  }

  void setWorkOff(DayOfWeekEnum dayOfWeek, bool workOff) {
    state = state.copyWith(
      workingPeriods: state.workingPeriods
          .map(
            (e) =>
                e.dayOfWeek == dayOfWeek ? e.copyWith(startTime: null, endTime: null, workOff: workOff) : e,
          )
          .toList(),
    );
  }

  void setTeamSummary(TeamSummaryModel? teamSummary) {
    state = state.copyWith(teamSummary: teamSummary);
  }

  void setCalendarColor(Color color) {
    state = state.copyWith.config(calendarColor: color);
  }

  Future<bool> createOrUpdateMobileUser() async {
    if (arg == null) {
      return _createMobileUser();
    }
    return _updateMobileUser();
  }

  Future<bool> _updateMobileUser() async {
    try {
      await ref.read(mobileUsersRepositoryProvider).updateMobileUserWithConfig(
            mobileUserId: state.id,
            mobileUser: state.toMobileUser(),
            config: state.config,
            workingPeriods: state.workingPeriods,
            teamId: state.teamSummary?.id,
          );
      return true;
    } catch (e, stackTrace) {
      debugPrint(e.toString());
      debugPrint(stackTrace.toString());
    }
    return false;
  }

  Future<bool> _createMobileUser() async {
    try {
      await ref.read(mobileUsersRepositoryProvider).createMobileUserWithConfig(
            mobileUser: state.toMobileUser(),
            config: state.config,
            workingPeriods: state.workingPeriods,
            teamId: state.teamSummary?.id,
          );
      return true;
    } catch (e, stackTrace) {
      debugPrint(e.toString());
      debugPrint(stackTrace.toString());
    }
    return false;
  }
}

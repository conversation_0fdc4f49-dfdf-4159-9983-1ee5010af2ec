// ignore_for_file: invalid_annotation_target

import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:smart_team_web/src/shared/converters/color_converter.dart';
import 'package:smart_team_web/src/shared/converters/timestamp_converter.dart';

part 'user_device_model.freezed.dart';
part 'user_device_model.g.dart';

@freezed
abstract class UserDeviceModel with _$UserDeviceModel {
  const UserDeviceModel._();
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory UserDeviceModel({
    required String id,
    required String nameSurname,
    required String phone,
    required String brand,
    required String model,
    required String version,
    String? vehicle,
    String? group,
    @ColorConverter() Color? calendarColor,
    @Default(false) bool isAppInstalled,
    @Default(false) bool locationDisablePermission,
    @Default(false) bool companyVisit,
    @Default(false) bool closedPortfolio,
    @TimestampConverter.auto() required DateTime createdAt,
    @TimestampConverter.auto() DateTime? updatedAt,
  }) = _UserDeviceModel;

  factory UserDeviceModel.fromJson(Map<String, dynamic> json) =>
      _$UserDeviceModelFromJson(json);
}

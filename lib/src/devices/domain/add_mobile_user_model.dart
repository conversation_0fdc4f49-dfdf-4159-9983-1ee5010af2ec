import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:smart_team_common/smart_team_common.dart';

part 'add_mobile_user_model.freezed.dart';
part 'add_mobile_user_model.g.dart';

@freezed
abstract class AddMobileUserModel with _$AddMobileUserModel {
  const factory AddMobileUserModel({
    required String id,
    required Company company,
    required MobileUserConfig config,
    @Default('') String name,
    @Default('') String surname,
    @Default('90') String countryCode,
    @Default('') String phoneNumber,
    @Default([]) List<WorkingPeriod> workingPeriods,
    TeamSummaryModel? teamSummary,
  }) = _AddMobileUserModel;

  const AddMobileUserModel._();

  factory AddMobileUserModel.fromJson(Map<String, dynamic> json) => _$AddMobileUserModelFromJson(json);

  factory AddMobileUserModel.fromCompanyMobileUserAndCompany(
      CompanyMobileUserModel companyMobileUser, Company company) {
    return AddMobileUserModel(
      id: companyMobileUser.mobileUser.id,
      company: company,
      name: companyMobileUser.mobileUser.name,
      surname: companyMobileUser.mobileUser.surname,
      countryCode: companyMobileUser.mobileUser.phonecountrycode ?? '',
      phoneNumber: companyMobileUser.mobileUser.phonewithoutcountrycode ?? '',
      workingPeriods: companyMobileUser.workingPeriods ?? [],
      teamSummary: companyMobileUser.team,
      config: companyMobileUser.mobileUserConfig ??
          MobileUserConfig(mobileUserId: companyMobileUser.mobileUser.id),
    );
  }

  DateTime? getStartTimeForDay(DayOfWeekEnum dayOfWeek) {
    return workingPeriods.firstWhere((element) => element.dayOfWeek == dayOfWeek).startTime;
  }

  DateTime? getEndTimeForDay(DayOfWeekEnum dayOfWeek) {
    return workingPeriods.firstWhere((element) => element.dayOfWeek == dayOfWeek).endTime;
  }

  MobileUser toMobileUser() {
    return MobileUser(
      id: id,
      company: company,
      name: name,
      surname: surname,
      phoneCountryCode: countryCode,
      phoneWithoutCountryCode: phoneNumber,
      phone: '$countryCode$phoneNumber',
    );
  }

  List<MobileUserWorkingPeriod> toMobileUserWorkingPeriods() {
    return workingPeriods
        .map((e) => MobileUserWorkingPeriod(
              mobileUserId: config.mobileUserId,
              workingPeriodId: e.id!,
            ))
        .toList();
  }
}

import 'dart:math';

import 'package:country_picker/country_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/devices/application/new_device_provider.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/enums/form_type.dart';
import 'package:smart_team_web/src/shared/enums/regex_type.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/random_color_extension.dart';
import 'package:smart_team_web/src/shared/extensions/string_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/shared/providers/form_key_provider.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/toast/toast_manager.dart';
import 'package:smart_team_web/src/widgets/app_dialog/app_dialog.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/data_table/schedule_table.dart';
import 'package:smart_team_web/src/widgets/form_fields/dropdown_formfield.dart';
import 'package:smart_team_web/src/widgets/switch/custom_switch.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

part 'widgets/add_group_widgets.dart';

class NewDeviceWidgets extends HookConsumerWidget {
  const NewDeviceWidgets({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = ref.watch(formKeyProvider(FormType.addMobileUser));
    final groups = ref.watch(groupsProvider);

    final applicationInstalled = useState<bool>(false);
    final locationClosureAuthorization = useState<bool>(false);
    final companyName = useState<bool>(true);
    final closedPortfolio = useState<bool>(false);
    final phoneController = useTextEditingController();
    final selectedDialCode = useState<String>('+90');

    final selectedGroup = useState<String?>(null);
    final selectedColor = useState<Color>(Random().randomColor);

    final autovalidate = useState<AutovalidateMode>(AutovalidateMode.disabled);

    return Form(
      key: formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          spacing: 16,
          children: [
            Row(
              spacing: 16,
              children: [
                CustomSwitch(
                  header: context.tr.applicationInstalled,
                  value: applicationInstalled.value,
                  onChanged: (val) async {
                    if (!val) {
                      final confirm = await showDialog<bool>(
                        context: context,
                        builder: (_) => AlertDialog(
                          title: TextWidget(
                            'Emin misin?'.hardcoded,
                            style: ATextStyle.text13,
                          ),
                          actions: [
                            ElevatedButton(
                              style: OutlinedButton.styleFrom(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(35 / 2),
                                ),
                                foregroundColor: AColor.primaryColor,
                                backgroundColor: AColor.white,
                              ),
                              onPressed: () => Navigator.of(context).pop(false),
                              child: TextWidget(
                                'Hayır'.hardcoded,
                                style: ATextStyle.text13,
                              ),
                            ),
                            ElevatedButton(
                              style: OutlinedButton.styleFrom(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(35 / 2),
                                ),
                                foregroundColor: AColor.white,
                                backgroundColor: AColor.primaryColor,
                              ),
                              onPressed: () => Navigator.of(context).pop(true),
                              child: TextWidget(
                                'Evet'.hardcoded,
                                style: ATextStyle.text13,
                              ),
                            ),
                          ],
                        ),
                      );
                      if (confirm == true) {
                        applicationInstalled.value = val;
                      }
                    } else {
                      applicationInstalled.value = val;
                    }
                  },
                ),
                CustomSwitch(
                  header: context.tr.locationClosureAuthorization,
                  value: locationClosureAuthorization.value,
                  onChanged: (val) => locationClosureAuthorization.value = val,
                ),
                CustomSwitch(
                  header: 'Firma Konumunda Ziyaret'.hardcoded,
                  value: companyName.value,
                  onChanged: (val) => companyName.value = val,
                ),
                CustomSwitch(
                  header: context.tr.closedPortfolio,
                  value: closedPortfolio.value,
                  onChanged: (val) => closedPortfolio.value = val,
                ),
              ],
            ),
            Row(
              spacing: 16,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: 8,
                    children: [
                      TextWidget(
                        context.tr.gsm,
                        style: ATextStyle.text13,
                      ),
                      Row(
                        spacing: 4,
                        children: [
                          GestureDetector(
                            onTap: () {
                              showCountryPicker(
                                context: context,
                                showPhoneCode: true,
                                onSelect: (Country country) {
                                  selectedDialCode.value =
                                      '+${country.phoneCode}';
                                },
                                countryListTheme: CountryListThemeData(
                                  backgroundColor: AColor.white,
                                  textStyle: ATextStyle.text13.copyWith(
                                    color: AColor.black,
                                  ),
                                  bottomSheetHeight: context.height * 0.7,
                                  borderRadius: const BorderRadius.vertical(
                                    top: Radius.circular(16),
                                  ),
                                ),
                              );
                            },
                            child: Container(
                              decoration: CommonDecorations.containerDecoration(
                                borderColor: Colors.grey.shade400,
                              ),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 7,
                              ),
                              child: TextWidget(
                                selectedDialCode.value,
                                style: ATextStyle.text13.copyWith(
                                  color: AColor.black,
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: Container(
                              decoration:
                                  CommonDecorations.containerDecoration(),
                              child: CustomTextFormField(
                                controller: phoneController,
                                hintText: '555 555 55 55'.hardcoded,
                                keyboardType: TextInputType.phone,
                                textInputAction: TextInputAction.next,
                                validator: (value) =>
                                    value.isValidPhone(context),
                                regexType: RegexType.phone,
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 12,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Container(
                    decoration: CommonDecorations.containerDecoration(),
                    child: CustomTextFormField(
                      headerText: context.tr.nameSurname,
                      hintText: context.tr.device,
                      keyboardType: TextInputType.name,
                      textInputAction: TextInputAction.next,
                      onChanged: (value) {},
                      validator: (value) => value.isValidName(context),
                      regexType: RegexType.name,
                    ),
                  ),
                ),
              ],
            ),
            Row(
              spacing: 16,
              children: [
                Expanded(
                  child: ANewDropdownFormField<String>(
                    header: context.tr.group,
                    placeholder: context.tr.select,
                    itemList: groups,
                    selectedItems: selectedGroup.value == null
                        ? []
                        : [selectedGroup.value!],
                    onSelected: (value) {
                      selectedGroup.value = value;
                    },
                    onLoadNewItems: () async {
                      await AppDialog.withoutHeader<void>(
                        context: context,
                        width: context.width * .50,
                        height: context.height * .50,
                        child: const AddGroupWidgets(),
                      );
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return context.tr.fieldRequired;
                      }
                      return null;
                    },
                  ),
                ),
                Expanded(
                  child: ANewDropdownFormField<String>(
                    header: context.tr.calendarColor,
                    placeholder: context.tr.select,
                    itemList: const [],
                    selectedItems: const [],
                    onSelected: (item) {},
                    enableColorPicker: true,
                    initialColor: selectedColor.value,
                    onColorSelected: (color) {
                      selectedColor.value = color;
                    },
                  ),
                ),
              ],
            ),
            const ScheduleTable(),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                LoadingElevatedButton(
                  height: 45,
                  onPressed: () async {
                    if (!formKey.currentState!.validate()) {
                      ref
                          .read(toastManagerProvider)
                          .showToast('Tüm alanları doldurunuz.'.hardcoded);
                      return;
                    }
                    if (formKey.currentState!.validate()) {}
                  },
                  text: context.tr.save,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

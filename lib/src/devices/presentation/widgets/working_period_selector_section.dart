part of '../add_new_mobile_user_dialog_contents.dart';

class _WorkingPeriodSelectorSection extends HookConsumerWidget {
  const _WorkingPeriodSelectorSection({this.companyMobileUser});
  final CompanyMobileUserModel? companyMobileUser;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final style = ref.watch(appStyleProvider);
    final state = ref.watch(addMobileUserProvider(companyMobileUser));
    final notifier = ref.read(addMobileUserProvider(companyMobileUser).notifier);
    const headerHeight = 20.0;
    const rowHeight = 40.0;
    final verticalSpacing = style.insets.sm;

    Widget buildDayColumn({required int index}) {
      final workingPeriod = state.workingPeriods[index];
      return Expanded(
        flex: 3,
        child: Column(
          spacing: verticalSpacing,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: headerHeight,
              child: Center(
                child: Text(
                  workingPeriod.dayOfWeek.getLocalizedName(),
                  style: style.text.bodyXXSmall,
                ),
              ),
            ),
            DateTimePicker(
              mode: DateTimePickerMode.time,
              selectedDate: workingPeriod.startTime,
              isDisabled: workingPeriod.workOff,
              onSelected: (date) {
                notifier.setStartTimeForDay(index, date);
              },
            ),
            DateTimePicker(
              mode: DateTimePickerMode.time,
              selectedDate: workingPeriod.endTime,
              startDate: workingPeriod.startTime,
              isDisabled: workingPeriod.workOff,
              onSelected: (date) {
                if (workingPeriod.startTime != null && date.isBefore(workingPeriod.startTime!)) {
                  ref.read(toastManagerProvider).showToast('Başlangıç zamanı bitiş zamanından önce olamaz');
                } else {
                  notifier.setEndTimeForDay(index, date);
                }
              },
            ),
            SizedBox(
              height: rowHeight,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Checkbox(
                  value: workingPeriod.workOff,
                  onChanged: (value) {
                    notifier.setWorkOff(workingPeriod.dayOfWeek, value ?? false);
                  },
                  activeColor: style.colors.primaryColor,
                  side: BorderSide(color: style.colors.grayMedium),
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(style.insets.xs).copyWith(top: 0),
      decoration: BoxDecoration(
        color: style.colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        spacing: style.insets.xs,
        children: [
          Expanded(
            child: Column(
              spacing: verticalSpacing,
              children: [
                const SizedBox(height: headerHeight),
                SizedBox(
                  height: rowHeight,
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      'Başlangıç Zamanı'.hardcoded,
                      style: style.text.bodyXXSmall,
                    ),
                  ),
                ),
                SizedBox(
                  height: rowHeight,
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      'Bitiş Zamanı'.hardcoded,
                      style: style.text.bodyXXSmall,
                    ),
                  ),
                ),
                SizedBox(
                  height: rowHeight,
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      'Mesai Dışı'.hardcoded,
                      style: style.text.bodyXXSmall,
                    ),
                  ),
                ),
              ],
            ),
          ),
          ...List.generate(7, (index) => buildDayColumn(index: index)),
        ],
      ),
    );
  }
}

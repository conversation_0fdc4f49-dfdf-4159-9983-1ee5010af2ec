part of '../add_new_mobile_user_dialog_contents.dart';

class _SwitchesSection extends HookConsumerWidget {
  const _SwitchesSection({this.companyMobileUser});
  final CompanyMobileUserModel? companyMobileUser;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final style = ref.watch(appStyleProvider);
    final state = ref.watch(addMobileUserProvider(companyMobileUser));
    final notifier = ref.read(addMobileUserProvider(companyMobileUser).notifier);

    Widget buildSwitch({required String title, required bool value, required void Function(bool) onChanged}) {
      return Container(
        padding: EdgeInsets.all(style.insets.xs),
        decoration: BoxDecoration(
          color: style.colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 4,
          children: [
            Text(
              title,
              style: style.text.bodyXXSmall,
            ),
            CustomSwitch(
              value: value,
              onChanged: onChanged,
            ),
          ],
        ),
      );
    }

    return Row(
      spacing: 16,
      children: [
        buildSwitch(
          title: context.tr.applicationInstalled,
          value: state.config.appRegistered,
          onChanged: (value) {
            notifier.setIsAppInstalled(isAppInstalled: value);
          },
        ),
        buildSwitch(
          title: context.tr.locationClosureAuthorization,
          value: state.config.perDisableLocationTracking,
          onChanged: (value) {
            notifier.setLocationDisablePermission(locationDisablePermission: value);
          },
        ),
        buildSwitch(
          title: 'Firma Konumunda Ziyaret'.hardcoded,
          value: state.config.validateVisitCustomers,
          onChanged: (value) {
            notifier.setCompanyVisit(companyVisit: value);
          },
        ),
        buildSwitch(
          title: context.tr.closedPortfolio,
          value: state.config.perDisablePushNotification,
          onChanged: (value) {
            notifier.setClosedPortfolio(closedPortfolio: value);
          },
        ),
      ],
    );
  }
}

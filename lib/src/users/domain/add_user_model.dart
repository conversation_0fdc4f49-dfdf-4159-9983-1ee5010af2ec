import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:smart_team_common/smart_team_common.dart';

part 'add_user_model.freezed.dart';
part 'add_user_model.g.dart';

@freezed
abstract class AddUserModel with _$AddUserModel {
  const factory AddUserModel({
    required Company company,
    @Default('') String name,
    @Default('') String surname,
    @Default('') String email,
    @Default('90') String countryCode,
    @Default('') String phoneNumber,
    @Default('') String password,
    @Default('') String passwordAgain,
    @Default('') String userName,
    @Default([]) List<MobileUser> managedMobileUsers,
    Role? role,
    MobileUser? mobileUser,
  }) = _AddUserModel;

  const AddUserModel._();

  factory AddUserModel.fromJson(Map<String, dynamic> json) =>
      _$AddUserModelFromJson(json);

  factory AddUserModel.fromCompanyUser(CompanyUserModel companyUser) {
    return AddUserModel(
      company: companyUser.user.company,
      name: companyUser.user.name,
      surname: companyUser.user.surname ?? '',
      email: companyUser.user.email ?? '',
      countryCode: companyUser.user.phoneCountryCode ?? '90',
      phoneNumber: companyUser.user.phoneWithoutCountryCode ?? '',
      userName: companyUser.user.username ?? '',
      role: companyUser.role,
      mobileUser: companyUser.mobileUser,
      managedMobileUsers: companyUser.managedMobileUsers,
    );
  }

  User toUser({String? id}) {
    return User(
      id: id ?? UUIDGenerator.generate(),
      name: name,
      surname: surname,
      email: email,
      phoneCountryCode: countryCode,
      phone: '$countryCode$phoneNumber',
      phoneWithoutCountryCode: phoneNumber,
      company: company,
      username: userName,
    );
  }
}

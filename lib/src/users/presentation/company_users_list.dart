import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/users/application/company_users_provider.dart';
import 'package:smart_team_web/src/users/presentation/new_user_widgets.dart';
import 'package:smart_team_web/src/widgets/app_dialog/app_dialog.dart';
import 'package:smart_team_web/src/widgets/base_async_provider_widget.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_column_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_row_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/st_data_table.dart';

class CompanyUsersList extends HookConsumerWidget {
  const CompanyUsersList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final companyUsersProvider = ref.watch(companyUsersAsyncNotifierProvider);
    const tableColumnModels = [
      TableColumnModel(columnName: 'Ad'),
      TableColumnModel(columnName: 'Kullanıcı Adı'),
      TableColumnModel(columnName: 'Rol'),
      TableColumnModel(columnName: 'Gsm'),
      TableColumnModel(
        columnName: '',
        width: 220,
        sortable: false,
        filterable: false,
      ),
    ];

    List<List<RowDataModel<dynamic>>> getRowData({required List<CompanyUserModel> companyUsers}) {
      return companyUsers.map((companyUser) {
        return [
          RowDataModel<String>(
            columnName: tableColumnModels.first.columnName,
            value: companyUser.user.name,
          ),
          RowDataModel<String>(
            columnName: tableColumnModels[1].columnName,
            value: companyUser.user.username,
          ),
          RowDataModel<String?>(
            columnName: tableColumnModels[2].columnName,
            value: companyUser.role?.displayName,
          ),
          RowDataModel<String?>(
            columnName: tableColumnModels[3].columnName,
            value: companyUser.user.phone != null ? '+${companyUser.user.phone}' : null,
          ),
          RowDataModel<void>(
            columnName: tableColumnModels[4].columnName,
            cellBuilder: () {
              return Row(
                spacing: 8,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  LoadingElevatedButton(
                    height: 30,
                    text: (companyUser.user.isActive ?? true) ? 'Pasif'.hardcoded : 'Aktif'.hardcoded,
                    backgroundColor: AColor.white,
                    borderColor: AColor.loginBorderColor,
                    textColor: AColor.mineShaft,
                    onPressed: () {
                      ref.read(companyUsersAsyncNotifierProvider.notifier).toggleUserActive(companyUser.user);
                    },
                  ),
                  LoadingElevatedButton(
                    height: 30,
                    text: 'Düzenle'.hardcoded,
                    backgroundColor: AColor.white,
                    borderColor: AColor.loginBorderColor,
                    textColor: AColor.mineShaft,
                    onPressed: () {
                      context.maybePop();
                      AppDialog.show<void>(
                          context: context,
                          title: context.tr.newUser,
                          width: context.width * .95,
                          height: context.height * .95,
                          child: NewUserWidgets(companyUser: companyUser),
                        );
                    },
                  ),
                ],
              );
            },
          ),
        ];
      }).toList();
    }

    return BaseAsyncProviderWidget<PaginatedDataModel<CompanyUserModel>>(
      value: companyUsersProvider,
      loadingWidget: const SizedBox.shrink(),
      builder: (paginatedData) {
        return SmartTeamDataTable(
          showExportButtons: false,
          columns: tableColumnModels,
          rowData: getRowData(companyUsers: paginatedData.items),
          totalItemCount: paginatedData.total,
          onPageChanged: ref.read(companyUsersAsyncNotifierProvider.notifier).fetchPage,
        );
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/online_user_manager/online_user_manager.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/enums/form_type.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/riverpod_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/shared/providers/form_key_provider.dart';
import 'package:smart_team_web/src/shared/providers/user_roles_provider.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/toast/toast_manager.dart';
import 'package:smart_team_web/src/users/application/add_user_provider.dart';
import 'package:smart_team_web/src/users/application/company_users_provider.dart';
import 'package:smart_team_web/src/users/application/mobile_users_provider.dart';
import 'package:smart_team_web/src/users/domain/add_user_model.dart';
import 'package:smart_team_web/src/widgets/base_async_provider_widget.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/form_fields/dropdown_formfield.dart';
import 'package:smart_team_web/src/widgets/form_fields/phone_picker_formfield.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';

class NewUserWidgets extends StatefulHookConsumerWidget {
  const NewUserWidgets({super.key, this.companyUser});
  final CompanyUserModel? companyUser;

  @override
  ConsumerState<NewUserWidgets> createState() => _NewUserWidgetsState();
}

class _NewUserWidgetsState extends ConsumerState<NewUserWidgets> {
  late final AutoDisposeFamilyNotifierProvider<AddUser, AddUserModel,
      CompanyUserModel?> _addUserNotifier;
  @override
  void initState() {
    super.initState();
    _addUserNotifier = addUserProvider(widget.companyUser);
  }

  @override
  Widget build(BuildContext context) {
    final formKey = ref.watch(formKeyProvider(FormType.addUser));
    final onlineUser = ref.futureValue(onlineUserManagerProvider)!;
    final userRolesProvider = ref.watch(userRolesFutureProvider);
    final mobileUsersProvider = ref.watch(mobileUsersFutureProvider);
    final unLinkedMobileUsersProvider =
        ref.watch(unLinkedMobileUsersFutureProvider);
    final nameController =
        useTextEditingController(text: widget.companyUser?.user.name);
    final surnameController =
        useTextEditingController(text: widget.companyUser?.user.surname);
    final userNameController =
        useTextEditingController(text: widget.companyUser?.user.username);
    final emailController =
        useTextEditingController(text: widget.companyUser?.user.email);
    final passwordController = useTextEditingController();
    final passwordAgainController = useTextEditingController();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 16,
          children: [
            Row(
              spacing: 8,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: BaseAsyncProviderWidget<List<MobileUser>>(
                    value: mobileUsersProvider,
                    loadingWidget: const SizedBox.shrink(),
                    builder: (mobileUsers) {
                      return Consumer(
                        builder: (context, innerRef, child) {
                          final selectedRole = innerRef.watch(
                              _addUserNotifier.select((value) => value.role));
                          final selectedManagedUsers = innerRef.watch(
                              _addUserNotifier
                                  .select((value) => value.managedMobileUsers));
                          if (selectedRole?.id == RoleTypeEnum.webManager ||
                              selectedRole?.id == RoleTypeEnum.webUser) {
                            return ANewDropdownFormField<MobileUser>(
                              placeholder: context.tr.select,
                              header: context.tr.selectDevice,
                              itemList: mobileUsers,
                              selectedItems: selectedManagedUsers,
                              allowMultipleSelection: true,
                              enableSearch: true,
                              onSelected: (user) {
                                ref
                                    .read(_addUserNotifier.notifier)
                                    .toggleManagedUser(user);
                              },
                              validator: (val) {
                                if (val == null) {
                                  return 'Kullanıcı cihazı seçiniz.'.hardcoded;
                                }
                                return null;
                              },
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      );
                    },
                  ),
                ),
                const Spacer(),
              ],
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 16,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: 8,
                    children: [
                      Row(
                        spacing: 8,
                        children: [
                          Expanded(
                            child: CustomTextFormField(
                              controller: nameController,
                              headerText: 'Ad'.hardcoded,
                              onChanged:
                                  ref.read(_addUserNotifier.notifier).setName,
                              validator: (value) => value.isValidName(context),
                            ),
                          ),
                          Expanded(
                            child: CustomTextFormField(
                              controller: surnameController,
                              headerText: 'Soyad'.hardcoded,
                              onChanged: ref
                                  .read(_addUserNotifier.notifier)
                                  .setSurname,
                              validator: (value) => value.isValidName(context),
                            ),
                          ),
                        ],
                      ),
                      Consumer(
                        builder: (context, innerRef, child) {
                          final countryCode = innerRef.watch(_addUserNotifier
                              .select((value) => value.countryCode));
                          final phoneNumber = innerRef.watch(_addUserNotifier
                              .select((value) => value.phoneNumber));
                          return PhonePickerFormField(
                            phoneNumberInfo: (
                              countryCode: countryCode,
                              phoneNumber: phoneNumber,
                            ),
                            headerText: context.tr.gsm,
                            onPhoneNumberChanged: (value) {
                              ref
                                  .read(_addUserNotifier.notifier)
                                  .setPhoneNumber(value);
                            },
                          );
                        },
                      ),
                      BaseAsyncProviderWidget(
                        value: userRolesProvider,
                        builder: (roles) {
                          return Consumer(
                            builder: (context, innerRef, child) {
                              final userRole = onlineUser.userRole;
                              final selectedRole = innerRef.watch(
                                  _addUserNotifier
                                      .select((value) => value.role));
                              return ANewDropdownFormField<Role>(
                                placeholder: context.tr.select,
                                header: context.tr.roleType,
                                itemList: roles.where((e) {
                                  if (userRole!.role.isSystemRole) {
                                    return true;
                                  }
                                  return e.id == RoleTypeEnum.webManager ||
                                      e.id == RoleTypeEnum.webUser;
                                }).toList(),
                                selectedItems:
                                    selectedRole == null ? [] : [selectedRole],
                                onSelected:
                                    ref.read(_addUserNotifier.notifier).setRole,
                                itemBuilder: (role) {
                                  return Text(
                                    role!.displayName ?? '',
                                    style: ATextStyle.mediumRegular,
                                  );
                                },
                                validator: (val) {
                                  if (val == null) {
                                    return 'Lütfen rol seçiniz'.hardcoded;
                                  }
                                  return null;
                                },
                              );
                            },
                          );
                        },
                      ),
                      if (widget.companyUser == null)
                        CustomTextFormField(
                          headerText: context.tr.password,
                          controller: passwordController,
                          validator: (val) => val.isValidPassword(context),
                        ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    spacing: 8,
                    children: [
                      CustomTextFormField(
                        controller: userNameController,
                        headerText: context.tr.userName,
                        validator: (val) => val.isValidField(context),
                        onChanged:
                            ref.read(_addUserNotifier.notifier).setUserName,
                      ),
                      CustomTextFormField(
                        headerText: context.tr.email,
                        controller: emailController,
                        validator: (val) => val.isValidMail(context),
                        onChanged: ref.read(_addUserNotifier.notifier).setEmail,
                      ),
                      BaseAsyncProviderWidget<List<MobileUser>>(
                        value: unLinkedMobileUsersProvider,
                        builder: (users) {
                          return Consumer(
                            builder: (context, innerRef, child) {
                              final selectedRole = innerRef.watch(
                                  _addUserNotifier
                                      .select((value) => value.role));
                              final selectedUserDevice = innerRef.watch(
                                  _addUserNotifier
                                      .select((value) => value.mobileUser));
                              return ANewDropdownFormField<MobileUser>(
                                placeholder: context.tr.select,
                                isDisabled: selectedRole?.id !=
                                    RoleTypeEnum.webManager,
                                header: context.tr.userDevice,
                                itemList: users,
                                selectedItems: selectedUserDevice == null
                                    ? null
                                    : [selectedUserDevice],
                                onSelected: (user) {
                                  ref
                                      .read(_addUserNotifier.notifier)
                                      .setMobileUser(user);
                                },
                                validator: (val) {
                                  if (selectedRole?.id !=
                                      RoleTypeEnum.webManager) {
                                    return null;
                                  }
                                  if (val == null) {
                                    return 'Kullanıcı cihazı seçiniz.'
                                        .hardcoded;
                                  }
                                  return null;
                                },
                              );
                            },
                          );
                        },
                      ),
                      if (widget.companyUser == null)
                        CustomTextFormField(
                          headerText: context.tr.passAgain,
                          controller: passwordAgainController,
                          validator: (val) => val.isValidPasswordAgain(
                              context, passwordController.text),
                        ),
                      if (widget.companyUser == null)
                        LoadingElevatedButton(
                          height: 40,
                          text: context.tr.generatePass,
                          backgroundColor: AColor.white,
                          borderColor: AColor.primaryColor,
                          textColor: AColor.primaryColor,
                          onPressed: () {
                            final newPassword = ref
                                .read(_addUserNotifier.notifier)
                                .generateAndSetPasswords();
                            passwordController.text = newPassword;
                            passwordAgainController.text = newPassword;
                          },
                        ),
                    ],
                  ),
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                LoadingElevatedButton(
                  height: 45,
                  onPressed: () async {
                    if (!formKey.currentState!.validate()) {
                      return;
                    }
                    final isSuccess = await ref.executeWithLoading<bool>(
                        ref.read(_addUserNotifier.notifier).submitForm);
                    if (!context.mounted) return;
                    if (isSuccess) {
                      final toastMessage = widget.companyUser == null
                          ? 'Kullanıcı başarıyla kaydedildi.'.hardcoded
                          : 'Kullanıcı başarıyla güncellendi.'.hardcoded;
                      ref.read(toastManagerProvider).showToast(toastMessage);
                      ref.invalidate(companyUsersAsyncNotifierProvider);
                      await context.maybePop();
                    } else {
                      ref.read(toastManagerProvider).showToast(
                          'Kullanıcı kaydedilirken bir hata oluştu.'.hardcoded);
                    }
                  },
                  text: widget.companyUser == null
                      ? context.tr.save
                      : 'Güncelle'.hardcoded,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/global_state_manager/global_state_manager.dart';
import 'package:smart_team_web/src/users/domain/add_user_model.dart';

final addUserProvider =
    AutoDisposeNotifierProviderFamily<AddUser, AddUserModel, CompanyUserModel?>(AddUser.new);

class AddUser extends AutoDisposeFamilyNotifier<AddUserModel, CompanyUserModel?> {
  @override
  AddUserModel build(CompanyUserModel? companyUserModel) {
    final selectedCompany = ref.watch(globalStateManagerProvider.select((state) => state.selectedCompany));
    return companyUserModel == null
        ? AddUserModel(company: selectedCompany!)
        : AddUserModel.fromCompanyUser(companyUserModel);
  }

  void setName(String name) {
    state = state.copyWith(name: name);
  }

  void setSurname(String surname) {
    state = state.copyWith(surname: surname);
  }

  void setUserName(String userName) {
    state = state.copyWith(userName: userName);
  }

  void setEmail(String email) {
    state = state.copyWith(email: email);
  }

  void setPhoneNumber(({String countryCode, String phoneNumber}) phoneNumberInfo) {
    state =
        state.copyWith(countryCode: phoneNumberInfo.countryCode, phoneNumber: phoneNumberInfo.phoneNumber);
  }

  void setRole(Role role) {
    if (role == state.role) return;
    state = state.copyWith(role: role, mobileUser: null, managedMobileUsers: []);
  }

  void setMobileUser(MobileUser mobileUser) {
    state = state.copyWith(mobileUser: mobileUser);
  }

  void toggleManagedUser(MobileUser mobileUser) {
    if (state.managedMobileUsers.any((e) => e.id == mobileUser.id)) {
      state = state.copyWith(managedMobileUsers: state.managedMobileUsers.where((e) => e.id != mobileUser.id).toList());
    } else {
      state = state.copyWith(managedMobileUsers: [...state.managedMobileUsers, mobileUser]);
    }
  }

  String generateAndSetPasswords() {
    final generatedPassword = PasswordHelpers.generatePassword();
    state = state.copyWith(password: generatedPassword, passwordAgain: generatedPassword);
    return generatedPassword;
  }

  Future<bool> submitForm() async {
    if (arg == null) {
      return createUser();
    } else {
      return updateUser();
    }
  }

  Future<bool> createUser() async {
    final isUserCreatedByAdmin = await ref
        .read(userRepositoryProvider)
        .createUserFromAdmin(email: state.email, password: state.password);
    if (!isUserCreatedByAdmin) return false;
    final user = state.toUser();
    final isUserCreated = await ref.read(userRepositoryProvider).create(user);
    if (!isUserCreated) return false;
    final userRole =
        UserRole(user: user, role: state.role!, createdAt: DateTime.now(), updatedAt: DateTime.now());
    final isUserRoleCreated = await ref.read(userRoleRepositoryProvider).create(userRole);
    if (!isUserRoleCreated) return false;
    if (state.mobileUser != null) {
      final isUserMobileUserCreated = await ref
          .read(userMobileUserRepositoryProvider)
          .create(UserMobileUser(user: user, mobileUser: state.mobileUser!));
      if (!isUserMobileUserCreated) return false;
    }
    if (state.managedMobileUsers.isNotEmpty) {
      final usersLinked = await ref.read(userManagedMobileUsersRepositoryProvider).linkUserToMobileUsers(
            userId: user.id!,
            mobileUserIds: state.managedMobileUsers.map((e) => e.id!).toList(),
          );

      if (!usersLinked) return false;
    }
    return true;
  }

  Future<bool> updateUser() async {
    final user = state.toUser(id: arg?.user.id);
    final isUserCreated = await ref.read(userRepositoryProvider).update(user);
    if (!isUserCreated) return false;
    final userRole =
        UserRole(user: user, role: state.role!, createdAt: DateTime.now(), updatedAt: DateTime.now());
    final isUserRoleCreated = await ref.read(userRoleRepositoryProvider).update(userRole);
    if (!isUserRoleCreated) return false;
    if (state.mobileUser != null && state.mobileUser!.id != arg!.mobileUser?.id) {
      final isUserMobileUserCreated = await ref
          .read(userMobileUserRepositoryProvider)
          .upsert(UserMobileUser(user: user, mobileUser: state.mobileUser!));
      if (!isUserMobileUserCreated) return false;
    }
    final usersLinked = await ref.read(userManagedMobileUsersRepositoryProvider).linkUserToMobileUsers(
            userId: user.id!,
            mobileUserIds: state.managedMobileUsers.map((e) => e.id!).toList(),
          );

      if (!usersLinked) return false;
    return true;
  }
}

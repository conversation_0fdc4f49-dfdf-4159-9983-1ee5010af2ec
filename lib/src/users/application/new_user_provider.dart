import 'dart:math';
import 'package:hooks_riverpod/hooks_riverpod.dart';

final newUserProvider = Provider<NewUserService>((ref) {
  return NewUserService();
});

class NewUserService {
  String generateRandomPassword({int length = 8}) {
    final random = Random.secure();

    const upperCase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowerCase = 'abcdefghijklmnopqrstuvwxyz';
    const digits = '**********';
    const special = r'!@#$%^&*()-_=+[]{};:/?<>';

    const allChars = upperCase + lowerCase + digits + special;

    final passChars = <String>[
      upperCase[random.nextInt(upperCase.length)],
      lowerCase[random.nextInt(lowerCase.length)],
      digits[random.nextInt(digits.length)],
      special[random.nextInt(special.length)],
    ];

    for (var i = 0; i < length - 4; i++) {
      passChars.add(allChars[random.nextInt(allChars.length)]);
    }

    passChars.shuffle(random);

    return passChars.join();
  }
}

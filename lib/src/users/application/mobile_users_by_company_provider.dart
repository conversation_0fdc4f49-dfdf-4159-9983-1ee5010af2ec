import 'dart:async';

import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/global_state_manager/global_state_manager.dart';

final mobileUsersByCompanyProvider = AutoDisposeAsyncNotifierProvider<
    MobileUsersByCompanyProvider, PaginatedDataModel<User>>(
  MobileUsersByCompanyProvider.new,
);

class MobileUsersByCompanyProvider extends AutoDisposeAsyncNotifier<PaginatedDataModel<User>>
    with PaginationMixin<User> {
  @override
  FutureOr<PaginatedDataModel<User>> build() async {
    final selectedCompany = ref.watch(globalStateManagerProvider.select((state) => state.selectedCompany));
    if (selectedCompany == null) {
      return const PaginatedDataModel<User>();
    }
    return getData(
      filters: {'company_id': selectedCompany.id},
      paginationType: PaginationType.mobileUsersByCompany,
    );
  }

  Future<void> fetchNextMobileUsers() async {
    final selectedCompany = ref.watch(globalStateManagerProvider.select((state) => state.selectedCompany));
    await getNextData(
      filters: {'company_id': selectedCompany!.id},
    );
  }
}

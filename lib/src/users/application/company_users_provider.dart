import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/global_state_manager/global_state_manager.dart';

final companyUsersAsyncNotifierProvider =
    AutoDisposeAsyncNotifierProvider<CompanyUsersAsyncNotifier, PaginatedDataModel<CompanyUserModel>>(
  CompanyUsersAsyncNotifier.new,
);

class CompanyUsersAsyncNotifier extends AutoDisposeAsyncNotifier<PaginatedDataModel<CompanyUserModel>> with PaginationMixin<CompanyUserModel> {
  Company? company;
  @override
  Future<PaginatedDataModel<CompanyUserModel>> build() async {
    company = ref.watch(globalStateManagerProvider.select((state) => state.selectedCompany));
    return getData(filters: {'company_id': company!.id});
  }

  Future<void> fetchPage(int page) async {
    final newData = await getData(filters: {'company_id': company!.id}, page: page);
    state = AsyncData(newData);
  }

  Future<void> toggleUserActive(User editUser) async {
    if (state.value == null) {
      return;
    }
    final newStatus = !editUser.isActive!;
    final editedUser = editUser.copyWith(isActive: newStatus);
    await ref.read(userRepositoryProvider).update(editedUser);
    state = AsyncData(
      state.value!.copyWith(
        items: state.value!.items.map(
          (companyuser) =>
              companyuser.user.id == editUser.id ? companyuser.copyWith(user: editedUser) : companyuser,
        ).toList(),
      ),
    );
  }
}

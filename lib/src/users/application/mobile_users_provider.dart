import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/global_state_manager/global_state_manager.dart';

final mobileUsersFutureProvider = FutureProvider.autoDispose<List<MobileUser>>  ((ref) async {
  final selectedCompany = ref.watch(globalStateManagerProvider.select((state) => state.selectedCompany));
  final companyId = selectedCompany?.id;
  if (companyId == null) {
    return [];
  }
  return ref.read(mobileUsersRepositoryProvider).fetchMobileUsersByCompanyId(companyId);
});

final unLinkedMobileUsersFutureProvider = FutureProvider.autoDispose<List<MobileUser>>  ((ref) async {
  final selectedCompany = ref.watch(globalStateManagerProvider.select((state) => state.selectedCompany));
  final companyId = selectedCompany?.id;
  if (companyId == null) {
    return [];
  }
  return ref.read(mobileUsersRepositoryProvider).fetchUnlinkedMobileUsersByCompanyId(companyId);
});

final mobileDevicesFutureProvider = FutureProvider.autoDispose<List<MobileDevice>>  ((ref) async {
  final selectedCompany = ref.watch(globalStateManagerProvider.select((state) => state.selectedCompany));
  final companyId = selectedCompany?.id;
  if (companyId == null) {
    return [];
  }
  return ref.read(mobileDeviceRepositoryProvider).fetchMobileDevicesByCompanyId(companyId);
});


import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';

enum NewUserType {
  active,
  potential,
}

extension NewUserTypeX on NewUserType {
  String label(BuildContext context) {
    switch (this) {
      case NewUserType.active:
        return context.tr.active;
      case NewUserType.potential:
        return context.tr.potential;
    }
  }
}

class DisplayNewUserType {
  DisplayNewUserType({
    required this.newUserType,
    required BuildContext context,
  }) : label = newUserType.label(context);

  final NewUserType newUserType;
  final String label;

  @override
  String toString() => label;

  static List<DisplayNewUserType> getList(BuildContext context) =>
      NewUserType.values
          .map(
            (type) => DisplayNewUserType(
              newUserType: type,
              context: context,
            ),
          )
          .toList();
}

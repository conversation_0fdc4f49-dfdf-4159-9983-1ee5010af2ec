import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';

enum WorkType {
  contracted,
  shortTerm,
  fullTime,
  partTime,
}

extension WorkTypeX on WorkType {
  String label(BuildContext context) {
    switch (this) {
      case WorkType.contracted:
        return context.tr.contracted;
      case WorkType.shortTerm:
        return context.tr.shortTerm;
      case WorkType.fullTime:
        return context.tr.fullTime;
      case WorkType.partTime:
        return context.tr.partTime;
    }
  }
}

class DisplayWorkType {
  DisplayWorkType({
    required this.newUserType,
    required BuildContext context,
  }) : label = newUserType.label(context);

  final WorkType newUserType;
  final String label;

  @override
  String toString() => label;

  static List<DisplayWorkType> getList(BuildContext context) => WorkType.values
      .map(
        (type) => DisplayWorkType(
          newUserType: type,
          context: context,
        ),
      )
      .toList();
}

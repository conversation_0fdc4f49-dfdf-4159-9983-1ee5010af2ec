import 'package:flutter/material.dart';

//TODO(Ali): s=>critical , u=>ali , m=> fix this via theme of ST
class AColor {
  static const Color white = Colors.white;
  static const Color black = Colors.black;
  static const Color primaryColor = Color.fromRGBO(230, -0, 1, 1);
  static const Color secondaryColor = Color(0xFFbd0000);
  static const Color backgroundColor = Color.fromARGB(255, 245, 245, 245); //Colors.grey.shade100;
  static const Color textColor = Color(0xFF333333); // #333333
  static const Color separatorColor = Color(0xFFD2D2D2); // #D2D1D1
  static const Color lightGreyBackground = Color(0xFFF5F5F8); // #F5F5F8
  static const Color linkColor = Color(0xFF336E72); // #336E72
  static const Color pinBorderColor = Color(0xFFD2D1D1); // #D2D1D1
  static const Color containerColor = Color(0xccffffff); // #0xccffffff
  static const Color switchActiveColor = Color(0xFFA692DE); // #A692DE
  static const Color subscriptionBoxBackground = Color(0xFFD9D9D9); // #D9D9D9
  static const Color transparent = Colors.transparent;
  static const Color inactiveColor = Color(0xFFADACAC); //#ADACAC
  static const Color calenderBackground = Color(0xFFF5F5F5); //#F5F5F5
  static const Color red = Color(0xFFDC2525); //#DC2525
  static const Color green = Color(0xFF34C85A); //#34C85A
  static const Color trackColor = Color(0xFF9E9999); //#9E9999
  static const Color grey = Colors.grey;
  static const Color blue = Colors.blue;
  static const Color lightGrey = Color(0xFFF5F5F5);
  static const Color loginBorderColor = Color(0xFFc6c6c6); //#C6C6C6
  static const Color grayMedium = Color(0xFF9F9F9F); //#9F9F9F
  static const Color tertiary = Color(0xFF4a4d4e);
  static const Color scienceBlue = Color(0xFF005CC8);
  static const Color doveGray = Color(0xFF656565); //#656565
  static const Color dustyGray = Color(0xFF999999); //#999999
  static const Color mineShaft = Color(0xFF353535); //#353535
  static const Color mercury = Color(0xFFE8E8E8);

  static var primary; //#E8E8E8
}

part of 'theme_provider.dart';

TooltipThemeData _getTooltipThemeData() {
  return TooltipThemeData(
    preferBelow: false,
    textStyle: ATextStyle.text12,
    verticalOffset: 20,
    decoration: BoxDecoration(
      color: AColor.mercury,
      borderRadius: BorderRadius.circular(24),
    ),
  );
}

SwitchThemeData _getSwitchThemeData() {
  return SwitchThemeData(
    thumbColor: WidgetStateProperty.resolveWith<Color>((states) {
      return AColor.white;
    }),
    trackColor: WidgetStateProperty.resolveWith<Color>((states) {
      return states.contains(WidgetState.selected)
          ? AColor.primaryColor
          : AColor.grey;
    }),
    trackOutlineColor: WidgetStateProperty.resolveWith<Color>((states) {
      return states.contains(WidgetState.selected)
          ? AColor.primaryColor
          : Colors.grey;
    }),
  );
}

ThemeData lightTheme = ThemeData(
  brightness: Brightness.light,
  scaffoldBackgroundColor: AColor.lightGreyBackground,
  tooltipTheme: _getTooltipThemeData(),
  colorScheme: const ColorScheme.light(
    primary: AColor.primaryColor,
    secondary: AColor.white,
  ),
  splashFactory: NoSplash.splashFactory,
  splashColor: AColor.transparent,
  hoverColor: AColor.transparent,
  highlightColor: AColor.transparent,
  expansionTileTheme: const ExpansionTileThemeData(
    shape: Border(),
  ),
  switchTheme: _getSwitchThemeData(),
  dialogTheme: const DialogThemeData(
    backgroundColor: AColor.white,
    shape: RoundedRectangleBorder(),
  ),
  textTheme: GoogleFonts.rubikTextTheme().apply(
    bodyColor: AColor.black,
    displayColor: AColor.black,
  ),
);

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/environment/environment.dart';
import 'package:smart_team_web/core/local_storage/storage_key.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/theme/colors.dart';

part 'dark_theme.dart';
part 'light_theme.dart';

final themeModeProvider =
    StateNotifierProvider<ThemeModeController, ThemeMode>((ref) {
  return ThemeModeController(ref);
});

class ThemeModeController extends StateNotifier<ThemeMode> {
  ThemeModeController(this.ref) : super(ThemeMode.system) {
    _init();
  }

  final Ref ref;

  /// Read saved theme mode from cache
  Future<void> _init() async {
    final cache = ref.read(cacheManagerProvider);

    final themeIndex = cache.read<int?>(
      key: AppStorageKeys.themeMode,
    );
    if (themeIndex != null &&
        themeIndex >= 0 &&
        themeIndex < ThemeMode.values.length) {
      state = ThemeMode.values[themeIndex];
    }
  }

  /// Switch to dark mode and persist
  void darkMode() {
    state = ThemeMode.dark;
    _updateStorage();
  }

  /// Switch to light mode and persist
  void lightMode() {
    state = ThemeMode.light;
    _updateStorage();
  }

  Future<void> _updateStorage() async {
    final cache = ref.read(cacheManagerProvider);

    await cache.write<int>(
      key: AppStorageKeys.themeMode,
      value: state.index,
    );
  }
}

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:gap/gap.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/online_user_manager/online_user_manager.dart';
import 'package:smart_team_web/core/router/auto_router.gr.dart';
import 'package:smart_team_web/src/customers/presentation/add_new_customer_widgets.dart';
import 'package:smart_team_web/src/devices/presentation/add_new_mobile_user_dialog_contents.dart';
import 'package:smart_team_web/src/devices/presentation/company_mobile_users_list.dart';
import 'package:smart_team_web/src/devices/presentation/new_device_widgets.dart';
import 'package:smart_team_web/src/home/<USER>/dashboard_constants.dart';
import 'package:smart_team_web/src/home/<USER>/dashboard_screen_view.dart';
import 'package:smart_team_web/src/main/application/main_provider.dart';
import 'package:smart_team_web/src/rules/presentation/add_new_rules_widgets.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/constants/constants.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/riverpod_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/shared/providers/countries_provider.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/users/presentation/company_users_list.dart';
import 'package:smart_team_web/src/users/presentation/new_user_widgets.dart';
import 'package:smart_team_web/src/widgets/app_dialog/app_dialog.dart';
import 'package:smart_team_web/src/widgets/app_dialog/premade_dialogs/feature_dialog.dart';
import 'package:smart_team_web/src/widgets/app_overlay.dart';
import 'package:smart_team_web/src/widgets/base_async_provider_widget.dart';
import 'package:smart_team_web/src/widgets/employee_tree_view/employee_tree_view.dart';
import 'package:smart_team_web/src/widgets/hover_builder.dart';
import 'package:smart_team_web/src/widgets/language_switcher.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

part 'components/a_app_bar.dart';
part 'components/custom_header_actions.dart';
part 'components/drawer_menu/drawer_menu.dart';
part 'components/drawer_menu/drawer_menu_item.dart';
part 'components/user_profile_button.dart';
part 'components/dialogs/logout_dialog.dart';

@RoutePage(name: 'MainRoute')
class MainView extends StatefulHookConsumerWidget {
  const MainView({super.key});

  @override
  ConsumerState<MainView> createState() => _MainViewState();
}

class _MainViewState extends ConsumerState<MainView> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  @override
  Widget build(BuildContext context) {
    final countries = ref.watch(countryFutureProvider);

    final isSmallScreen = context.width <= kMidScreenBreakpoint;
    return Portal(
      child: AutoTabsRouter(
        routes: [
          //Main
          const DashboardRoute(),
          const ReportsRoute(),
          const CalendarRoute(),
          const JobListRoute(),
          const FormsRoute(),
          const SettingsRoute(),
          const PerformanceRoute(),
          const PermitsRoute(),
          const CostsRoute(),
          const PersonalManagementRoute(),
          //Admin related
          const AdminRoute(),
          // Not included in side menu
          const MyProfileRoute(),
          DynamicMapRoute(),
          const PersonalDetailRoute(),
        ],
        builder: (context, child) {
          return BaseAsyncProviderWidget(
            value: countries,
            loadingWidget: const SizedBox.expand(
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),
            builder: (countries) {
              return Scaffold(
                key: _scaffoldKey,
                drawer: isSmallScreen
                    ? _EmployeeTreeDrawer()
                    : Drawer(
                        width: DashboardConstants.leftContainerWidth(context),
                        child: const LeftContainer(),
                      ),
                endDrawer: const _DrawerMenu(),
                appBar: _AAppBar(scaffoldKey: _scaffoldKey),
                body: Column(
                  children: [
                    Expanded(
                      child: child,
                    ),
                    Container(
                      color: AColor.primaryColor,
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: TextWidget(
                          'Akıllı Konum Teknolojileri A.Ş. © 2025'.hardcoded,
                          style: ATextStyle.small.copyWith(
                            color: AColor.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}

class _EmployeeTreeDrawer extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedMembers = ref.watch(selectedMembersProvider);

    return Drawer(
      width: context.responsive(
        mobile: context.width * 0.5,
        tablet: 150,
        desktop: 200,
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Align(
              alignment: Alignment.centerRight,
              child: InkWell(
                onTap: () => context.pop(),
                child: const Icon(
                  Icons.close,
                  color: AColor.primaryColor,
                ),
              ),
            ),
            Expanded(
              child: EmployeeTreeView(
                selectedMembers: selectedMembers,
                onTeamMemberSelected: (members) {
                  ref.read(selectedMembersProvider.notifier).state = members;
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

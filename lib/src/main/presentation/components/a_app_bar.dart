part of '../main_view.dart';

class _AAppBar extends HookConsumerWidget implements PreferredSizeWidget {
  const _AAppBar({required this.scaffoldKey});

  final GlobalKey<ScaffoldState> scaffoldKey;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabsRouter = AutoTabsRouter.of(context);
    final onlineUser = ref.futureValue(onlineUserManagerProvider);
    final isSmallScreen = context.width <= kMidScreenBreakpoint;
    final isDashboardRoute = tabsRouter.activeIndex == 0;

    return AppBar(
      leadingWidth: isSmallScreen && isDashboardRoute ? 250 : 200,
      leading: Row(
        children: [
          if (isSmallScreen && isDashboardRoute) ...[
            Padding(
              padding: const EdgeInsets.only(left: 8),
              child: Tooltip(
                message: 'Team Members'.hardcoded,
                child: InkWell(
                  onTap: () {
                    scaffoldKey.currentState?.openDrawer();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AColor.primaryColor,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AColor.white),
                    ),
                    child: const Icon(
                      Icons.group,
                      color: AColor.white,
                    ),
                  ),
                ),
              ),
            ),
          ],
          Expanded(
            child: InkWell(
              onTap: () {
                if (onlineUser?.userRole?.role.id == RoleTypeEnum.systemAdmin) {
                  tabsRouter.setActiveIndex(10);
                } else {
                  tabsRouter.setActiveIndex(0);
                }
              },
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: SmartTeamAssets.icons.beyazYatay3x.svg(),
              ),
            ),
          ),
        ],
      ),
      backgroundColor: AColor.primaryColor,
      iconTheme: const IconThemeData(
        color: AColor.white,
      ),
      actions: [
        _CustomHeaderActions(
          scaffoldKey: scaffoldKey,
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(58);
}

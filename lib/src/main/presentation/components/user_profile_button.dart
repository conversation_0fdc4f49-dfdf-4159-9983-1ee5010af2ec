part of '../main_view.dart';

class _UserProfileButton extends HookWidget {
  const _UserProfileButton();

  @override
  Widget build(BuildContext context) {
    final isOverlayOpen = useState(false);
    final tabsRouter = AutoTabsRouter.of(context);

    Widget buildTargetWidget() {
      return InkWell(
        onTap: () {
          isOverlayOpen.value = !isOverlayOpen.value;
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          color:
              isOverlayOpen.value ? AColor.secondaryColor : AColor.primaryColor,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            spacing: 8,
            children: [
              SmartTeamAssets.images.avatarWhite.image(width: 40, height: 40),
              Text(
                'Test Kullanıcı'.hardcoded,
                style:
                    ATextStyle.semiLargeRegular.copyWith(color: AColor.white),
              ),
            ],
          ),
        ),
      );
    }

    Widget buildFollowerItem({
      required String text,
      required VoidCallback onTap,
    }) {
      return HoverBuilder(
        onTap: onTap,
        builder: (isHovering, _) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 5),
            width: double.infinity,
            color: isHovering ? AColor.loginBorderColor : AColor.grayMedium,
            child: Text(
              text,
              style: ATextStyle.semiLargeRegular.copyWith(color: AColor.white),
            ),
          );
        },
      );
    }

    return AppOverlay(
      onOutsideClick: () {
        isOverlayOpen.value = false;
      },
      buildTrigger: (_) => buildTargetWidget(),
      isOpen: isOverlayOpen.value,
      buildOverlay: (_) => ColoredBox(
        color: AColor.grayMedium,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildFollowerItem(
              text: context.tr.myProfile,
              onTap: () {
                isOverlayOpen.value = false;
                tabsRouter.setActiveIndex(11);
              },
            ),
            buildFollowerItem(
              text: context.tr.exit,
              onTap: () {
                isOverlayOpen.value = false;
                context.router.replaceAll([const AuthRoute()]);
              },
            ),
          ],
        ),
      ),
    );
  }
}

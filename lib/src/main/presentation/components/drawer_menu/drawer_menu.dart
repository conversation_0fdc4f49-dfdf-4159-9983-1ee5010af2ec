part of '../../main_view.dart';

class _DrawerMenu extends HookConsumerWidget {
  const _DrawerMenu();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabsRouter = AutoTabsRouter.of(context);
    final onlineUser = ref.futureValue(onlineUserManagerProvider)!;

    return Drawer(
      width: 310,
      backgroundColor: AColor.white,
      child: Padding(
        padding: const EdgeInsets.only(
          top: 40,
          bottom: 16,
        ),
        child: Column(
          children: [
            SmartTeamAssets.images.kirmiziYatay.image(width: 200),
            const SizedBox(height: 20),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  children: [
                    _DrawerMenuItem(
                      svgPath: SmartTeamAssets.icons.panel.path,
                      text: context.tr.dashboard,
                      onPress: () {
                        tabsRouter.setActiveIndex(0);
                        context.maybePop();
                      },
                    ),
                    _DrawerMenuItem(
                      svgPath: SmartTeamAssets.icons.rules.path,
                      text: context.tr.rules,
                      onListClick: () {
                        Navigator.of(context).pop();
                        AppDialog.show<void>(
                          context: context,
                          width: context.width * .95,
                          height: context.height * .95,
                          title: context.tr.ruleList,
                          child: TableDialog(
                            columns: [
                              context.tr.title,
                              ''.hardcoded,
                            ],
                            data: const [],
                          ),
                        );
                      },
                      onAddClick: () {
                        Navigator.of(context).pop();
                        AppDialog.show<void>(
                          context: context,
                          title: context.tr.addNewRule,
                          width: context.width * .95,
                          height: context.height * .95,
                          child: const AddNewRuleWidgets(),
                        );
                      },
                    ),
                    _DrawerMenuItem(
                      svgPath: SmartTeamAssets.icons.customers.path,
                      text: context.tr.customers,
                      onListClick: () {
                        Navigator.of(context).pop();
                        AppDialog.show<void>(
                          context: context,
                          width: context.width * .95,
                          height: context.height * .95,
                          title: context.tr.device,
                          child: TableDialog(
                            columns: [
                              context.tr.customerName,
                              context.tr.contactName,
                              context.tr.deviceName,
                              context.tr.gsm,
                              context.tr.email,
                              context.tr.province,
                              context.tr.district,
                              '',
                            ],
                            data: const [],
                          ),
                        );
                      },
                      onAddClick: () {
                        Navigator.of(context).pop();
                        AppDialog.show<void>(
                          context: context,
                          title: context.tr.customer,
                          width: context.width * .95,
                          height: context.height * .95,
                          child: const AddNewCustomerWidgets(),
                        );
                      },
                    ),
                    _DrawerMenuItem(
                      svgPath: SmartTeamAssets.icons.devices.path,
                      text: context.tr.devices,
                      onListClick: () {
                        AppDialog.show<void>(
                          context: context,
                          width: context.width * .95,
                          height: context.height * .95,
                          title: context.tr.device,
                          child: const CompanyMobileUsersList(),
                        );
                      },
                      onAddClick: () {
                        AppDialog.show<void>(
                          context: context,
                          title: context.tr.newDevice,
                          width: context.width * .95,
                          height: context.height * .95,
                          child: const AddNewMobileUserDialogContents(),
                        );
                      },
                    ),
                    _DrawerMenuItem(
                      svgPath: SmartTeamAssets.icons.users.path,
                      text: context.tr.users,
                      onListClick: () {
                        Navigator.of(context).pop();
                        AppDialog.show<void>(
                          context: context,
                          width: context.width * .95,
                          height: context.height * .95,
                          title: context.tr.users,
                          child: const CompanyUsersList(),
                        );
                      },
                      onAddClick: () {
                              Navigator.of(context).pop();
                              AppDialog.show<void>(
                                context: context,
                                title: context.tr.newUser,
                                width: context.width * .95,
                                height: context.height * .95,
                                child: const NewUserWidgets(),
                              );
                            },
                    ),

                    /// Currently not available
                    _DrawerMenuItem(
                      svgPath: SmartTeamAssets.icons.reports.path,
                      text: context.tr.reports,
                      onPress: () {
                        tabsRouter.setActiveIndex(1);
                        context.maybePop();
                      },
                    ),
                    _DrawerMenuItem(
                      svgPath: SmartTeamAssets.icons.calendar.path,
                      text: context.tr.calendar,
                      onPress: () {
                        tabsRouter.setActiveIndex(2);
                        context.maybePop();
                      },
                    ),
                    _DrawerMenuItem(
                      svgPath: SmartTeamAssets.icons.jobList.path,
                      text: context.tr.jobList,
                      onPress: () {
                        context..navigateTo(const JobListRoute())
                        ..maybePop();
                      },
                    ),
                    _DrawerMenuItem(
                      svgPath: SmartTeamAssets.icons.forms.path,
                      text: context.tr.forms,
                      onPress: () {
                        context
                          ..navigateTo(const FormsRoute())
                          ..maybePop();
                      },
                    ),

                    /// Currently not available
                    _DrawerMenuItem(
                      svgPath: SmartTeamAssets.icons.performance.path,
                      text: context.tr.performance,
                      onPress: () {
                        tabsRouter.setActiveIndex(6);
                        context.maybePop();
                      },
                    ),

                    /// Currently not available
                    _DrawerMenuItem(
                      svgPath: SmartTeamAssets.icons.settings.path,
                      text: context.tr.permits,
                      onPress: () {
                        tabsRouter.setActiveIndex(7);
                        context.maybePop();
                      },
                    ),

                    /// Currently not available
                    _DrawerMenuItem(
                      svgPath: SmartTeamAssets.icons.expenses.path,
                      text: context.tr.costs,
                      onPress: () {
                        context..navigateTo(const CostsRoute())
                        ..maybePop();
                      },
                    ),

                    /// Currently not available
                    _DrawerMenuItem(
                      svgPath: SmartTeamAssets.icons.settings.path,
                      text: 'Çalışanlar'.hardcoded,
                      onPress: () {
                        Navigator.of(context).pop();
                        tabsRouter.setActiveIndex(9);
                      },
                    ),
                    _DrawerMenuItem(
                      svgPath: SmartTeamAssets.icons.settings.path,
                      text: context.tr.settings,
                      onPress: () {
                        tabsRouter.setActiveIndex(5);
                        context.maybePop();
                      },
                    ),
                  ],
                ),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                IconButton(
                  color: AColor.primaryColor,
                  icon: Row(
                    mainAxisSize: MainAxisSize.min,
                    spacing: 8,
                    children: [
                      const Icon(Icons.logout),
                      Text(
                        'Çıkış'.hardcoded,
                        style: ATextStyle.mediumRegular
                            .copyWith(color: AColor.primaryColor),
                      ),
                    ],
                  ),
                  onPressed: () async {
                    final isLogOutAccepted = await showDialog<bool>(
                      context: context,
                      builder: (context) => const _LogoutDialog(),
                    );
                    if (isLogOutAccepted != null && isLogOutAccepted) {
                      await ref
                          .read(onlineUserManagerProvider.notifier)
                          .signOutAndNavigateToLogin();
                    }
                  },
                ),
                const LanguageSwitcher(),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

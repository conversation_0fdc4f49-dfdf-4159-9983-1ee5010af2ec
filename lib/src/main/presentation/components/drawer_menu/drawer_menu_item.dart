part of '../../main_view.dart';

class _DrawerMenuItem extends StatelessWidget {
  const _DrawerMenuItem({
    required this.svgPath,
    required this.text,
    this.onPress,
    this.onListClick,
    this.onAddClick,
  });

  final String svgPath;
  final String text;
  final VoidCallback? onPress;
  final VoidCallback? onListClick;
  final VoidCallback? onAddClick;

  @override
  Widget build(BuildContext context) {
    return HoverBuilder(
      onTap: onPress,
      builder: (isHovering, _) {
        final color = isHovering ? AColor.primaryColor : AColor.mineShaft;
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: AColor.pinBorderColor,
              ),
            ),
          ),
          child: Row(
            children: [
              SizedBox(
                width: 18,
                height: 18,
                child: FittedBox(
                  child: SvgPicture.asset(
                    svgPath,
                    package: 'smart_team_common',
                    colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
                  ),
                ),
              ),
              const Gap(12),
              Text(
                text,
                style: ATextStyle.text13.copyWith(
                  color: color,
                ),
              ),
              const Spacer(),
              if (onListClick != null)
                Tooltip(
                  message: 'Listele',
                  preferBelow: true,
                  child: GestureDetector(
                    onTap: onListClick,
                    child: SizedBox(
                      width: 16,
                      child: FittedBox(
                        child: SmartTeamAssets.icons.list.svg(
                          package: 'smart_team_common',
                          colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
                        ),
                      ),
                    ),
                  ),
                ),
              if (onAddClick != null)
                Tooltip(
                  message: 'Ekle',
                  preferBelow: true,
                  margin: const EdgeInsets.only(left: 21),
                  child: GestureDetector(
                    onTap: onAddClick,
                    child: Container(
                      width: 16,
                      margin: const EdgeInsets.only(left: 20),
                      child: FittedBox(
                        child: SmartTeamAssets.icons.add.svg(
                          package: 'smart_team_common',
                          colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}

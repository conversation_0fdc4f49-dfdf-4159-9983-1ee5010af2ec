part of '../main_view.dart';

class _CustomHeaderActions extends HookConsumerWidget {
  const _CustomHeaderActions({required this.scaffoldKey});
  final GlobalKey<ScaffoldState> scaffoldKey;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selected = ref.watch(selectedCompanyProvider).value;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        spacing: 8,
        children: [
          const _UserProfileButton(),
          if (selected != null)
            InkWell(
              onTap: () {
                scaffoldKey.currentState?.openEndDrawer();
              },
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AColor.primaryColor,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AColor.white),
                ),
                child: const Icon(
                  Icons.menu,
                  color: Color.fromRGBO(255, 255, 255, 1),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

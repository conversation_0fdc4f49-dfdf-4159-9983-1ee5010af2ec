part of '../../main_view.dart';

class _LogoutDialog extends StatelessWidget {
  const _LogoutDialog();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('<PERSON><PERSON>k<PERSON>ş Yap'),
      content: const Text('<PERSON><PERSON><PERSON><PERSON><PERSON> yapmak istediğinize emin misiniz?'),
      actions: [
        TextButton(
          onPressed: context.maybePop,
          child: Text(
            'Hayır'.hardcoded,
            style:
                ATextStyle.mediumRegular.copyWith(
              color: AColor.black,
                ),
          ),
        ),
        TextButton(
          onPressed: () {
            context.maybePop(true);
          },
          child: Text(
            'Evet'.hardcoded,
            style:
                ATextStyle.mediumRegular.copyWith(color: AColor.primaryColor),
          ),
        ),
      ],
    );
  }
}

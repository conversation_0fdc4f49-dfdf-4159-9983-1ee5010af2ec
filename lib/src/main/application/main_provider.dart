import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/local_storage/storage_key.dart';
import 'package:smart_team_web/src/home/<USER>/mock_data.dart';
import 'package:smart_team_web/src/home/<USER>/models/all_team_list.dart';

final selectedCompanyProvider =
    AsyncNotifierProvider<SelectedCompanyNotifier, Company?>(
  SelectedCompanyNotifier.new,
);

class SelectedCompanyNotifier extends AsyncNotifier<Company?> {
  @override
  Future<Company?> build() async {
    final cache = ref.read(cacheManagerProvider);

    return cache.read<Company?>(key: AppStorageKeys.selectedCompany);
  }

  Future<void> selectCompany(Company company) async {
    state = AsyncData(company);
    final cache = ref.read(cacheManagerProvider);

    await cache.write<Company?>(
      key: AppStorageKeys.selectedCompany,
      value: company,
    );
  }

  Future<void> clearSelection() async {
    state = const AsyncData(null);
    final cache = ref.read(cacheManagerProvider);

    await cache.delete<Company?>(key: AppStorageKeys.selectedCompany);
  }
}

final selectedMembersProvider = StateProvider<List<TeamMemberModel>>((_) => []);

final allTeamProvider = StateProvider<List<AllTeamList>>(
  (_) => DashboardMockData.getMockAllTeamList(),
);

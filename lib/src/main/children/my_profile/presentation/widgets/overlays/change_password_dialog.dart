part of '../../my_profile_page.dart';

class _ChangePasswordDialog extends HookConsumerWidget {
  const _ChangePasswordDialog();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = ref.watch(formKeyProvider(FormType.changePassword));
    final newPassController = useTextEditingController();
    final newPassAgainController = useTextEditingController();

    Widget buildLabel(String text) {
      return Text(
        text,
        style: ATextStyle.mediumRegular.copyWith(
          color: AColor.tertiary,
        ),
      );
    }

    return Dialog(
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          maxWidth: 600,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: double.infinity,
              height: 45,
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
              color: AColor.primaryColor,
              child: <PERSON>gn(
                alignment: Alignment.centerRight,
                child: IconButton(
                  onPressed: context.maybePop,
                  icon: const Icon(
                    Icons.close,
                    color: AColor.white,
                    size: 20,
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 24),
              child: Form(
                key: formKey,
                child: Column(
                  spacing: 20,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    CustomTextFormField(
                      contentPadding: const EdgeInsets.all(10),
                      headerText: context.tr.oldPass,
                      headerTextStyle: ATextStyle.mediumRegular,
                      borderColor: AColor.black.withValues(alpha: .08),
                      validator: (value) => value.isValidPassword(context),
                    ),
                    CustomTextFormField(
                      contentPadding: const EdgeInsets.all(10),
                      headerText: context.tr.newPass,
                      headerTextStyle: ATextStyle.mediumRegular,
                      borderColor: AColor.black.withValues(alpha: .08),
                      validator: (value) => value.isValidPassword(context),
                    ),
                    CustomTextFormField(
                      contentPadding: const EdgeInsets.all(10),
                      headerText: context.tr.newPassAgain,
                      headerTextStyle: ATextStyle.mediumRegular,
                      borderColor: AColor.black.withValues(alpha: .08),
                      validator: (value) {
                        if (newPassController.text != value) {
                          return 'Şifreler eşleşmiyor'.hardcoded;
                        }
                        return value.isValidPassword(context);
                      },
                    ),
                    LoadingElevatedButton(
                      onPressed: () async {
                        formKey.currentState?.validate();
                        // TODO(Levent): Implement change password
                      },
                      height: 44,
                      text: context.tr.save,
                      backgroundColor: AColor.tertiary,
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}

part of '../my_profile_page.dart';

class _InputField extends StatelessWidget {
  const _InputField({required this.headerText});

  final String headerText;

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 8,
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          headerText,
          style: ATextStyle.mediumRegular,
        ),
        const TextField(
          cursorColor: AColor.black,
          decoration: InputDecoration(
            filled: true,
            fillColor: AColor.lightGreyBackground,
            border: OutlineInputBorder(
              borderSide: BorderSide.none,
            ),
          ),
        ),
      ],
    );
  }
}

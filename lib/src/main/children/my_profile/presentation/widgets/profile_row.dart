part of '../my_profile_page.dart';

class _ProfileRow extends StatelessWidget {
  const _ProfileRow();

  @override
  Widget build(BuildContext context) {
    Widget buildProfileAvatar() {
      return Container(
        width: 70,
        height: 70,
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
          color: AColor.primaryColor,
        ),
        child: Center(
          child: Text(
            'TK'.hardcoded,
            style: ATextStyle.headerRegular.copyWith(color: AColor.white),
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(40),
      child: Row(
        children: [
          buildProfileAvatar(),
          const Spacer(),
          Row(
            mainAxisSize: MainAxisSize.min,
            spacing: 16,
            children: [
              LoadingElevatedButton(
                onPressed: () async {
                  await showDialog<void>(
                    context: context,
                    barrierDismissible: false,
                    builder: (_) => const _ChangePasswordDialog(),
                  );
                },
                height: 44,
                text: context.tr.changePassword,
                backgroundColor: AColor.white,
                borderColor: AColor.primaryColor,
                textColor: AColor.primaryColor,
              ),
              LoadingElevatedButton(
                onPressed: () async {},
                height: 44,
                text: context.tr.save,
                backgroundColor: AColor.primaryColor,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

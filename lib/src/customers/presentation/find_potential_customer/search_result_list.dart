/* class SearchResultsList extends HookConsumerWidget {
  const SearchResultsList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchQuery = ref.watch(searchQueryProvider).trim();
    final results = ref.watch(searchResultsProvider);
    final notifier = ref.read(searchResultsProvider.notifier);
    final _scrollController = useScrollController();

    useEffect(
      () {
        void onScroll() {
          if (_scrollController.position.pixels >=
                  _scrollController.position.maxScrollExtent - 100 &&
              notifier.nextPageToken != null &&
              !notifier.isFetching) {
            notifier.searchCompanies(
              searchQuery,
              loadMore: true,
            );
          }
        }

        _scrollController.addListener(onScroll);
        return () => _scrollController.removeListener(onScroll);
      },
      [_scrollController, notifier.isFetching, notifier.nextPageToken],
    );

    if (searchQuery.isEmpty || results.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        Expanded(
          child: Scrollbar(
            thumbVisibility: true,
            interactive: true,
            controller: _scrollController,
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(vertical: 4),
              itemCount:
                  results.length + (notifier.nextPageToken != null ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == results.length && notifier.nextPageToken != null) {
                  return const Padding(
                    padding: EdgeInsets.all(8),
                    child: Center(child: CircularProgressIndicator()),
                  );
                }

                final company = results[index];
                return Container(
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(250),
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: [
                      BoxShadow(
                        color: AColor.black.withAlpha(100),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ListTile(
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    title: TextWidget(
                      company.name,
                      style: ATextStyle.semiSmall.copyWith(color: AColor.black),
                    ),
                    subtitle: TextWidget(
                      company.address,
                      style: ATextStyle.small.copyWith(),
                    ),
                    onTap: () {
                      ref.read(selectedCompanyProvider.notifier).state =
                          company;
                      ref
                          .read(mapProvider.notifier)
                          .updateLocation(company.location);
                    },
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
 */

/* import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/customers/domain/customer_map_state.dart';
import 'package:smart_team_web/src/customers/domain/map_company_model.dart';

final mapProvider = StateNotifierProvider<MapNotifier, CustomerMapState>(
  (ref) => MapNotifier(),
);

final selectedCompanyProvider = StateProvider<MapCompany?>((ref) => null);

class MapNotifier extends StateNotifier<CustomerMapState> {
  MapNotifier()
      : super(
          CustomerMapState(
            location: const LatLng(38.448556, 27.395010),
            circleRadius: 2000,
          ),
        );

  void updateCircleRadius(double newRadius) {
    state = state.copyWith(circleRadius: newRadius);
  }

  void updateLocation(LatLng newLocation) {
    state = state.copyWith(location: newLocation);
  }
}
 */

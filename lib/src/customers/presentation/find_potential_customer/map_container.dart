/* 
class MapContainer extends HookConsumerWidget {
  const MapContainer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mapState = ref.watch(mapProvider);
    final mapNotifier = ref.read(mapProvider.notifier);
    final isLoading = ref.watch(isLoadingProvider);
    final errorMessage = ref.watch(searchErrorProvider);
    final containerHeight = context.height * 0.3;
    DateTime? lastTapTime;

    final searchController = useTextEditingController();

    final buttonPressTimestamps = ref.watch(buttonPressTimestampsProvider);
    final buttonPressTimestampsNotifier =
        ref.read(buttonPressTimestampsProvider.notifier);

    final currentTime = useState(DateTime.now());
    useEffect(
      () {
        final timer = Timer.periodic(const Duration(seconds: 1), (timer) {
          currentTime.value = DateTime.now();

          final updatedTimestamps = [
            ...buttonPressTimestamps,
          ]..removeWhere(
              (time) =>
                  currentTime.value.difference(time) >
                  const Duration(minutes: 1),
            );

          if (updatedTimestamps.length != buttonPressTimestamps.length) {
            buttonPressTimestampsNotifier.state = updatedTimestamps;
          }
        });
        return timer.cancel;
      },
      [],
    );

    final isRateLimited = buttonPressTimestamps.length >= 5;

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AColor.grey),
        borderRadius: BorderRadius.circular(4),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: SizedBox(
          height: containerHeight,
          child: Stack(
            children: [
              GoogleMap(
                zoomGesturesEnabled: false,
                initialCameraPosition: CameraPosition(
                  target: mapState.location,
                  zoom: 15,
                ),
                markers: {
                  Marker(
                    markerId: const MarkerId('location'),
                    position: mapState.location,
                  ),
                  ...ref.watch(searchResultsProvider).map(
                        (company) => Marker(
                          markerId: MarkerId(company.id),
                          position: company.location,
                          infoWindow: InfoWindow(title: company.name),
                        ),
                      ),
                },
                circles: {
                  Circle(
                    circleId: const CircleId('radiusCircle'),
                    center: mapState.location,
                    radius: mapState.circleRadius,
                    fillColor: AColor.blue.withAlpha(100),
                    strokeColor: AColor.blue,
                    strokeWidth: 2,
                  ),
                },
                onTap: (latLng) {
                  mapNotifier.updateLocation(latLng);
                  final now = DateTime.now();
                  if (lastTapTime != null &&
                      now.difference(lastTapTime!) <
                          const Duration(milliseconds: 300)) {
                    debugPrint('Double Tap -> $latLng');
                    mapNotifier.updateLocation(latLng);
                    lastTapTime = null;
                  } else {
                    lastTapTime = now;
                  }
                },
                onMapCreated: (controller) {},
              ),
              Positioned(
                top: containerHeight * 0.03,
                left: context.width * 0.01,
                right: context.width * 0.50,
                child: Row(
                  spacing: 8,
                  children: [
                    Expanded(
                      child: CustomTextFormField(
                        controller: searchController,
                        hintText: context.tr.search,
                        prefixIcon: HoverColoredIcon(
                          icon: Icons.search,
                          onSelected: (_) {},
                        ),
                        onChanged: (value) {
                          ref.read(searchQueryProvider.notifier).state = value;
                        },
                      ),
                    ),
                    LoadingElevatedButton(
                      width: 80,
                      height: 35,
                      onPressed: isRateLimited
                          ? null
                          : () async {
                              final now = DateTime.now();
                              buttonPressTimestampsNotifier.state = [
                                ...buttonPressTimestamps,
                                now,
                              ];

                              // Arama sorgusunu güncelle ve aramayı yap
                              final query = searchController.text.trim();
                              if (query.isNotEmpty) {
                                ref.read(searchQueryProvider.notifier).state =
                                    query;
                                await ref
                                    .read(searchResultsProvider.notifier)
                                    .searchCompanies(query);
                              } else {
                                // Boş arama yapılıyorsa sonuçları temizle
                                ref
                                    .read(searchResultsProvider.notifier)
                                    .clearResults();
                              }
                            },
                      isLoading: isLoading,
                      text: context.tr.search,
                      textStyle: ATextStyle.small,
                    ),
                  ],
                ),
              ),
              Positioned(
                top: containerHeight * 0.10,
                left: context.width * 0.01,
                right: context.width * 0.50,
                child: isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : (errorMessage != null
                        ? TextWidget(
                            errorMessage,
                            style: const TextStyle(color: AColor.red),
                          )
                        : const SizedBox.shrink()),
              ),
              Positioned(
                top: containerHeight * 0.15,
                left: context.width * 0.01,
                right: context.width * 0.50,
                bottom: 60,
                child: const SearchResultsList(),
              ),
              Positioned(
                bottom: 10,
                left: 50,
                right: 50,
                child: Slider(
                  value: mapState.circleRadius,
                  min: 100,
                  max: 5000,
                  divisions: 49,
                  label: '${mapState.circleRadius.toStringAsFixed(0)} m',
                  onChanged: mapNotifier.updateCircleRadius,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
 */

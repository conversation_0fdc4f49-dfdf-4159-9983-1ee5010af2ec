/* import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/customers/domain/map_company_model.dart';
import 'package:smart_team_web/src/customers/presentation/map_provider.dart';
import 'package:smart_team_web/src/customers/repository/places_repository.dart';

final searchQueryProvider = StateProvider<String>((ref) => '');
final searchResultsProvider =
    StateNotifierProvider<SearchResultsNotifier, List<MapCompany>>(
  (ref) => SearchResultsNotifier(ref.watch(placesRepositoryProvider), ref),
);

final searchErrorProvider = StateProvider<String?>((ref) => null);
final isLoadingProvider = StateProvider<bool>((ref) => false);

final buttonPressTimestampsProvider =
    StateProvider<List<DateTime>>((ref) => []);

class SearchResultsNotifier extends StateNotifier<List<MapCompany>> {
  SearchResultsNotifier(this._repository, this._ref) : super([]);

  final PlacesRepository _repository;
  final Ref _ref;
  String? _nextPageToken;
  bool _isFetching = false;

  String? get nextPageToken =>
      (_nextPageToken?.isEmpty ?? true) ? null : _nextPageToken;

  bool get isFetching => _isFetching;

  Future<void> fetchPlaces({
    required String keyword,
    required double latitude,
    required double longitude,
    required double radius,
    bool loadMore = false,
  }) async {
    if ((loadMore && (_nextPageToken == null)) || _isFetching) return;

    _isFetching = true;

    if (!loadMore) {
      _ref.read(isLoadingProvider.notifier).state = true;
    }

    _ref.read(searchErrorProvider.notifier).state = null;

    try {
      if (loadMore) {
        await Future.delayed(const Duration(seconds: 2));
      }

      final responseModel = await _repository.fetchPlaces(
        keyword: keyword,
        latitude: latitude,
        longitude: longitude,
        radius: radius,
        nextPageToken: loadMore ? _nextPageToken : null,
      );

      _nextPageToken = responseModel.nextPageToken;

      final newPlaces = responseModel.results.map((place) {
        return MapCompany(
          id: place.placeId,
          name: place.name,
          location: LatLng(
            place.geometry.location.lat,
            place.geometry.location.lng,
          ),
          address: place.vicinity,
        );
      }).toList();

      if (loadMore) {
        state = [...state, ...newPlaces];
      } else {
        state = newPlaces;
      }
    } catch (e) {
      _ref.read(searchErrorProvider.notifier).state = e.toString();
      debugPrint('Error occurred during search: $e');
    } finally {
      _isFetching = false;
      _ref.read(isLoadingProvider.notifier).state = false;
    }
  }

  Future<void> searchCompanies(String keyword, {bool loadMore = false}) async {
    if (keyword.trim().isEmpty && !loadMore) {
      state = [];
      return;
    }

    final mapState = _ref.read(mapProvider);
    await fetchPlaces(
      keyword: keyword,
      latitude: mapState.location.latitude,
      longitude: mapState.location.longitude,
      radius: mapState.circleRadius,
      loadMore: loadMore,
    );
  }

  void clearResults() {
    state = [];
    _nextPageToken = null;
  }
}
 */

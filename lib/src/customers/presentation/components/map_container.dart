part of '../add_new_customer_widgets.dart';

class MapContainer extends HookConsumerWidget {
  const MapContainer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final containerHeight = context.height * 0.3;

    final selectedLocation =
        ref.watch(addCustomerNotifierProvider.select((state) => state.selectedPlaceDetails));

    Future<void> onLocationClicked(LatLng location) async {
      await ref.read(addCustomerNotifierProvider.notifier).setPlaceDetailsByLocation(location);
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AColor.grey),
        borderRadius: BorderRadius.circular(4),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: SizedBox(
          height: containerHeight,
          child: Stack(
            children: [
              Positioned(
                top: 0,
                right: 0,
                left: 0,
                bottom: 0,
                child: AppMap(
                  location: selectedLocation?.location,
                  onLocationClicked: onLocationClicked,
                ),
              ),

              // <PERSON><PERSON> alani
              <PERSON>(
                top: 14,
                left: 14,
                child: PointerInterceptor(
                  child: const SearchPlaces(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/customers/application/add_customer_notifier.dart';
import 'package:smart_team_web/src/customers/application/country_state_province_notifiers.dart';
import 'package:smart_team_web/src/customers/domain/country_state_city/country_state_city_model.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/providers/countries_provider.dart';
import 'package:smart_team_web/src/widgets/base_async_provider_widget.dart';
import 'package:smart_team_web/src/widgets/dropdown/a_new_dropdown.dart';

class LocationSelectionFormItems extends HookConsumerWidget {
  const LocationSelectionFormItems({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifier = ref.read(addCustomerNotifierProvider.notifier);
    final countries = ref.read(countryFutureProvider).requireValue;

    final provincesAsync = ref.watch(provinceFutureProvider);
    final citiesAsync = ref.watch(cityFutureProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 8,
      children: [
        Consumer(
          builder: (context, innerRef, child) {
            final selectedCountry =
                innerRef.watch(addCustomerNotifierProvider.select((state) => state.country));
            return ANewDropDown<Country>(
              header: 'Ülke',
              placeholder: context.tr.select,
              itemList: countries,
              selectedItems: selectedCountry == null ? [] : [selectedCountry],
              enableSearch: true,
              onSelected: notifier.setCountry,
            );
          },
        ),

        // İl dropdown
        BaseAsyncProviderWidget<List<StateModel>>(
          value: provincesAsync,
          loadingWidget: const Padding(
            padding: EdgeInsets.all(8),
            child: Text('İl listesi yükleniyor...'),
          ),
          errorBuilder: (e, st) => Text('İl listesi hata: $e'),
          builder: (provinces) {
            return Consumer(
              builder: (context, innerRef, child) {
                final selectedProvince =
                    innerRef.watch(addCustomerNotifierProvider.select((state) => state.province));
                return ANewDropDown<StateModel>(
                  header: 'İl',
                  placeholder: context.tr.select,
                  itemList: provinces,
                  selectedItems: selectedProvince == null ? [] : [selectedProvince],
                  enableSearch: true,
                  onSelected: notifier.setProvince,
                );
              },
            );
          },
        ),

        BaseAsyncProviderWidget<List<City>>(
          value: citiesAsync,
          loadingWidget: const Padding(
            padding: EdgeInsets.all(8),
            child: Text('İlçe listesi yükleniyor...'),
          ),
          errorBuilder: (e, st) => Text('İlçe listesi hata: $e'),
          builder: (cities) {
            return Consumer(
              builder: (context, innerRef, child) {
                final selectedCity =
                    innerRef.watch(addCustomerNotifierProvider.select((state) => state.city));
                return ANewDropDown<City>(
                  header: 'İlçe',
                  placeholder: context.tr.select,
                  itemList: cities,
                  selectedItems: selectedCity == null ? [] : [selectedCity],
                  enableSearch: true,
                  onSelected: notifier.setCity,
                );
              },
            );
          },
        ),
      ],
    );
  }
}

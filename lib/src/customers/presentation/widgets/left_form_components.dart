import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/customers/application/add_customer_notifier.dart';
import 'package:smart_team_web/src/customers/presentation/widgets/location_selection_form_items.dart';
import 'package:smart_team_web/src/shared/enums/regex_type.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_extensions.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';

class LeftFormComponents extends HookConsumerWidget {
  const LeftFormComponents({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final locationController = useTextEditingController();
    final streetController = useTextEditingController();
    final postalCodeController = useTextEditingController();
    final buildingDoorController = useTextEditingController();
    final addressController = useTextEditingController();
    final addressDirectionsController = useTextEditingController();

    ref.listen(addCustomerNotifierProvider.select((state) => state.selectedPlaceDetails), (previous, next) {
      if (next == null) return;
      final newLocation = '${next.location.latitude},${next.location.longitude}';
      if (newLocation != locationController.text) {
        locationController.text = newLocation;
      }
      streetController.text = next.addressDetails.street?.longName ?? '';
      postalCodeController.text = next.addressDetails.postalCode?.longName ?? '';
      buildingDoorController.text = next.addressDetails.streetNumber?.longName ?? '';
      addressController.text = next.formattedAddress;
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 8,
      children: [
        CustomTextFormField(
          controller: locationController,
          headerText: context.tr.location,
          validator: (value) => value.isValidLatLng(context),
          autovalidateMode: AutovalidateMode.onUserInteraction,
          onChanged: (value) {
            final latLng = value.toLatLng();
            if (latLng != null) {
              ref.read(addCustomerNotifierProvider.notifier).setLocation(latLng);
            }
          },
        ),
        const LocationSelectionFormItems(),
        Row(
          spacing: 16,
          children: [
            Expanded(
              flex: 5,
              child: CustomTextFormField(
                headerText: context.tr.avenueStreet,
                controller: streetController,
                validator: (value) => value.isValidField(context),
                onChanged: ref.read(addCustomerNotifierProvider.notifier).setStreet,
              ),
            ),
            Expanded(
              child: CustomTextFormField(
                headerText: context.tr.postalCode,
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.next,
                controller: postalCodeController,
                onChanged: ref.read(addCustomerNotifierProvider.notifier).setPostalCode,
                validator: (value) => value?.isValidField(context),
                regexType: RegexType.postalCode,
              ),
            ),
          ],
        ),
        CustomTextFormField(
          controller: buildingDoorController,
          headerText: context.tr.buildingDoor,
          validator: (value) => value.isValidField(context),
          onChanged: ref.read(addCustomerNotifierProvider.notifier).setStreetNumber,
        ),
        CustomTextFormField(
          controller: addressController,
          maxLines: 2,
          headerText: context.tr.address,
          validator: (value) => value.isValidField(context),
          onChanged: ref.read(addCustomerNotifierProvider.notifier).setFormattedAddress,
        ),
        CustomTextFormField(
          controller: addressDirectionsController,
          headerText: context.tr.addressDirections,
          maxLines: 4,
          onChanged: ref.read(addCustomerNotifierProvider.notifier).setAddressDirections,
        ),
      ],
    );
  }
}

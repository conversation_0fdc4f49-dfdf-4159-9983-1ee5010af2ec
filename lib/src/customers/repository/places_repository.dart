import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/core/environment/environment.dart';
import 'package:smart_team_web/src/customers/domain/search_result/search_result.dart';
import 'package:smart_team_web/src/customers/repository/places_api_service.dart';

final placesApiServiceProvider = Provider<PlacesApiService>((ref) {
  return PlacesApiService(
    cloudFunctionUrl: ref.watch(environmentProvider).cloudFunctionsBaseUrl,
  );
});

final placesRepositoryProvider = Provider<PlacesRepository>((ref) {
  final placesService = ref.watch(placesApiServiceProvider);
  return PlacesRepository(placesService);
});

final googlePlacesRepositoryProvider = Provider<PlacesRepository>(
  (ref) {
    final placesService = ref.watch(placesApiServiceProvider);
    return PlacesRepository(placesService);
  },
);

class PlacesRepository {
  PlacesRepository(this._placesService);
  final PlacesApiService _placesService;

  Future<List<SearchResult>> searchPlaces(String query) async {
    try {
      final response = await _placesService.searchPlaces(query);
      if (response.status == 'OK' && response.results.isNotEmpty) {
        return response.results
            .map(
              (place) => SearchResult(
                id: place.placeId,
                name: place.name,
                location: place.location,
                address: place.address,
              ),
            )
            .toList();
      }
      return [];
    } catch (e) {
      throw Exception('Error searching places: $e');
    }
  }
}

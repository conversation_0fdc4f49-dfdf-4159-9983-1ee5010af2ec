import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:smart_team_web/src/customers/domain/google_place_details_model.dart';
import 'package:smart_team_web/src/customers/domain/google_predictions_model.dart';
import 'package:smart_team_web/src/customers/domain/map/google_place_model.dart';
import 'package:smart_team_web/src/shared/extensions/iterable_extensions.dart';

class PlacesApiService {
  PlacesApiService({
    required String cloudFunctionUrl,
    http.Client? client,
  })  : _cloudFunctionUrl = cloudFunctionUrl,
        _client = client ?? http.Client();

  final String _cloudFunctionUrl;
  final http.Client _client;

  Future<GooglePlaceResponse> searchPlaces(String query) async {
    final encodedQuery = Uri.encodeComponent(query);
    final url = '$_cloudFunctionUrl?query=$encodedQuery';

    final response = await _client
        .get(
      Uri.parse(url),
    )
        .catchError((e) {
      debugPrint('Error in proxy request: $e');
      throw Exception('Network error: $e');
    });

    if (response.statusCode == 200) {
      final jsonResponse = json.decode(response.body) as Map<String, dynamic>;
      debugPrint('Proxy Json response: $jsonResponse');
      return GooglePlaceResponse.fromJson(jsonResponse);
    } else {
      throw Exception('Proxy search failed: ${response.statusCode}');
    }
  }

  Future<GooglePredictionsModel> autoComplete(String query) async {
    final encodedQuery = Uri.encodeComponent(query);
    final url = '$_cloudFunctionUrl/autocompletePlace?input=$encodedQuery';

    final response = await _client
        .get(
      Uri.parse(url),
    )
        .catchError((e) {
      debugPrint('Error in proxy request: $e');
      throw Exception('Network error: $e');
    });

    if (response.statusCode == 200) {
      final jsonResponse = json.decode(response.body) as Map<String, dynamic>;
      debugPrint('Proxy Json response: $jsonResponse');
      return GooglePredictionsModel.fromJson(jsonResponse);
    } else {
      throw Exception('Proxy search failed: ${response.statusCode}');
    }
  }

  Future<GooglePlaceDetailsModel> getPlaceDetails(String placeId) async {
    final encodedQuery = Uri.encodeComponent(placeId);
    final url = '$_cloudFunctionUrl/getPlaceGeometryAndAddress?placeId=$encodedQuery';

    final response = await _client
        .get(
      Uri.parse(url),
    )
        .catchError((e) {
      debugPrint('Error in proxy request: $e');
      throw Exception('Network error: $e');
    });

    if (response.statusCode == 200) {
      final jsonResponse = json.decode(response.body) as Map<String, dynamic>;
      debugPrint('Proxy Json response: $jsonResponse');
      final placeDetails = GooglePlaceDetailsModel.fromJson(jsonResponse);
      if (placeDetails.result != null) {
        return placeDetails.copyWith.result!(formattedAddress: placeDetails.result!.correctAddress);
      }
      return placeDetails;
    } else {
      throw Exception('Proxy search failed: ${response.statusCode}');
    }
  }

  Future<PlaceDetails?> reverseGeocode(LatLng latLng) async {
    final url = '$_cloudFunctionUrl/reverseGeocode?latitude=${latLng.latitude}&longitude=${latLng.longitude}';

    final response = await _client
        .get(
      Uri.parse(url),
    )
        .catchError((e) {
      debugPrint('Error in proxy request: $e');
      throw Exception('Network error: $e');
    });

    if (response.statusCode == 200) {
      final jsonResponse = json.decode(response.body) as Map<String, dynamic>;
      debugPrint('Proxy Json response: $jsonResponse');
      final reverseGeocode = GoogleReverseGeocodeModel.fromJson(jsonResponse);
      final placeDetails = reverseGeocode.results.firstWhereOrNull(
        (result) => result.addressComponents.any((component) => component.types.contains('country')),
      );
      return placeDetails?.copyWith(formattedAddress: placeDetails.correctAddress);
    } else {
      throw Exception('Proxy search failed: ${response.statusCode}');
    }
  }
}

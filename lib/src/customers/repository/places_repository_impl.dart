/* import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/customers/domain/response/response_model.dart';
import 'package:smart_team_web/src/customers/repository/places_repository.dart';
import 'package:smart_team_web/src/shared/managers/network_manager/network_manager.dart';
import 'package:smart_team_web/src/shared/services/firebase_functions_endpoint/firebase_function_endpoint.dart';

class PlacesRepositoryImpl extends IPlacesRepository {
  PlacesRepositoryImpl(this.ref);

  final Ref ref;

  @override
  Future<ResponseModel> fetchPlaces({
    required String keyword,
    required double latitude,
    required double longitude,
    required double radius,
    String? nextPageToken,
  }) async {
    final response = await ref.read(networkManagerProvider).get<Map<String, dynamic>>(
      FirebaseFunctionEndpoint.searchPlace.getEndpoint(),
      queryParameters: {
        'latitude': latitude,
        'longitude': longitude,
        'radius': radius,
        'keyword': keyword,
        if (nextPageToken != null) 'nextPageToken': nextPageToken,
      },
    );
    return ResponseModel.fromJson(response.data!);
  }
}
 */

import 'dart:html' as html;
import 'package:google_maps_flutter/google_maps_flutter.dart';

class BrowserLocationService {
  static Future<LatLng?> getUserLocation() async {
    try {
      final position =
          await html.window.navigator.geolocation.getCurrentPosition(
        enableHighAccuracy: true,
        timeout: const Duration(milliseconds: 5000),
        maximumAge: Duration.zero,
      );

      final lat = position.coords!.latitude;
      final lng = position.coords!.longitude;
      return LatLng((lat ?? 0).toDouble(), (lng ?? 0).toDouble());
    } catch (e) {
      return null;
    }
  }
}

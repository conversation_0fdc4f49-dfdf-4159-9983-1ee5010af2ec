import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/core/environment/environment.dart';
import 'package:smart_team_web/src/customers/domain/country_state_city/country_state_city_model.dart';
import 'package:smart_team_web/src/customers/repository/add_new_customer_repository_impl.dart';

final addNewCustomerRepositoryProvider = Provider<AddNewCustomerRepository>(
  (ref) => AddNewCustomerRepositoryImpl(
    apiKey: ref.watch(environmentProvider).countryStateCityApiKey,
  ),
);

abstract class AddNewCustomerRepository {
  Future<List<Country>> getCountries();
  Future<List<StateModel>> getStatesByCountry(String countryIso2);
  Future<List<City>> getCitiesByStatesCountry(
    String countryIso2,
    String stateIso2,
  );
  Future<LatLng> fetchCurrentUserLocation();
}

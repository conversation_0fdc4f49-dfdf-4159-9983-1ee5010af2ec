import 'dart:convert';

import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:smart_team_web/core/environment/environment.dart';
import 'package:smart_team_web/src/customers/application/map_provider.dart';

// Reverse geocoding provider - from coordinates to address
final reverseGeocodingProvider =
    FutureProvider.family<ReverseGeocodeResult?, LatLng>(
  (ref, latLng) async {
    try {
      final apiKey = ref.watch(environmentProvider).googleMapsApiKey;
      final repository = ReverseGeocodingRepository(googleMapsApiKey: apiKey);
      return await repository.reverseGeocode(
        latitude: latLng.latitude,
        longitude: latLng.longitude,
      );
    } catch (e) {
      // Handle errors
      ref.read(searchErrorProvider.notifier).state =
          'Reverse geocoding failed: $e';
      return null;
    }
  },
);

// Forward geocoding provider - from address to coordinates
final geocodingProvider = FutureProvider.family<LatLng?, String>(
  (ref, address) async {
    try {
      final apiKey = ref.watch(environmentProvider).googleMapsApiKey;
      // Implementation of geocoding service
      final repository = GeocodingRepository(googleMapsApiKey: apiKey);
      return await repository.geocodeAddress(address);
    } catch (e) {
      // Handle errors
      ref.read(searchErrorProvider.notifier).state = 'Geocoding failed: $e';
      return null;
    }
  },
);

// Implementation for geocoding (address to coordinates)
class GeocodingRepository {
  GeocodingRepository({required this.googleMapsApiKey});
  final String googleMapsApiKey;

  Future<LatLng?> geocodeAddress(String address) async {
    try {
      final url =
          'https://maps.googleapis.com/maps/api/geocode/json?address=${Uri.encodeComponent(address)}&key=$googleMapsApiKey&language=tr';
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final jsonBody = json.decode(response.body) as Map<String, dynamic>;

        if (jsonBody['status'] == 'OK') {
          final results = jsonBody['results'] as List<dynamic>;
          if (results.isNotEmpty) {
            final location =
                results[0]['geometry']['location'] as Map<String, dynamic>;
            final lat = location['lat'] as double;
            final lng = location['lng'] as double;
            return LatLng(lat, lng);
          }
        }
      }
      return null;
    } catch (e) {
      throw Exception('Geocoding request failed: $e');
    }
  }
}

class ReverseGeocodeResult {
  ReverseGeocodeResult({
    this.countryIso2,
    this.countryName,
    this.stateName,
    this.cityName,
  });
  final String? countryIso2;
  final String? countryName;
  final String? stateName;
  final String? cityName;
}

class ReverseGeocodingRepository {
  ReverseGeocodingRepository({required this.googleMapsApiKey});

  final String googleMapsApiKey;

  Future<ReverseGeocodeResult> reverseGeocode({
    required double latitude,
    required double longitude,
  }) async {
    final url =
        'https://maps.googleapis.com/maps/api/geocode/json?latlng=$latitude,$longitude&key=$googleMapsApiKey&language=tr';

    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      final jsonBody = json.decode(response.body) as Map<String, dynamic>;
      if (jsonBody['status'] == 'OK') {
        return _parseGeocodeResult(jsonBody);
      } else {
        throw Exception('Geocoding failed: ${jsonBody['status']}');
      }
    } else {
      throw Exception('Geocoding request failed: ${response.statusCode}');
    }
  }

  ReverseGeocodeResult _parseGeocodeResult(Map<String, dynamic> jsonBody) {
    final results = jsonBody['results'] as List<dynamic>;

    String? countryIso2;
    String? countryName;
    String? stateName;
    String? cityName;

    for (final item in results) {
      // "address_components" listesini al, List<dynamic> olarak cast et
      final addressComponents = item['address_components'] as List<dynamic>;

      for (final comp in addressComponents) {
        // "types" alanı da bir liste; List<dynamic> olarak cast ediyoruz
        final List<dynamic> types = comp['types'] as List<dynamic>;

        // longName ve shortName, String değerler
        final String longName = comp['long_name'] as String;
        final String shortName = comp['short_name'] as String;

        // "country" yakala
        if (types.contains('country')) {
          countryIso2 = shortName; // Örn. "TR"
          countryName = longName; // Örn. "Türkiye"
        }

        // "administrative_area_level_1" (genelde il/eyalet)
        if (types.contains('administrative_area_level_1')) {
          stateName = longName;
        }

        // "administrative_area_level_2" (genelde ilçe)
        // bazen "locality" city adı olarak gelebilir
        if (types.contains('administrative_area_level_2')) {
          cityName = longName;
        }
        if (types.contains('locality')) {
          cityName = longName;
        }
      }
    }

    return ReverseGeocodeResult(
      countryIso2: countryIso2,
      countryName: countryName,
      stateName: stateName,
      cityName: cityName,
    );
  }
}

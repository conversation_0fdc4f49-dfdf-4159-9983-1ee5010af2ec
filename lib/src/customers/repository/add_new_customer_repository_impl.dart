import 'dart:async';
import 'dart:convert';
import 'dart:js_interop';
import 'package:web/web.dart' as web;
import 'package:google_maps_flutter_platform_interface/src/types/location.dart';
import 'package:http/http.dart' as http;
import 'package:smart_team_web/src/customers/domain/country_state_city/country_state_city_model.dart';
import 'package:smart_team_web/src/customers/repository/add_new_customer_repository.dart';

class AddNewCustomerRepositoryImpl implements AddNewCustomerRepository {
  AddNewCustomerRepositoryImpl({required this.apiKey});

  final String apiKey;
  final String baseUrl = 'https://api.countrystatecity.in/v1';

  @override
  Future<List<Country>> getCountries() async {
    final url = '$baseUrl/countries';
    final response = await http.get(
      Uri.parse(url),
      headers: {
        'X-CSCAPI-KEY': apiKey,
      },
    );
    if (response.statusCode == 200) {
      final jsonList = json.decode(response.body) as List<dynamic>;
      return jsonList.map((json) => Country.fromJson(json as Map<String, dynamic>)).toList();
    } else {
      throw Exception(response.reasonPhrase);
    }
  }

  @override
  Future<List<StateModel>> getStatesByCountry(String countryIso2) async {
    final url = '$baseUrl/countries/$countryIso2/states';
    final response = await http.get(
      Uri.parse(url),
      headers: {
        'X-CSCAPI-KEY': apiKey,
      },
    );
    if (response.statusCode == 200) {
      final jsonList = json.decode(response.body) as List<dynamic>;
      return jsonList.map((json) => StateModel.fromJson(json as Map<String, dynamic>)).toList();
    } else {
      throw Exception(response.reasonPhrase);
    }
  }

  @override
  Future<List<City>> getCitiesByStatesCountry(String countryIso2, String stateIso2) async {
    final url = '$baseUrl/countries/$countryIso2/states/$stateIso2/cities';
    final response = await http.get(
      Uri.parse(url),
      headers: {
        'X-CSCAPI-KEY': apiKey,
      },
    );
    if (response.statusCode == 200) {
      final jsonList = json.decode(response.body) as List<dynamic>;
      return jsonList.map((json) => City.fromJson(json as Map<String, dynamic>)).toList();
    } else {
      throw Exception(response.reasonPhrase);
    }
  }

  @override
  Future<LatLng> fetchCurrentUserLocation() async {
    final completer = Completer<web.GeolocationPosition>();
    web.window.navigator.geolocation.getCurrentPosition(
      // We can't use a lambda here because it's not serializable
      // ignore: unnecessary_lambdas
      (web.GeolocationPosition result) {
        completer.complete(result);
      }.toJS,
      () {
        completer.completeError(Exception('location error'));
      }.toJS,
      web.PositionOptions(
        enableHighAccuracy: true,
        timeout: 5000,
      ),
    );
    final position = await completer.future;

    return LatLng(position.coords.latitude, position.coords.longitude);
  }
}

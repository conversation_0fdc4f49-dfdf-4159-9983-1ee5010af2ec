import 'dart:developer';

import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:smart_team_common/smart_team_common.dart' hide Country;
import 'package:smart_team_web/core/managers/global_state_manager/global_state_manager.dart';
import 'package:smart_team_web/src/customers/application/country_state_province_notifiers.dart';
import 'package:smart_team_web/src/customers/domain/add_customer_state/add_customer_state.dart';
import 'package:smart_team_web/src/customers/domain/country_state_city/country_state_city_model.dart';
import 'package:smart_team_web/src/customers/domain/google_place_details_model.dart';
import 'package:smart_team_web/src/customers/repository/add_new_customer_repository.dart';
import 'package:smart_team_web/src/customers/repository/places_repository.dart';
import 'package:smart_team_web/src/customers/repository/reverse_geocoding_repository.dart';
import 'package:smart_team_web/src/shared/extensions/iterable_extensions.dart';
import 'package:smart_team_web/src/shared/providers/countries_provider.dart';

final addCustomerNotifierProvider = AutoDisposeNotifierProvider<AddCustomerNotifier, AddCustomerState>(
  AddCustomerNotifier.new,
);

class AddCustomerNotifier extends AutoDisposeNotifier<AddCustomerState> {
  @override
  AddCustomerState build() => AddCustomerState();

  void setCustomerName(String? customerName) {
    state = state.copyWith(customerName: customerName);
  }

  void setAuthorizedName(String? authorizedName) {
    state = state.copyWith(authorizedName: authorizedName);
  }

  void setAuthorizedGSM(String? authorizedGSM) {
    state = state.copyWith(authorizedGSM: authorizedGSM);
  }

  void setAuthorizedEmail(String? authorizedEmail) {
    state = state.copyWith(authorizedEmail: authorizedEmail);
  }

  void setContactName(String? contactName) {
    state = state.copyWith(contactName: contactName);
  }

  void setContactGSM(String? contactGSM) {
    state = state.copyWith(contactGSM: contactGSM);
  }

  void setContactEmail(String? contactEmail) {
    state = state.copyWith(contactEmail: contactEmail);
  }

  void setAddressDirections(String? addressDirections) {
    state = state.copyWith(addressDirections: addressDirections);
  }

  void addOrRemoveAssignedToUsers(User user) {
    final users = state.assignedToUsers;
    final indexOfUser = users.indexWhere((u) => u.id == user.id);
    if (indexOfUser != -1) {
      state = state.copyWith(
        assignedToUsers: users.where((u) => u.id != user.id).toList(),
      );
    } else {
      state = state.copyWith(
        assignedToUsers: [...users, user],
      );
    }
  }

  void setStatus(CustomerStatusEnum? status) {
    state = state.copyWith(status: status);
  }

  void setSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
  }

  void setCountry(Country? country) {
    state = state.copyWith(country: country, province: null, city: null);
  }

  void setProvince(StateModel? province) {
    state = state.copyWith(province: province, city: null);
  }

  void setCity(City? city) {
    state = state.copyWith(city: city);
  }

  void setLocation(LatLng location) {
    state = state.copyWith(location: location);
  }

  void setStreet(String? street) {
    state = state.copyWith(street: street);
  }

  void setPostalCode(String? postalCode) {
    state = state.copyWith(postalCode: postalCode);
  }

  void setStreetNumber(String? streetNumber) {
    if (streetNumber == state.streetNumber) return;
    state = state.copyWith(streetNumber: streetNumber);
  }

  void setFormattedAddress(String? formattedAddress) {
    state = state.copyWith(formattedAddress: formattedAddress);
  }

  void setIsLoading({required bool isLoading}) {
    state = state.copyWith(isLoading: isLoading);
  }

  void _setSelectedPlaceDetails(PlaceDetails placeDetails) {
    if (placeDetails == state.selectedPlaceDetails) return;
    state = state.copyWith(
      selectedPlaceDetails: placeDetails,
      formattedAddress: placeDetails.formattedAddress,
      street: placeDetails.addressDetails.street?.longName,
      streetNumber: placeDetails.addressDetails.streetNumber?.longName,
      postalCode: placeDetails.addressDetails.postalCode?.longName,
      location: placeDetails.location,
    );
  }

  Future<void> setPlaceDetailsById(String placeId) async {
    setIsLoading(isLoading: true);
    final placeDetails = await _fetchPlaceDetailsById(placeId);
    if (placeDetails == null) return;
    await parsePlaceDetails(placeDetails);
  }

  Future<void> setPlaceDetailsByLocation(LatLng location) async {
    state = state.copyWith(location: location, isLoading: true);
    final placeDetails = await _fetchPlaceDetailsByLocation(location);
    if (placeDetails == null) return;
    await parsePlaceDetails(placeDetails);
  }

  Future<void> showCurrentLocation() async {
    setIsLoading(isLoading: true);
    final location = await ref.read(addNewCustomerRepositoryProvider).fetchCurrentUserLocation();
    await setPlaceDetailsByLocation(location);
  }

  Future<void> parsePlaceDetails(PlaceDetails placeDetails) async {
    try {
      _setSelectedPlaceDetails(placeDetails);
      final addressDetails = placeDetails.addressDetails;

      final selectedCountry = _setCountryFromAddressDetails(addressDetails.country);
      if (selectedCountry == null) return;

      final selectedProvince = await _setProvinceFromAddressDetails(addressDetails.province);
      if (selectedProvince == null) return;

      final selectedCity = await _setCityFromAddressDetails(addressDetails.city);
      if (selectedCity == null) return;
    } finally {
      setIsLoading(isLoading: false);
    }
  }

  Future<PlaceDetails?> _fetchPlaceDetailsById(String placeId) async {
    try {
      final placeDetails = await ref.read(placesApiServiceProvider).getPlaceDetails(placeId);
      return placeDetails.result;
    } catch (e) {
      return null;
    }
  }

  Future<PlaceDetails?> _fetchPlaceDetailsByLocation(LatLng location) async {
    try {
      return await ref.read(placesApiServiceProvider).reverseGeocode(location);
    } catch (e) {
      return null;
    }
  }

  Country? _setCountryFromAddressDetails(AddressComponentModel? country) {
    final countries = ref.read(countryFutureProvider).requireValue;
    final selectedCountry = countries.firstWhereOrNull((c) => country?.shortName == c.iso2);
    setCountry(selectedCountry);
    return selectedCountry;
  }

  Future<StateModel?> _setProvinceFromAddressDetails(AddressComponentModel? province) async {
    final provinces = await ref.read(provinceFutureProvider.future);
    final selectedProvince =
        provinces.firstWhereOrNull((p) => province?.longName.toLowerCase() == p.name.toLowerCase());
    setProvince(selectedProvince);
    return selectedProvince;
  }

  Future<City?> _setCityFromAddressDetails(AddressComponentModel? city) async {
    final cities = await ref.read(cityFutureProvider.future);
    final selectedCity = cities.firstWhereOrNull((c) => city?.longName.toLowerCase() == c.name.toLowerCase());
    setCity(selectedCity);
    return selectedCity;
  }

  Future<void> updateLocation(LatLng latLng) async {
    final reverseResult = await ref.read(reverseGeocodingProvider(latLng).future);
    if (reverseResult == null) return;

    if (reverseResult.countryIso2 != null) {
      await setCountryFromGeocode(reverseResult.countryIso2!);
    }
    if (reverseResult.stateName != null) {
      await setStateFromGeocode(reverseResult.stateName!);
    }
    if (reverseResult.cityName != null) {
      await setCityFromGeocode(reverseResult.cityName!);
    }
  }

  Future<void> setCountryFromGeocode(String iso2OrName) async {
    final allCountries = await ref.read(countryFutureProvider.future);
    if (allCountries.isEmpty) return;

    final normalized = normalizeTR(iso2OrName);
    final matched = allCountries.firstWhereOrNull(
      (c) => normalizeTR(c.iso2) == normalized || normalizeTR(c.name) == normalized,
    );
    if (matched != null) {
      setCountry(matched);
    }
  }

  Future<void> setStateFromGeocode(String stateName) async {
    final states = await ref.read(provinceFutureProvider.future);
    if (states.isEmpty) return;

    final normalized = normalizeTR(stateName);
    final matchedState = states.firstWhereOrNull(
      (s) => normalizeTR(s.name) == normalized,
    );
    if (matchedState != null) {
      setProvince(matchedState);
    }
  }

  Future<void> setCityFromGeocode(String cityName) async {
    final cityList = await ref.read(cityFutureProvider.future);
    if (cityList.isEmpty) return;

    final normalized = normalizeTR(cityName);
    final matchedCity = cityList.firstWhereOrNull(
      (c) => normalizeTR(c.name) == normalized,
    );
    if (matchedCity != null) {
      setCity(matchedCity);
    }
  }

  Future<bool> saveCustomer() async {
    final selectedCompany = ref.read(globalStateManagerProvider.select((state) => state.selectedCompany));
    if (selectedCompany == null) return false;
    final data = state.toJson(companyId: selectedCompany.id!);
    log(data.toString());
    try {
      await ref.read(customerRepositoryProvider).createCustomerWithData(data);
      return true;
    } catch (e) {
      log(e.toString());
      return false;
    }
  }
}

//TODO move extensions etc.
String normalizeTR(String input) {
  return input
      .toLowerCase()
      .replaceAll('ğ', 'g')
      .replaceAll('ü', 'u')
      .replaceAll('ş', 's')
      .replaceAll('ı', 'i')
      .replaceAll('ö', 'o')
      .replaceAll('ç', 'c');
}

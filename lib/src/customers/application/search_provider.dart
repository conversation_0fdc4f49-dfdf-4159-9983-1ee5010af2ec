import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/customers/application/add_customer_notifier.dart';
import 'package:smart_team_web/src/customers/application/add_new_customer_provider.dart';
import 'package:smart_team_web/src/customers/application/address_fields_notifier.dart';
import 'package:smart_team_web/src/customers/application/map_provider.dart';
import 'package:smart_team_web/src/customers/domain/address_field/address_fields.dart';
import 'package:smart_team_web/src/customers/domain/google_predictions_model.dart';
import 'package:smart_team_web/src/customers/domain/search_result/search_result.dart';
import 'package:smart_team_web/src/customers/repository/places_repository.dart';

final googleAutocompleteResultsProvider = FutureProvider.autoDispose<GooglePredictionsModel>((ref) async {
  final query = ref.watch(addCustomerNotifierProvider.select((state) => state.searchQuery));
  if (query.length < 4) return const GooglePredictionsModel(predictions: []);
  final repository = ref.read(placesApiServiceProvider);
  return repository.autoComplete(query);
});

final searchResultsProvider = NotifierProvider<SearchResultsNotifier, List<SearchResult>>(
  SearchResultsNotifier.new,
);

class SearchResultsNotifier extends Notifier<List<SearchResult>> {
  @override
  List<SearchResult> build() => [];

  Future<void> searchCompanies(String query) async {
    final ref = this.ref;
    ref.read(isLoadingProvider.notifier).state = true;
    ref.read(searchErrorProvider.notifier).state = null;

    try {
      final repository = ref.read(placesRepositoryProvider);
      final results = await repository.searchPlaces(query);

      if (results.isEmpty) {
        ref.read(searchErrorProvider.notifier).state = 'No results found';
      }

      state = results;
      ref.read(isLoadingProvider.notifier).state = false;
    } catch (e) {
      ref.read(searchErrorProvider.notifier).state = 'Search failed: $e';
      ref.read(isLoadingProvider.notifier).state = false;
      state = [];
    }
  }

  void clearResults() {
    state = [];
    ref.read(searchQueryProvider.notifier).state = '';
    ref.read(searchErrorProvider.notifier).state = null;

    ref.read(isLoadingProvider.notifier).state = false;
  }

  Future<void> selectResult(SearchResult result) async {
    ref.read(mapProvider.notifier).updateLocation(result.location, zoom: 15, force: true);
    await ref.read(addCustomerNotifierProvider.notifier).updateLocation(result.location);
    ref.read(countryStateSelectionProvider.notifier).updateSelectedLocation(result.location);

    final addressComponents = parseAddress(result.address);

    ref.read(addressFieldsProvider.notifier).updateAddress(
          street: addressComponents.street,
          buildingDoor: addressComponents.buildingDoor,
          postalCode: addressComponents.postalCode,
          fullAddress: result.address,
        );

    ref.read(searchQueryProvider.notifier).state = result.address;

    final controller = ref.read(mapControllerProvider);
    if (controller != null) {
      await controller.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: result.location,
            zoom: 15,
          ),
        ),
      );
    }
  }
}

AddressFields parseAddress(String fullAddress) {
  var street = '';
  var buildingDoor = '';
  var postalCode = '';

  final doorNumberRegex = RegExp(r'\b(no|nu)([.:])?( ?\d+(\/\d+)?)', caseSensitive: false);
  final doorMatch = doorNumberRegex.firstMatch(fullAddress);

  if (doorMatch != null) {
    buildingDoor = doorMatch.group(0) ?? '';

    final doorIndex = doorMatch.start;
    if (doorIndex > 0) {
      street = fullAddress.substring(0, doorIndex).trim();
    }
  } else {
    street = '';
  }

  final postalCodeRegex = RegExp(r'\b\d{5,6}\b');
  final postalMatch = postalCodeRegex.firstMatch(fullAddress);

  if (postalMatch != null) {
    postalCode = postalMatch.group(0) ?? '';
  }

  return AddressFields(
    street: street,
    buildingDoor: buildingDoor,
    postalCode: postalCode,
    fullAddress: fullAddress,
  );
}

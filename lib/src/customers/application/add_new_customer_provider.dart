import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/customers/application/map_provider.dart';
import 'package:smart_team_web/src/customers/domain/country_state_city/country_state_city_model.dart';
import 'package:smart_team_web/src/customers/repository/add_new_customer_repository.dart';
import 'package:smart_team_web/src/customers/repository/places_repository.dart';
import 'package:smart_team_web/src/customers/repository/reverse_geocoding_repository.dart';

final resetFormFieldsProvider = StateProvider<bool>((ref) => false);

final countryStateSelectionProvider =
    NotifierProvider.autoDispose<CountryStateSelectionNotifier, CountryStateSelection>(
  CountryStateSelectionNotifier.new,
);

class CountryStateSelection {
  CountryStateSelection({
    this.selectedCountry,
    this.selectedState,
    this.selectedCity,
    this.states = const [],
    this.cities = const [],
    this.selectedLocation,
  });

  final Country? selectedCountry;
  final StateModel? selectedState;
  final City? selectedCity;
  final List<StateModel> states;
  final List<City> cities;
  final LatLng? selectedLocation;

  CountryStateSelection copyWith({
    Country? selectedCountry,
    StateModel? selectedState,
    City? selectedCity,
    List<StateModel>? states,
    List<City>? cities,
    LatLng? selectedLocation,
  }) {
    return CountryStateSelection(
      selectedCountry: selectedCountry ?? this.selectedCountry,
      selectedState: selectedState ?? this.selectedState,
      selectedCity: selectedCity ?? this.selectedCity,
      states: states ?? this.states,
      cities: cities ?? this.cities,
      selectedLocation: selectedLocation ?? this.selectedLocation,
    );
  }
}

class CountryStateSelectionNotifier extends AutoDisposeNotifier<CountryStateSelection> {
  @override
  CountryStateSelection build() {
    ref.onDispose(resetSelections);
    const defaultCountry = Country(id: 1, name: 'Türkiye', iso2: 'TR');
    return CountryStateSelection(selectedCountry: defaultCountry);
  }

  Future<void> setCountry(Country? country) async {
    if (country == null) {
      state = state.copyWith(
        states: [],
        cities: [],
      );
      return;
    }

    final repository = ref.read(addNewCustomerRepositoryProvider);
    final states = await repository.getStatesByCountry(country.iso2);

    // Debug amaçlı bakın:
    print("GELEN IL LISTESI: ${states.map((e) => e.name).toList()}");

    if (country.iso2.toUpperCase() == 'TR') {
      final pinned = ['Ankara', 'İstanbul', 'İzmir'];

      states.sort((a, b) {
        final aName = a.name.toLowerCase();
        final bName = b.name.toLowerCase();

        final aPinnedIndex = pinned.indexWhere(
          (pin) => pin.toLowerCase() == aName,
        );
        final bPinnedIndex = pinned.indexWhere(
          (pin) => pin.toLowerCase() == bName,
        );

        final aPinned = aPinnedIndex != -1;
        final bPinned = bPinnedIndex != -1;

        // a pinned, b değil => a önce
        if (aPinned && !bPinned) return -1;
        // b pinned, a değil => b önce
        if (!aPinned && bPinned) return 1;

        // ikisi de pinned ise pinned içindeki sıra
        if (aPinned && bPinned) {
          return aPinnedIndex.compareTo(bPinnedIndex);
        }

        // Hiçbiri pinned değilse alfabetik sırala
        return aName.compareTo(bName);
      });
    } else {
      // TR değilse sadece alfabetik sırala
      states.sort((a, b) => a.name.toLowerCase().compareTo(b.name.toLowerCase()));
    }

    state = state.copyWith(
      selectedCountry: country,
      states: states,
      cities: [],
    );
  }

  Future<void> setState(StateModel? selectedState) async {
    if (selectedState == null) {
      state = state.copyWith(
        cities: [],
      );
      return;
    }

    final country = state.selectedCountry;
    if (country == null) return;

    final repository = ref.read(addNewCustomerRepositoryProvider);
    final cities = await repository.getCitiesByStatesCountry(country.iso2, selectedState.iso2);

    state = state.copyWith(
      selectedState: selectedState,
      cities: cities,
    );
  }

  void setCity(City? city) {
    final country = state.selectedCountry;
    final state_ = state.selectedState;

    if (city != null && country != null && state_ != null) {
      // Harita konumunu güncelle
      ref.read(mapProvider.notifier).updateLocationByPlace(
            countryCode: country.iso2,
            stateCode: state_.iso2,
            cityName: city.name,
            forceUpdate: true,
          );
    }

    state = state.copyWith(
      selectedCity: city,
    );
  }

  // Geocode sonuçlarından ülke bilgisini ayarlamak için
  Future<void> setCountryFromGeocode(Country country) async {
    await setCountry(country);
  }

  // Geocode sonuçlarından il bilgisini ayarlamak için
  Future<void> setStateFromGeocode(String stateName) async {
    final matchingState = state.states.firstWhere(
      (state) => state.name.toLowerCase() == stateName.toLowerCase(),
    );

    if (matchingState != null) {
      await setState(matchingState);
    }
  }

  // Geocode sonuçlarından ilçe bilgisini ayarlamak için
  void setCityFromGeocode(String cityName) {
    final matchingCity = state.cities.firstWhere(
      (city) => city.name.toLowerCase() == cityName.toLowerCase(),
    );

    if (matchingCity != null) {
      setCity(matchingCity);
    }
  }

  void updateSelectedLocation(LatLng location) {
    ref.read(reverseGeocodingProvider(location)).whenData((result) {
      if (result != null) {
        state = state.copyWith(selectedLocation: location);

        if (result.countryIso2 != null && result.countryName != null) {
          final country = Country(id: 0, name: result.countryName!, iso2: result.countryIso2!);
          setCountryFromGeocode(country);
        }
        if (result.stateName != null) {
          try {
            setStateFromGeocode(result.stateName!);
          } catch (e) {
            print(e);
          }
        }
        if (result.cityName != null) {
          setCityFromGeocode(result.cityName!);
        }
      }
    });
  }

  void resetSelections() {
    state = CountryStateSelection();
  }
}

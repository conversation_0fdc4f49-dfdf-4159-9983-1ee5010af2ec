import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/customers/application/add_customer_notifier.dart';
import 'package:smart_team_web/src/customers/domain/country_state_city/country_state_city_model.dart';
import 'package:smart_team_web/src/customers/repository/add_new_customer_repository.dart';

final provinceFutureProvider = FutureProvider.autoDispose<List<StateModel>>((ref) async {
  final repository = ref.watch(addNewCustomerRepositoryProvider);

  final country = ref.watch(addCustomerNotifierProvider.select((state) => state.country));
  if (country == null) return [];

  final states = await repository.getStatesByCountry(country.iso2);
  if (country.iso2.toUpperCase() == 'TR') {
    final pinned = ['Ankara', 'İstanbul', 'İzmir'];
    states.sort((a, b) {
      final aName = a.name.toLowerCase();
      final bName = b.name.toLowerCase();

      final aPinnedIndex = pinned.indexWhere((pin) => pin.toLowerCase() == aName);
      final bPinnedIndex = pinned.indexWhere((pin) => pin.toLowerCase() == bName);

      final aPinned = aPinnedIndex != -1;
      final bPinned = bPinnedIndex != -1;

      if (aPinned && !bPinned) return -1;
      if (!aPinned && bPinned) return 1;
      if (aPinned && bPinned) {
        return aPinnedIndex.compareTo(bPinnedIndex);
      }

      return aName.compareTo(bName);
    });
  } else {
    states.sort((a, b) => a.name.toLowerCase().compareTo(b.name.toLowerCase()));
  }

  return states;
});

final cityFutureProvider = FutureProvider.autoDispose<List<City>>((ref) async {
  final selectedCountry = ref.watch(addCustomerNotifierProvider.select((s) => s.country));
  final selectedProvince = ref.watch(addCustomerNotifierProvider.select((s) => s.province));
  debugPrint('Şu an seçili il: ${selectedProvince?.name}');

  if (selectedCountry == null || selectedProvince == null) {
    return [];
  }

  final cscRepo = ref.read(addNewCustomerRepositoryProvider);
  final cities = await cscRepo.getCitiesByStatesCountry(
    selectedCountry.iso2,
    selectedProvince.iso2,
  );

  return cities;
});

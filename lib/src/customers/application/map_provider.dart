import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/customers/repository/browser_location_service.dart';

final mapControllerProvider = StateProvider.autoDispose<GoogleMapController?>((ref) => null);

final mapProvider = NotifierProvider<MapNotifier, MapState>(MapNotifier.new);

final isLoadingProvider = StateProvider<bool>((ref) => false);
final searchErrorProvider = StateProvider<String?>((ref) => null);
final searchQueryProvider = StateProvider<String>((ref) => '');
final buttonPressTimestampsProvider = StateProvider<List<DateTime>>((ref) => []);

class LocationService {
  static Future<LatLng?> getLocationForPlace({
    String? countryCode,
    String? stateCode,
    String? cityName,
  }) async {
    return null;
  }
}

class MapState {
  MapState({LatLng? location, this.zoom = 6.0, this.shouldUpdateMap = false})
      : location = location ?? const LatLng(39, 35);

  final LatLng location;
  final double zoom;
  final bool shouldUpdateMap;

  MapState copyWith({
    LatLng? location,
    double? zoom,
    bool? shouldUpdateMap,
  }) {
    return MapState(
      location: location ?? this.location,
      zoom: zoom ?? this.zoom,
      shouldUpdateMap: shouldUpdateMap ?? this.shouldUpdateMap,
    );
  }
}

class MapNotifier extends Notifier<MapState> {
  @override
  MapState build() {
    _initUserLocation();
    return MapState();
  }

  Future<void> _initUserLocation() async {
    final browserLocation = await BrowserLocationService.getUserLocation();
    if (browserLocation != null) {
      updateLocation(browserLocation, zoom: 13, force: true);
      final controller = ref.read(mapControllerProvider);
      if (controller != null) {
        await controller.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: browserLocation,
              zoom: 13,
            ),
          ),
        );
      }
    }
  }

  void updateLocation(LatLng newLocation, {double? zoom, bool force = false}) {
    state = MapState(
      location: newLocation,
      zoom: zoom ?? state.zoom,
      shouldUpdateMap: force,
    );
  }

  void updateZoom(double newZoom) {
    state = state.copyWith(zoom: newZoom);
  }

  Future<void> updateLocationByPlace({
    String? countryCode,
    String? stateCode,
    String? cityName,
    bool forceUpdate = false,
  }) async {
    if (cityName == null) return;
    final location = await LocationService.getLocationForPlace(
      countryCode: countryCode,
      stateCode: stateCode,
      cityName: cityName,
    );
    if (location != null) {
      final controller = ref.read(mapControllerProvider);
      if (controller != null) {
        await controller.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(target: location, zoom: 12),
          ),
        );
      }
      updateLocation(location, zoom: 12, force: forceUpdate);
    }
  }

  Future<void> locateUser() async {
    final browserLocation = await BrowserLocationService.getUserLocation();
    if (browserLocation != null) {
      final controller = ref.read(mapControllerProvider);
      if (controller != null) {
        await controller.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(target: browserLocation, zoom: 15),
          ),
        );
      }
      updateLocation(browserLocation, zoom: 15, force: true);
    }
  }

  void resetMap() {
    state = MapState(
      location: const LatLng(39, 35),
      shouldUpdateMap: true,
    );

    final controller = ref.read(mapControllerProvider);
    if (controller != null) {
      controller.animateCamera(
        CameraUpdate.newCameraPosition(
          const CameraPosition(
            target: LatLng(39, 35),
            zoom: 6,
          ),
        ),
      );
    }
  }
}

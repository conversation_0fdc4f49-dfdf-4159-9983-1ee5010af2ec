import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/customers/domain/address_field/address_fields.dart';

final addressFieldsProvider =
    NotifierProvider<AddressFieldsNotifier, AddressFields>(
  AddressFieldsNotifier.new,
);

class AddressFieldsNotifier extends Notifier<AddressFields> {
  @override
  AddressFields build() => AddressFields();

  void updateAddress({
    String? street,
    String? buildingDoor,
    String? postalCode,
    String? fullAddress,
    String? directions,
  }) {
    state = state.copyWith(
      street: street ?? state.street,
      buildingDoor: buildingDoor ?? state.buildingDoor,
      postalCode: postalCode ?? state.postalCode,
      fullAddress: fullAddress ?? state.fullAddress,
      directions: directions ?? state.directions,
    );
  }

  void resetFields() {
    state = AddressFields(
      street: '',
      buildingDoor: '',
      postalCode: '',
      fullAddress: '',
    );
  }
}

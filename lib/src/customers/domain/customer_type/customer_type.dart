import 'package:freezed_annotation/freezed_annotation.dart';

part 'customer_type.freezed.dart';
part 'customer_type.g.dart';

@freezed
abstract class CustomerType with _$CustomerType {
  const factory CustomerType({
    required String id,
    required String name,
  }) = _CustomerType;
  const CustomerType._();

  factory CustomerType.fromJson(Map<String, dynamic> json) =>
      _$CustomerTypeFromJson(json);

  @override
  String toString() => name;
}

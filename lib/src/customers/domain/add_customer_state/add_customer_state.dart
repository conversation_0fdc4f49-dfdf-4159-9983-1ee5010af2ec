import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:smart_team_common/smart_team_common.dart' hide Country;
import 'package:smart_team_web/src/customers/domain/country_state_city/country_state_city_model.dart';
import 'package:smart_team_web/src/customers/domain/google_place_details_model.dart';

part 'add_customer_state.freezed.dart';

@freezed
abstract class AddCustomerState with _$AddCustomerState {
  factory AddCustomerState({
    @Default('') String searchQuery,
    String? customerName,
    String? authorizedName,
    String? authorizedGSM,
    String? authorizedEmail,
    String? contactName,
    String? contactGSM,
    String? contactEmail,
    Country? country,
    StateModel? province,
    City? city,
    CustomerStatusEnum? status,
    LatLng? location,
    String? streetNumber,
    String? street,
    String? postalCode,
    String? formattedAddress,
    String? addressDirections,
    @Default([]) List<User> assignedToUsers,

    // Not included in toJson
    PlaceDetails? selectedPlaceDetails,
    @Default(false) bool isLoading,
    @Default([]) List<Country> countries,
    @Default([]) List<StateModel> provinces,
    @Default([]) List<City> cities,
  }) = _AddCustomerState;

  const AddCustomerState._();

  Map<String, dynamic> toJson({required String companyId}) {
    return {
      'assigned_to_users': assignedToUsers.map((e) => e.id).toList(),
      'name': customerName,
      'status': status?.dbKey,
      'executive_name': authorizedName,
      'executive_phone': authorizedGSM,
      'executive_email': authorizedEmail,
      'contact_name': contactName,
      'contact_phone': contactGSM,
      'contact_email': contactEmail,
      'company_id': companyId,

      // Address
      'state_province':  city?.name,
      'city': province?.name,
      'district': street,
      'country_code': country?.iso2,
      'country_name': country?.name,
      'latitude': location?.latitude ?? 0,
      'longitude': location?.longitude ?? 0,
      'postal_code': postalCode,
      'address_line1': formattedAddress,
      'address_line2': streetNumber,
      'address_line3': addressDirections,
    };
  }
}

// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

part 'response_model.freezed.dart';
part 'response_model.g.dart';

@freezed
abstract class ResponseModel with _$ResponseModel {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory ResponseModel({
    required List<PlaceModel> results,
    required String status,
    List<dynamic>? htmlAttributions,
    String? nextPageToken,
  }) = _ResponseModel;

  factory ResponseModel.fromJson(Map<String, dynamic> json) =>
      _$ResponseModelFromJson(json);
}

@freezed
abstract class PlaceModel with _$PlaceModel {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory PlaceModel({
    required String name,
    required String placeId,
    @JsonKey(
      name: 'geometry',
      fromJson: _convertGeometryToLatLng,
      includeToJson: false,
    )
    required LatLng location,
    @JsonKey(name: 'formatted_address') required String address,
  }) = _PlaceModel;

  factory PlaceModel.fromJson(Map<String, dynamic> json) =>
      _$PlaceModelFromJson(json);
}

LatLng _convertGeometryToLatLng(Map<String, dynamic> json) {
  final locationMap = json['location'] as Map<String, dynamic>;
  return LatLng(locationMap['lat'] as double, locationMap['lng'] as double);
}

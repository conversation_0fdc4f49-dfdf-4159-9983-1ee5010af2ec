import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

part 'map_company_model.freezed.dart';
part 'map_company_model.g.dart';

class LatLngConverter implements JsonConverter<LatLng, Map<String, dynamic>> {
  const LatLngConverter();

  @override
  LatLng fromJson(Map<String, dynamic> json) => LatLng(
        (json['lat'] as num).toDouble(),
        (json['lng'] as num).toDouble(),
      );

  @override
  Map<String, dynamic> toJson(LatLng object) => <String, dynamic>{
        'lat': object.latitude,
        'lng': object.longitude,
      };
}

@freezed
abstract class MapCompany with _$MapCompany {
  factory MapCompany({
    required String id,
    required String name,
    @LatLngConverter() required LatLng location,
    required String address,
  }) = _MapCompany;

  factory MapCompany.fromJson(Map<String, dynamic> json) =>
      _$MapCompanyFromJson(json);
}

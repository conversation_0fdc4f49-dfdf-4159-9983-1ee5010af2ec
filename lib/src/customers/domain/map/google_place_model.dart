import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

part 'google_place_model.freezed.dart';
part 'google_place_model.g.dart';

@freezed
abstract class GooglePlaceResponse with _$GooglePlaceResponse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory GooglePlaceResponse({
    required List<GooglePlaceResult> results,
    required String status,
    List<dynamic>? htmlAttributions,
    String? nextPageToken,
  }) = _GooglePlaceResponse;

  factory GooglePlaceResponse.fromJson(Map<String, dynamic> json) =>
      _$GooglePlaceResponseFromJson(json);
}

@freezed
abstract class GooglePlaceResult with _$GooglePlaceResult {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory GooglePlaceResult({
    required String name,
    required String placeId,
    @JsonKey(name: 'formatted_address') required String address,
    @JsonKey(
      name: 'geometry',
      fromJson: _geometryFromJson,
      includeToJson: false,
    )
    required LatLng location,
  }) = _GooglePlaceResult;

  factory GooglePlaceResult.fromJson(Map<String, dynamic> json) =>
      _$GooglePlaceResultFromJson(json);
}

LatLng _geometryFromJson(Map<String, dynamic> json) {
  // "geometry" altındaki "location" alanından lat ve lng çekiliyor.
  final location = json['location'] as Map<String, dynamic>;
  return LatLng(
    (location['lat'] as num).toDouble(),
    (location['lng'] as num).toDouble(),
  );
}

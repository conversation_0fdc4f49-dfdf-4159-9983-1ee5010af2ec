import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:smart_team_web/src/shared/extensions/iterable_extensions.dart';

part 'google_place_details_model.freezed.dart';
part 'google_place_details_model.g.dart';

@freezed
abstract class GooglePlaceDetailsModel with _$GooglePlaceDetailsModel {
  const factory GooglePlaceDetailsModel({
    PlaceDetails? result,
  }) = _GooglePlaceDetailsModel;

  factory GooglePlaceDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$GooglePlaceDetailsModelFromJson(json);
}

@freezed
abstract class GoogleReverseGeocodeModel with _$GoogleReverseGeocodeModel {
  const factory GoogleReverseGeocodeModel({
    @Default([]) List<PlaceDetails> results,
  }) = _GoogleReverseGeocodeModel;

  factory GoogleReverseGeocodeModel.fromJson(Map<String, dynamic> json) =>
      _$GoogleReverseGeocodeModelFromJson(json);
}

@freezed
abstract class PlaceDetails with _$PlaceDetails {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory PlaceDetails({
    @JsonKey(
      name: 'geometry',
      fromJson: _convertGeometryToLatLng,
      includeToJson: false,
    )
    required LatLng location,
    required String formattedAddress,
    @Default([]) List<AddressComponentModel> addressComponents,
  }) = _PlaceDetails;

  const PlaceDetails._();

  factory PlaceDetails.fromJson(Map<String, dynamic> json) => _$PlaceDetailsFromJson(json);

  ({
    AddressComponentModel? country,
    AddressComponentModel? province,
    AddressComponentModel? city,
    AddressComponentModel? streetNumber,
    AddressComponentModel? street,
    AddressComponentModel? postalCode,
  }) get addressDetails {
    return (
      country: addressComponents.firstWhereOrNull((component) => component.isCountry),
      province: addressComponents.firstWhereOrNull((component) => component.isProvince),
      city: addressComponents.firstWhereOrNull((component) => component.isCity),
      streetNumber: addressComponents.firstWhereOrNull((component) => component.isStreetNumber),
      street: addressComponents.firstWhereOrNull((component) => component.isStreet),
      postalCode: addressComponents.firstWhereOrNull((component) => component.isPostalCode),
    );
  }

  String get correctAddress {
    final plusCode = addressComponents.firstWhereOrNull((component) => component.types.contains('plus_code'));
    if (plusCode == null) {
      return formattedAddress;
    }

    final addressContainsPlusCode = formattedAddress.contains(plusCode.longName);
    if (!addressContainsPlusCode) {
      return formattedAddress;
    }

    final cleanedAddress = formattedAddress.replaceFirst(plusCode.longName, '').trim();
    return cleanedAddress;
  }
}

@freezed
abstract class AddressComponentModel with _$AddressComponentModel {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory AddressComponentModel({
    required String longName,
    required String shortName,
    @Default([]) List<String> types,
  }) = _AddressComponentModel;

  const AddressComponentModel._();

  factory AddressComponentModel.fromJson(Map<String, dynamic> json) => _$AddressComponentModelFromJson(json);

  bool get isCountry => types.contains('country');
  bool get isProvince => types.contains('administrative_area_level_1');
  bool get isCity => types.contains('administrative_area_level_2');
  bool get isStreetNumber => types.contains('street_number');
  bool get isStreet => types.contains('route');
  bool get isPostalCode => types.contains('postal_code');
}

LatLng _convertGeometryToLatLng(Map<String, dynamic> json) {
  final locationMap = json['location'] as Map<String, dynamic>;
  return LatLng(locationMap['lat'] as double, locationMap['lng'] as double);
}

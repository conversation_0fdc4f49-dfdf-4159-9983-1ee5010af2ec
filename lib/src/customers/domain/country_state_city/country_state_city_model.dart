import 'package:freezed_annotation/freezed_annotation.dart';

part 'country_state_city_model.freezed.dart';
part 'country_state_city_model.g.dart';

@freezed
abstract class Country with _$Country {
  const factory Country({
    required int id,
    required String name,
    required String iso2,
  }) = _Country;

  const Country._();

  factory Country.fromJson(Map<String, dynamic> json) => _$CountryFromJson(json);

  @override
  String toString() {
    return name;
  }
}

@freezed
abstract class StateModel with _$StateModel {
  const factory StateModel({
    required int id,
    required String name,
    required String iso2,
  }) = _StateModel;

  const StateModel._();

  factory StateModel.fromJson(Map<String, dynamic> json) => _$StateModelFromJson(json);

  @override
  String toString() {
    return name;
  }
}

@freezed
abstract class City with _$City {
  const factory City({
    required int id,
    required String name,
  }) = _City;

  const City._();

  factory City.fromJson(Map<String, dynamic> json) => _$City<PERSON>rom<PERSON>son(json);

  @override
  String toString() {
    return name;
  }
}

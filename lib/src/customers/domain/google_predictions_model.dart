import 'package:freezed_annotation/freezed_annotation.dart';

part 'google_predictions_model.freezed.dart';
part 'google_predictions_model.g.dart';

@freezed
abstract class GooglePredictionsModel with _$GooglePredictionsModel {
  const factory GooglePredictionsModel({
    required List<Prediction> predictions,
  }) = _GooglePredictionsModel;

  factory GooglePredictionsModel.fromJson(Map<String, dynamic> json) =>
      _$GooglePredictionsModelFromJson(json);
}

@freezed
abstract class Prediction with _$Prediction {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory Prediction({
    required String description,
    required String placeId,
    @Default([]) List<Term> terms,
  }) = _Prediction;

  factory Prediction.fromJson(Map<String, dynamic> json) => _$PredictionFromJson(json);
}

@freezed
abstract class Term with _$Term {
  const factory Term({
    required int offset,
    required String value,
  }) = _Term;

  factory Term.fromJson(Map<String, dynamic> json) => _$Term<PERSON>(json);
}

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/home/<USER>/team_member.dart';
import 'package:smart_team_web/src/home/<USER>/mock_data.dart';
import 'package:smart_team_web/src/main/enum/education_status_enum.dart';
import 'package:smart_team_web/src/personal_management/model/duty_salary_record.dart';
import 'package:smart_team_web/src/personal_management/model/education_record.dart';
import 'package:smart_team_web/src/personal_management/model/good_record.dart';
import 'package:smart_team_web/src/personal_management/model/permit_record.dart';

final selectedMemberProvider =
    StateProvider<WebTeamMemberModel?>((ref) => null);

final educationRecordsProvider = StateProvider<List<EducationRecord>>((ref) {
  final member = ref.watch(selectedMemberProvider);
  if (member != null && member.id == DashboardMockData.mockMember.id) {
    return [
      EducationRecord(
        status: EducationStatus.graduated,
        institution: 'Örnek Üniversitesi',
        department: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        graduationDate: DateTime(2022, 6, 15),
      ),
    ];
  }
  return [];
});

final permitRecordsProvider = StateProvider<List<PermitRecord>>((ref) {
  final member = ref.watch(selectedMemberProvider);
  if (member != null && member.id == DashboardMockData.mockMember.id) {
    return [
      PermitRecord(
        startDate: DateTime(2022, 6, 15),
        endDate: DateTime(2022, 6, 18),
        period: '3',
        permitType: 'type',
        documentUrl: '',
      ),
    ];
  }
  return [];
});

final dutySalaryRecordsProvider = StateProvider<List<DutySalary>>((ref) {
  final member = ref.watch(selectedMemberProvider);
  if (member != null && member.id == DashboardMockData.mockMember.id) {
    return [
      DutySalary(
        department: 'departman',
        position: 'pozisyon',
        startDate: DateTime(2022, 6, 15),
        status: 'durum',
        brutSalary: 'brut maas',
      ),
    ];
  }
  return [];
});

final goodsRecordsProvider = StateProvider<List<GoodRecord>>((ref) {
  final member = ref.watch(selectedMemberProvider);
  if (member != null && member.id == DashboardMockData.mockMember.id) {
    return [
      GoodRecord(
        goodType: 'type',
        goodDetail: 'detay',
        startDate: DateTime(2022, 6, 15),
        endDate: DateTime(2022, 6, 18),
        explanation: 'aciklama',
      ),
    ];
  }
  return [];
});

extension EducationRecordsProviderExtension
    on StateProvider<List<EducationRecord>> {
  void addRecord(WidgetRef ref, EducationRecord record) {
    final currentRecords = ref.read(this);
    ref.read(notifier).state = [...currentRecords, record];
  }
}

extension PermitRecordsProviderExtension on StateProvider<List<PermitRecord>> {
  void addRecord(WidgetRef ref, PermitRecord record) {
    final currentRecords = ref.read(this);
    ref.read(notifier).state = [...currentRecords, record];
  }
}

extension GoodRecordsProviderExtension on StateProvider<List<GoodRecord>> {
  void addRecord(WidgetRef ref, GoodRecord record) {
    final currentRecords = ref.read(this);
    ref.read(notifier).state = [...currentRecords, record];
  }
}

extension DutySalaryRecordsProviderExtension
    on StateProvider<List<DutySalary>> {
  void addRecord(WidgetRef ref, DutySalary record) {
    final currentRecords = ref.read(this);
    ref.read(notifier).state = [...currentRecords, record];
  }
}

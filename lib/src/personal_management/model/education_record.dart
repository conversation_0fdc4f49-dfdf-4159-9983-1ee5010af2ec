import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:smart_team_web/src/main/enum/education_status_enum.dart';

part 'education_record.freezed.dart';
part 'education_record.g.dart';

@freezed
abstract class EducationRecord with _$EducationRecord {
  const factory EducationRecord({
    required EducationStatus status,
    required String institution,
    required String department,
    required DateTime graduationDate,
  }) = _EducationRecord;

  factory EducationRecord.fromJson(Map<String, dynamic> json) =>
      _$EducationRecordFromJson(json);
}

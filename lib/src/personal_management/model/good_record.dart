import 'package:freezed_annotation/freezed_annotation.dart';

part 'good_record.freezed.dart';
part 'good_record.g.dart';

@freezed
abstract class GoodRecord with _$GoodRecord {
  const factory GoodRecord({
    required String goodType,
    required String goodDetail,
    required DateTime startDate,
    required DateTime endDate,
    required String explanation,
  }) = _GoodRecord;

  factory GoodRecord.fromJson(Map<String, dynamic> json) =>
      _$GoodRecordFromJson(json);
}

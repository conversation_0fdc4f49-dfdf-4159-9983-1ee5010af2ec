import 'package:freezed_annotation/freezed_annotation.dart';

part 'permit_record.freezed.dart';
part 'permit_record.g.dart';

@freezed
abstract class PermitRecord with _$PermitRecord {
  const factory PermitRecord({
    required DateTime startDate,
    required DateTime endDate,
    required String period,
    required String permitType,
    required String documentUrl,
  }) = _PermitRecord;

  factory PermitRecord.fromJson(Map<String, dynamic> json) =>
      _$PermitRecordFromJson(json);
}

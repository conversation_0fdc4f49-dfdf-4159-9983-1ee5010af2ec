import 'package:freezed_annotation/freezed_annotation.dart';

part 'duty_salary_record.freezed.dart';
part 'duty_salary_record.g.dart';

@freezed
abstract class DutySalary with _$DutySalary {
  const factory DutySalary({
    required String department,
    required String position,
    required DateTime startDate,
    required String status,
    required String brutSalary,
  }) = _DutySalary;

  factory DutySalary.fromJson(Map<String, dynamic> json) =>
      _$DutySalaryFromJson(json);
}

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_common/smart_team_common.dart' show SmartTeamAssets;
import 'package:smart_team_web/src/home/<USER>/team_member.dart';
import 'package:smart_team_web/src/home/<USER>/mock_data.dart';
import 'package:smart_team_web/src/home/<USER>/widgets/hover_colored_icon.dart';
import 'package:smart_team_web/src/personal_management/application/selected_member_provider.dart';
import 'package:smart_team_web/src/personal_management/presentation/add_personal/add_personal_widgets.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/shared/utils/dt_format.dart';
import 'package:smart_team_web/src/shared/utils/dt_util.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/users/enum/work_type.dart';
import 'package:smart_team_web/src/widgets/app_dialog/app_dialog.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';
import 'package:url_launcher/url_launcher.dart';

part 'components/page_tabs.dart';
part 'components/personal_info.dart';
part 'components/work_info.dart';
part 'components/documents.dart';
part 'widgets/info_card.dart';
part 'widgets/contact_info_card.dart';
part 'widgets/education_card.dart';
part 'widgets/work_details_card.dart';
part 'widgets/assigned_assets_card.dart';
part 'widgets/assignment_salary_card.dart';
part 'widgets/leave_usage_card.dart';
part 'widgets/base_card.dart';
part 'widgets/document_card.dart';

@RoutePage(name: 'PersonalDetailRoute')
class PersonalDetailView extends HookConsumerWidget {
  const PersonalDetailView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabIndex = useState(0);
    final member = ref.watch(selectedMemberProvider);

    if (member == null) {
      return Center(
        child: TextWidget('Lütfen bir personel seçin'.hardcoded),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 24),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: AColor.white,
        ),
        child: Column(
          spacing: 32,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  spacing: 8,
                  children: [
                    IconButton(
                      icon: const Icon(
                        Icons.arrow_back_ios_new_rounded,
                        color: AColor.primaryColor,
                      ),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                    CircleAvatar(
                      radius: 24,
                      backgroundImage: member.imageUrl != null
                          ? NetworkImage(member.imageUrl!)
                          : null,
                      child: member.imageUrl == null
                          ? Text(member.userName[0])
                          : null,
                    ),
                    Text(member.userName, style: ATextStyle.text14SemiBold),
                  ],
                ),
                _DetailTabs(
                  index: tabIndex.value,
                  onChanged: (i) => tabIndex.value = i,
                ),
                const SizedBox.shrink(),
              ],
            ),
            Expanded(
              child: IndexedStack(
                index: tabIndex.value,
                children: [
                  _PersonalInfo(member: member),
                  _WorkInfo(member: member),
                  _Documents(member: DashboardMockData.mockMember),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

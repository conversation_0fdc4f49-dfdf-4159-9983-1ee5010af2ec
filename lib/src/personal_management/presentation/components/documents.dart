part of '../personal_detail_view.dart';

class _Documents extends StatelessWidget {
  const _Documents({required this.member});
  final WebTeamMemberModel member;

  @override
  Widget build(BuildContext context) {
    final types = <_DocumentType>[
      const _DocumentType(key: 'idCard', label: 'Kimlik Kartı'),
      const _DocumentType(key: 'residence', label: 'İkametgah Belgesi'),
      const _DocumentType(key: 'record', label: '<PERSON>ü<PERSON>s Kayıt Örneği'),
      const _DocumentType(key: 'criminal', label: '<PERSON><PERSON> Si<PERSON>l <PERSON>d<PERSON>'),
      const _DocumentType(key: 'graduation', label: 'Mezuniyet Belgesi'),
      const _DocumentType(
          key: 'familyStatus', label: 'Aile Durumunu Bildirir Belge'),
      const _DocumentType(
          key: 'military', label: 'Askerlik Durumunu Gösterir Belge'),
      const _DocumentType(
          key: 'bankInfo', label: '<PERSON><PERSON><PERSON> / Banka Bilgisi'),
      const _DocumentType(key: 'workRecord', label: '<PERSON>ş‑Ku<PERSON> Kaydı'),
    ];

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 16),
        child: LayoutBuilder(
          builder: (context, constraints) {
            const itemsPerRow = 6;
            const double spacing = 16;
            const totalSpacing = spacing * (itemsPerRow - 1);
            final cardWidth =
                (constraints.maxWidth - totalSpacing) / itemsPerRow;

            return Wrap(
              spacing: spacing,
              runSpacing: spacing,
              alignment: WrapAlignment.center,
              children: types.map((t) {
                final url = member.documents[t.key];
                return SizedBox(
                  width: cardWidth,
                  child: DocumentCard(
                    title: t.label,
                    documentUrl: url,
                    onUpload: () {},
                    onView: url != null ? () {} : null,
                  ),
                );
              }).toList(),
            );
          },
        ),
      ),
    );
  }
}

part of '../personal_management_view.dart';

class RightContainer extends HookConsumerWidget {
  const RightContainer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final allEmployees = DashboardMockData.getMembers();
    final currentPage = useState(1);
    const pageSize = 12;
    final totalPages = (allEmployees.length / pageSize)
        .ceil()
        .clamp(1, double.infinity)
        .toInt();

    final start = (currentPage.value - 1) * pageSize;
    final pageItems = allEmployees.skip(start).take(pageSize).toList();

    final selectedIds = useState<Set<String>>({});

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(color: AColor.white),
      child: Column(
        children: [
          Expanded(
            child: allEmployees.isEmpty
                ? Center(
                    child: TextWidget(
                      'Hiç personel yok'.hardcoded,
                      style: ATextStyle.text18.copyWith(
                        color: AColor.grey,
                      ),
                    ),
                  )
                : GridView.builder(
                    padding: const EdgeInsets.only(bottom: 16),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 4,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 4 / 3,
                    ),
                    itemCount: pageItems.length,
                    itemBuilder: (context, idx) {
                      final user = pageItems[idx];
                      final isSelected = selectedIds.value.contains(user.id);

                      return EmployeeCard(user: user);
                    },
                  ),
          ),
          BottomNavigationButtons(
            onFirst: currentPage.value > 1 ? () => currentPage.value = 1 : null,
            onPrevious: () {
              if (currentPage.value > 1) currentPage.value--;
            },
            onNext: () {
              if (currentPage.value < totalPages) currentPage.value++;
            },
            onLast: currentPage.value < totalPages
                ? () => currentPage.value = totalPages
                : null,
            currentPage: currentPage.value,
            totalPages: totalPages,
            totalCount: allEmployees.length,
          ),
        ],
      ),
    );
  }
}

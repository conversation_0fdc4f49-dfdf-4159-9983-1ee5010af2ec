part of '../personal_management_view.dart';

class EmployeeCard extends HookConsumerWidget {
  const EmployeeCard({required this.user, super.key});
  final WebTeamMemberModel user;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return HoverBuilder(
      onTap: () {
        // TODO(Ali): Revise
        // context.navigateTo(PersonalDetailRoute(member: user));
        // context.router.push(PersonalDetailRoute(member: user));
        // context.router.navigate(PersonalDetailRoute(member: user));
        ref.read(selectedMemberProvider.notifier).state = user;
        context.router.push(const PersonalDetailRoute());
      },
      builder: (isHovering, isPressed) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isHovering ? Colors.black : Colors.grey.withAlpha(100),
              width: isHovering ? 2 : 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              spacing: 8,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Row(
                    spacing: 8,
                    children: [
                      CircleAvatar(
                        radius: 35,
                        backgroundImage: user.imageUrl != null
                            ? NetworkImage(user.imageUrl!)
                            : null,
                        backgroundColor: Colors.grey.shade400,
                        child: user.imageUrl == null
                            ? TextWidget(
                                user.userName.substring(0, 1),
                                style: ATextStyle.medium
                                    .copyWith(color: Colors.white),
                              )
                            : null,
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              TextWidget(
                                user.userName,
                                style: ATextStyle.medium
                                    .copyWith(fontWeight: FontWeight.w600),
                                maxLines: 1,
                              ),
                              TextWidget(
                                user.department,
                                style: ATextStyle.text12,
                                maxLines: 1,
                              ),
                              TextWidget(
                                user.position,
                                style: ATextStyle.text12,
                                maxLines: 1,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    spacing: 4,
                    children: [
                      Row(
                        spacing: 8,
                        children: [
                          const Icon(Icons.phone, size: 16),
                          Expanded(
                            child: TextWidget(
                              user.phoneNumber,
                              style: ATextStyle.text12,
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                      Row(
                        spacing: 8,
                        children: [
                          const Icon(Icons.email, size: 16),
                          Expanded(
                            child: TextWidget(
                              user.emailAddress,
                              style: ATextStyle.text12,
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

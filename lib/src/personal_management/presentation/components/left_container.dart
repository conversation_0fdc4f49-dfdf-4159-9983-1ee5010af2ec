part of '../personal_management_view.dart';

class LeftContainer extends HookConsumerWidget {
  const LeftContainer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedWorkTypes = useState<List<DisplayWorkType>>([]);

    return Container(
      width: PersonalManagementConstants.leftContainerWidth(context),
      padding: context.responsive(
        desktop: const EdgeInsets.symmetric(horizontal: 30).copyWith(top: 24),
        tablet: const EdgeInsets.symmetric(horizontal: 20).copyWith(top: 16),
      ),
      child: Column(
        // mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 8,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 4,
            children: [
              TextWidget(
                'Personel Adı'.hardcoded,
                style: ATextStyle.small.copyWith(color: AColor.textColor),
              ),
              SearchField(
                onChanged: (value) {},
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 4,
            children: [
              TextWidget(
                'Departman'.hardcoded,
                style: ATextStyle.small.copyWith(color: AColor.textColor),
              ),
              MultiSelectFilterDropDown<WebTeamMemberModel>(
                placeholder: context.tr.select,
                itemList: DashboardMockData.getMembers(),
                selectedItems: [],
                onSelected: (members) {},
                width: double.infinity,
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 4,
            children: [
              TextWidget(
                'Pozisyon'.hardcoded,
                style: ATextStyle.small.copyWith(color: AColor.textColor),
              ),
              MultiSelectFilterDropDown<WebTeamMemberModel>(
                placeholder: context.tr.select,
                itemList: DashboardMockData.getMembers(),
                selectedItems: [],
                onSelected: (members) {},
                width: double.infinity,
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 4,
            children: [
              TextWidget(
                'İşe Giriş Tarihi'.hardcoded,
                style: ATextStyle.small.copyWith(color: AColor.textColor),
              ),
              DateRangeDropDown(
                placeholder: context.tr.select,
                onSelected: (dates) {},
                width: double.infinity,
              )
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 4,
            children: [
              TextWidget(
                'Çalışma Şekli'.hardcoded,
                style: ATextStyle.small.copyWith(color: AColor.textColor),
              ),
              MultiSelectFilterDropDown<DisplayWorkType>(
                placeholder: context.tr.select,
                itemList: DisplayWorkType.getList(context),
                selectedItems: selectedWorkTypes.value,
                onSelected: (wrappers) => selectedWorkTypes.value = wrappers,
                width: double.infinity,
                itemBuilder: (wrapper) => Text(
                  wrapper!.label,
                  style: ATextStyle.text14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            spacing: 16,
            children: [
              LoadingElevatedButton(
                height: 35,
                onPressed: () async {},
                text: 'Tüm Filtreleri Sil'.hardcoded,
                textColor: AColor.primaryColor,
                backgroundColor: AColor.white,
                borderColor: AColor.primaryColor,
              ),
              LoadingElevatedButton(
                height: 35,
                onPressed: () async {},
                text: 'Filtrele'.hardcoded,
                backgroundColor: AColor.primaryColor,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

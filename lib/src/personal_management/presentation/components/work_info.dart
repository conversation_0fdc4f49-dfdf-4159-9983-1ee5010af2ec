part of '../personal_detail_view.dart';

class _WorkInfo extends StatelessWidget {
  const _WorkInfo({required this.member});
  final WebTeamMemberModel member;

  DateTime? _parseDT(dynamic v) {
    if (v == null) return null;
    if (v is DateTime) return v;
    if (v is String) return DTUtil.stringToDT(v);
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final work = member.workInfo;

    final joinDate = _parseDT(work?.joinDate);

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 16),
        child: LayoutBuilder(
          builder: (context, constraints) {
            final cardWidth = (constraints.maxWidth - 32) / 3;
            return Wrap(
              spacing: 16,
              runSpacing: 16,
              children: [
                SizedBox(
                  width: cardWidth,
                  child: Column(
                    spacing: 32,
                    children: [
                      WorkDetailsCard(
                        startDate: joinDate,
                        workType: work?.workType?.label(context) ?? '',
                        onEdit: () {
                          AppDialog.show<void>(
                            context: context,
                            title: '<PERSON>el Düzenle'.hardcoded,
                            width: context.width * .95,
                            height: context.height * .95,
                            child: const AddPersonalWidgets(initialTabIndex: 1),
                          );
                        },
                      ),
                      AssignedAssetsCard(
                        assets: [
                          Asset(
                            type: member.userName,
                            detail: member.userName,
                            assignedDate: member.userName,
                            returnDate: member.userName,
                            note: member.userName,
                          ),
                          Asset(
                            type: member.userName,
                            detail: member.userName,
                            assignedDate: member.userName,
                            returnDate: member.userName,
                            note: member.userName,
                          ),
                        ],
                        onEdit: () {
                          AppDialog.show<void>(
                            context: context,
                            title: 'Personel Düzenle'.hardcoded,
                            width: context.width * .95,
                            height: context.height * .95,
                            child: const AddPersonalWidgets(initialTabIndex: 1),
                          );
                        },
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  width: cardWidth,
                  child: AssignmentSalaryCard(
                    assignments: [
                      JobAssignment(
                        department: member.department,
                        position: member.position,
                        startDate: member.userName,
                        endDate: member.userName,
                        salary: member.userName,
                      ),
                      JobAssignment(
                        department: member.department,
                        position: member.position,
                        startDate: member.userName,
                        endDate: member.userName,
                        salary: member.userName,
                      ),
                      JobAssignment(
                        department: member.department,
                        position: member.position,
                        startDate: member.userName,
                        endDate: member.userName,
                        salary: member.userName,
                      ),
                    ],
                    onEdit: () {
                      AppDialog.show<void>(
                        context: context,
                        title: 'Personel Düzenle'.hardcoded,
                        width: context.width * .95,
                        height: context.height * .95,
                        child: const AddPersonalWidgets(initialTabIndex: 1),
                      );
                    },
                  ),
                ),
                SizedBox(
                  width: cardWidth,
                  child: LeaveUsageCard(
                    summary: LeaveSummary(remaining: 1, used: 2),
                    leaves: [
                      LeaveRecord(
                        startDate: '01.11.1998',
                        endDate: '01.11.2025',
                        duration: 25,
                        type: 'type',
                        documentUrl:
                            'https://docs.google.com/document/d/1ySI7fhHgusRQtP0Vv7PQNFq2S4TqjXcG/edit?usp=drive_link&ouid=107747436243383320036&rtpof=true&sd=true',
                      ),
                    ],
                    onEdit: () {
                      AppDialog.show<void>(
                        context: context,
                        title: 'Personel Düzenle'.hardcoded,
                        width: context.width * .95,
                        height: context.height * .95,
                        child: const AddPersonalWidgets(initialTabIndex: 1),
                      );
                    },
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

part of '../personal_detail_view.dart';

class _DetailTabs extends StatelessWidget {
  const _DetailTabs({required this.index, required this.onChanged});
  final int index;
  final ValueChanged<int> onChanged;

  @override
  Widget build(BuildContext context) {
    List<String> labels = [
      'Kişisel Bilgiler'.hardcoded,
      'Çalışma Bilgileri'.hardcoded,
      'Özlük Evrakları'.hardcoded,
    ];
    return Container(
      height: 42,
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(labels.length, (i) {
          final selected = i == index;
          return InkWell(
            onTap: () => onChanged(i),
            child: Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 32),
              height: 34,
              decoration: BoxDecoration(
                color: selected ? AColor.white : Colors.grey[300],
                borderRadius: BorderRadius.circular(selected ? 10 : 0),
              ),
              child: Text(
                labels[i],
                style: ATextStyle.text12.copyWith(
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}

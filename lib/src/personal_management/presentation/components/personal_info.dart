part of '../personal_detail_view.dart';

class _PersonalInfo extends StatelessWidget {
  const _PersonalInfo({required this.member});
  final WebTeamMemberModel member;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 16),
        child: LayoutBuilder(
          builder: (context, constraints) {
            final cardWidth = (constraints.maxWidth - 32) / 3;

            return Wrap(
              spacing: 16,
              runSpacing: 16,
              children: [
                SizedBox(
                  width: cardWidth,
                  child: _InfoCard(
                    title: 'Profil'.hardcoded,
                    rows: {
                      '<PERSON><PERSON><PERSON> Tarihi': member.userName,
                      'Cinsiyet': member.userName,
                      'Uyruğu': member.userName,
                      '<PERSON><PERSON>': member.userName,
                      '<PERSON>deni <PERSON>': member.userName,
                      'Çocuk Sayısı': member.userName,
                    },
                  ),
                ),
                SizedBox(
                  width: cardWidth,
                  child: _ContactInfoCard(
                    info: ContactInfo(
                      workEmail: member.emailAddress,
                      personalEmail: member.userName,
                      workPhone: member.phoneNumber,
                      personalPhone: member.phoneNumber,
                      address: member.userName * 14,
                    ),
                    emergency: EmergencyContact(
                      name: member.userName,
                      phone: member.phoneNumber,
                      relation: member.userName,
                    ),
                  ),
                ),
                SizedBox(
                  width: cardWidth,
                  child: _EducationCard(
                    educationList: [
                      Education(status: 'a', institution: 'b', department: 'c'),
                      Education(
                        status: 'a',
                        institution: 'b',
                        department: 'c',
                        graduationDate: 'd',
                      ),
                      Education(
                        status: 'a',
                        institution: 'b',
                        department: 'c',
                        graduationDate: 'd',
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

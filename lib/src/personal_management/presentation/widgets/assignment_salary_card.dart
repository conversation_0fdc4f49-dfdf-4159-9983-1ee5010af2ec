part of '../personal_detail_view.dart';

class AssignmentSalaryCard extends StatelessWidget {
  const AssignmentSalaryCard(
      {required this.assignments, super.key, this.onEdit});

  final List<JobAssignment> assignments;
  final VoidCallback? onEdit;

  @override
  Widget build(BuildContext context) {
    return _BaseCard(
      title: 'Görevlendirme ve Maaş Bilgileri',
      onEdit: onEdit,
      child: Column(
        children: assignments.map((j) {
          return Column(
            children: [
              Table(
                columnWidths: const {
                  0: IntrinsicColumnWidth(),
                  1: FlexColumnWidth(),
                },
                children: [
                  _row('Departman', j.department),
                  _row('<PERSON><PERSON>syon', j.position),
                  _row('<PERSON><PERSON><PERSON><PERSON><PERSON> Ta<PERSON>', j.startDate),
                  if (j.endDate != null)
                    _row('Durum', 'Sonlandı - ${j.endDate!}')
                  else
                    _row('Durum', 'Aktif'),
                  _row('<PERSON><PERSON><PERSON><PERSON>', j.salary),
                ],
              ),
              if (j != assignments.last)
                const Divider(height: 24, color: AColor.lightGreyBackground),
            ],
          );
        }).toList(),
      ),
    );
  }

  TableRow _row(String label, String value) => TableRow(children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Text(label, style: ATextStyle.text12),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Text(value,
              style: ATextStyle.text12SemiBold, textAlign: TextAlign.right),
        ),
      ]);
}

class JobAssignment {
  JobAssignment({
    required this.department,
    required this.position,
    required this.startDate,
    required this.salary,
    this.endDate,
  });
  final String department, position, startDate, salary;
  final String? endDate;
}

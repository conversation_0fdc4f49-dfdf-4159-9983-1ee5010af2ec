part of '../personal_detail_view.dart';

class Education {
  Education({
    required this.status,
    required this.institution,
    required this.department,
    this.graduationDate,
  });
  final String status;
  final String institution;
  final String department;
  final String? graduationDate;
}

class _EducationCard extends StatelessWidget {
  const _EducationCard({
    required this.educationList,
  });

  final List<Education> educationList;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AColor.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: AColor.pinBorderColor),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            decoration: const BoxDecoration(
              color: AColor.lightGreyBackground,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
            child: ListTile(
              dense: true,
              title: TextWidget(
                'Eğitim Bilgileri'.hardcoded,
                style: ATextStyle.text14,
              ),
              trailing: SmartTeamAssets.icons.edit.svg(),
              onTap: () {
                AppDialog.show<void>(
                  context: context,
                  title: 'Personel Düzenle'.hardcoded,
                  width: context.width * .95,
                  height: context.height * .95,
                  child: const AddPersonalWidgets(),
                );
              },
            ),
          ),
          const Divider(
            height: 1,
            color: AColor.lightGreyBackground,
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: educationList.asMap().entries.map((entry) {
                final idx = entry.key;
                final edu = entry.value;
                final isLast = idx == educationList.length - 1;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Table(
                      columnWidths: const {
                        0: IntrinsicColumnWidth(),
                        1: FlexColumnWidth(),
                      },
                      defaultVerticalAlignment:
                          TableCellVerticalAlignment.middle,
                      children: [
                        _buildRow('Durum'.hardcoded, edu.status),
                        _buildRow('Eğitim Kurumu'.hardcoded, edu.institution),
                        _buildRow('Bölümü'.hardcoded, edu.department),
                        _buildRow('Mezuniyet Tarihi'.hardcoded,
                            edu.graduationDate ?? '-'),
                      ],
                    ),
                    if (!isLast)
                      const Divider(
                        height: 24,
                        color: AColor.lightGreyBackground,
                      ),
                  ],
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  TableRow _buildRow(String label, String value) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Align(
            alignment: Alignment.centerLeft,
            child: TextWidget(label,
                style: ATextStyle.text12, textAlign: TextAlign.left),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Align(
            alignment: Alignment.centerRight,
            child: TextWidget(value,
                style: ATextStyle.text12SemiBold, textAlign: TextAlign.right),
          ),
        ),
      ],
    );
  }
}

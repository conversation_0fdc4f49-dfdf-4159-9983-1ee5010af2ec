part of '../personal_detail_view.dart';

class ContactInfo {
  ContactInfo({
    required this.workEmail,
    required this.personalEmail,
    required this.workPhone,
    required this.personalPhone,
    required this.address,
  });
  final String workEmail;
  final String personalEmail;
  final String workPhone;
  final String personalPhone;
  final String address;
}

class EmergencyContact {
  EmergencyContact({
    required this.name,
    required this.phone,
    required this.relation,
  });
  final String name;
  final String phone;
  final String relation;
}

class _ContactInfoCard extends StatelessWidget {
  const _ContactInfoCard({
    required this.info,
    required this.emergency,
  });

  final ContactInfo info;
  final EmergencyContact emergency;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AColor.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: AColor.pinBorderColor),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            decoration: const BoxDecoration(
              color: AColor.lightGreyBackground,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
            child: ListTile(
              dense: true,
              title: TextWidget(
                'İletişim Bilgileri'.hardcoded,
                style: ATextStyle.text14,
              ),
              trailing: SmartTeamAssets.icons.edit.svg(),
              onTap: () {
                AppDialog.show<void>(
                  context: context,
                  title: 'Personel Düzenle'.hardcoded,
                  width: context.width * .95,
                  height: context.height * .95,
                  child: const AddPersonalWidgets(),
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Table(
              columnWidths: const {
                0: IntrinsicColumnWidth(),
                1: FlexColumnWidth(),
              },
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              children: [
                _buildRow('İş E‑Posta Adresi'.hardcoded, info.workEmail),
                _buildRow(
                    'Kişisel E‑Posta Adresi'.hardcoded, info.personalEmail),
                _buildRow('İş Telefonu'.hardcoded, info.workPhone),
                _buildRow('Kişisel Telefonu'.hardcoded, info.personalPhone),
                _buildRow('Açık Adres'.hardcoded, info.address),
              ],
            ),
          ),
          const Divider(height: 1, color: AColor.lightGreyBackground),
          Padding(
            padding: const EdgeInsets.fromLTRB(12, 8, 12, 0),
            child: Text(
              'Acil Durumda Aranacak Kişi'.hardcoded,
              style: ATextStyle.text14Bold.copyWith(
                color: AColor.trackColor,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
            child: Table(
              columnWidths: const {
                0: IntrinsicColumnWidth(),
                1: FlexColumnWidth(),
              },
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              children: [
                _buildRow('Ad‑Soyad'.hardcoded, emergency.name),
                _buildRow('Telefon'.hardcoded, emergency.phone),
                _buildRow('Yakınlık Derecesi'.hardcoded, emergency.relation),
              ],
            ),
          ),
        ],
      ),
    );
  }

  TableRow _buildRow(String label, String value) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 6),
          child: TextWidget(
            label,
            style: ATextStyle.text12,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 6),
          child: TextWidget(
            value,
            style: ATextStyle.text12SemiBold,
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }
}

part of '../personal_detail_view.dart';

class AssignedAssetsCard extends StatelessWidget {
  const AssignedAssetsCard({required this.assets, this.onEdit});

  final List<Asset> assets;
  final VoidCallback? onEdit;

  @override
  Widget build(BuildContext context) {
    return _BaseCard(
      title: 'Üzerine Zimmetli Mallar',
      onEdit: onEdit,
      child: Column(
        children: assets.map((a) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Table(
                columnWidths: const {
                  0: IntrinsicColumnWidth(),
                  1: FlexColumnWidth(),
                },
                children: [
                  _row('<PERSON>mirbaş Tipi', a.type),
                  _row('<PERSON><PERSON><PERSON><PERSON>', a.detail),
                  _row('Atama Tarihi', a.assignedDate),
                  _row('<PERSON>ade Tarihi', a.returnDate),
                  _row('Açıklama', a.note),
                ],
              ),
              if (a != assets.last)
                const Divider(height: 24, color: AColor.lightGreyBackground),
            ],
          );
        }).toList(),
      ),
    );
  }

  TableRow _row(String label, String value) {
    return TableRow(children: [
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Text(label, style: ATextStyle.text12),
      ),
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Text(value,
            style: ATextStyle.text12SemiBold, textAlign: TextAlign.right),
      ),
    ]);
  }
}

class Asset {
  Asset({
    required this.type,
    required this.detail,
    required this.assignedDate,
    required this.returnDate,
    required this.note,
  });
  final String type, detail, assignedDate, returnDate, note;
}

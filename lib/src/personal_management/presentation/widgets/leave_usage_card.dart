part of '../personal_detail_view.dart';

class LeaveUsageCard extends StatelessWidget {
  const LeaveUsageCard({
    required this.summary,
    required this.leaves,
    super.key,
    this.onEdit,
  });

  final LeaveSummary summary;
  final List<LeaveRecord> leaves;
  final VoidCallback? onEdit;

  @override
  Widget build(BuildContext context) {
    return _BaseCard(
      title: '<PERSON><PERSON> Kullanımları',
      onEdit: onEdit,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Table(
            columnWidths: const {
              0: IntrinsicColumnWidth(),
              1: FlexColumnWidth(),
            },
            children: [
              _row('Kullanılan Yıllık İzin', '${summary.used} gün'),
              _row('<PERSON><PERSON> Yıllık İzin Hakkı', '${summary.remaining} gün'),
            ],
          ),
          const Divider(height: 24, color: AColor.lightGreyBackground),
          ...leaves.map((l) {
            return Column(
              children: [
                Table(
                  columnWidths: const {
                    0: IntrinsicColumnWidth(),
                    1: FlexColumnWidth(),
                  },
                  children: [
                    _row('<PERSON><PERSON>şlangı<PERSON>', l.startDate),
                    _row('İzin Bitiş', l.endDate),
                    _row('İzin Süresi', '${l.duration} gün'),
                    _row('İzin Türü', l.type),
                    if (l.documentUrl != null)
                      TableRow(
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 4),
                            child: Text('Belge Görüntüle',
                                style: ATextStyle.text12),
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 4),
                            child: Align(
                              alignment: Alignment.bottomRight,
                              child: HoverColoredIcon(
                                color: AColor.black,
                                icon: Icons.find_in_page_outlined,
                                onSelected: (_) =>
                                    launchUrl(Uri.parse(l.documentUrl!)),
                              ),
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
                if (l != leaves.last)
                  const Divider(height: 24, color: AColor.lightGreyBackground),
              ],
            );
          }),
        ],
      ),
    );
  }

  TableRow _row(String label, String value) => TableRow(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Text(label, style: ATextStyle.text12),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Text(
              value,
              style: ATextStyle.text12SemiBold,
              textAlign: TextAlign.right,
            ),
          ),
        ],
      );
}

class LeaveSummary {
  LeaveSummary({required this.used, required this.remaining});
  final int used;
  final int remaining;
}

class LeaveRecord {
  LeaveRecord({
    required this.startDate,
    required this.endDate,
    required this.duration,
    required this.type,
    this.documentUrl,
  });
  final String startDate;
  final String endDate;
  final String type;
  final int duration;
  final String? documentUrl;
}

part of '../personal_detail_view.dart';

class WorkDetailsCard extends StatelessWidget {
  const WorkDetailsCard({
    required this.startDate,
    required this.workType,
    super.key,
    this.onEdit,
  });

  final DateTime? startDate;
  final String workType;

  final VoidCallback? onEdit;
  String _fmt(BuildContext ctx, DateTime? dt) => DTUtil.dtToString(
        dt,
        format: DTFormat.dayMonthYear,
        languageCode: Localizations.localeOf(ctx).languageCode,
      );

  String _humanize(DateTime? start) {
    if (start == null) return '';
    final diffDays = DateTime.now().difference(start).inDays;
    final years = diffDays ~/ 365;
    final months = (diffDays % 365) ~/ 30;

    if (years > 0 && months > 0) return '$years yıl $months ay';
    if (years > 0) return '$years yıl';
    if (months > 0) return '$months ay';
    return '$diffDays gün';
  }

  @override
  Widget build(BuildContext context) {
    final startLabel = _fmt(context, startDate);
    final durationLabel = _humanize(startDate);

    return _BaseCard(
      title: 'Çalışma Bilgileri',
      onEdit: onEdit,
      child: Table(
        columnWidths: const {
          0: IntrinsicColumnWidth(),
          1: FlexColumnWidth(),
        },
        children: [
          _row('İşe Başlama Tarihi'.hardcoded, startLabel),
          _row('Çalışma Süresi'.hardcoded, durationLabel),
          _row('Çalışma Türü'.hardcoded, workType),
        ],
      ),
    );
  }

  TableRow _row(String label, String value) => TableRow(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Text(label, style: ATextStyle.text12),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Text(
              value,
              style: ATextStyle.text12SemiBold,
              textAlign: TextAlign.right,
            ),
          ),
        ],
      );
}

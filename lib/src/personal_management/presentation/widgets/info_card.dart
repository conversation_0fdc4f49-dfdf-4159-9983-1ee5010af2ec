part of '../personal_detail_view.dart';

class _InfoCard extends StatelessWidget {
  const _InfoCard({required this.title, required this.rows});
  final String title;
  final Map<String, String?> rows;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AColor.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: AColor.pinBorderColor),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            decoration: const BoxDecoration(
              color: AColor.lightGreyBackground,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
            child: ListTile(
              dense: true,
              title: TextWidget(
                title,
                style: ATextStyle.text14,
              ),
              trailing: SmartTeamAssets.icons.edit.svg(),
              onTap: () {
                AppDialog.show<void>(
                  context: context,
                  title: '<PERSON>el Dü<PERSON>le'.hardcoded,
                  width: context.width * .95,
                  height: context.height * .95,
                  child: const AddPersonalWidgets(),
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Table(
              columnWidths: const {
                0: IntrinsicColumnWidth(),
                1: FlexColumnWidth(),
              },
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              children: rows.entries.map((e) {
                return TableRow(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: TextWidget(
                          e.key,
                          style: ATextStyle.text12,
                          textAlign: TextAlign.left,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: TextWidget(
                          e.value ?? '-',
                          style: ATextStyle.text12SemiBold,
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}

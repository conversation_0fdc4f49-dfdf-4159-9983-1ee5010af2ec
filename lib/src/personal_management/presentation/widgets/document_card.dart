part of '../personal_detail_view.dart';

class _DocumentType {
  const _DocumentType({required this.key, required this.label});
  final String key;
  final String label;
}

class DocumentCard extends StatefulWidget {
  const DocumentCard({
    required this.title,
    required this.documentUrl,
    required this.onUpload,
    this.onView,
    super.key,
  });

  final String title;
  final String? documentUrl;
  final VoidCallback onUpload;
  final VoidCallback? onView;

  @override
  State<DocumentCard> createState() => _DocumentCardState();
}

class _DocumentCardState extends State<DocumentCard> {
  bool _hovering = false;

  void _onEnter(PointerEvent _) => setState(() => _hovering = true);
  void _onExit(PointerEvent _) => setState(() => _hovering = false);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AColor.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: AColor.pinBorderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            height: 40,
            decoration: const BoxDecoration(
              color: AColor.lightGreyBackground,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
            child: Row(
              children: [
                Expanded(child: Text(widget.title, style: ATextStyle.text14)),
                IconButton(
                  icon: const Icon(Icons.add, size: 18),
                  onPressed: widget.onUpload,
                  tooltip: widget.documentUrl == null
                      ? 'Belge Yükle'.hardcoded
                      : 'Belge Güncelle'.hardcoded,
                ),
              ],
            ),
          ),
          MouseRegion(
            onEnter: _onEnter,
            onExit: _onExit,
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: AspectRatio(
                aspectRatio: 3 / 4,
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    if (widget.documentUrl != null)
                      ClipRRect(
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(4),
                          bottomRight: Radius.circular(4),
                        ),
                        child: Image.network(
                          widget.documentUrl!,
                          fit: BoxFit.cover,
                        ),
                      )
                    else
                      Container(
                        decoration: const BoxDecoration(
                          color: AColor.doveGray,
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(4),
                            bottomRight: Radius.circular(4),
                          ),
                        ),
                        child: Center(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            spacing: 8,
                            children: [
                              const Stack(
                                children: [
                                  Icon(
                                    Icons.circle,
                                    size: 36,
                                    color: AColor.white,
                                  ),
                                  Icon(
                                    Icons.cancel,
                                    size: 36,
                                    color: AColor.primaryColor,
                                  ),
                                ],
                              ),
                              TextWidget(
                                'Eksik Belge'.hardcoded,
                                style: ATextStyle.medium.copyWith(
                                  color: AColor.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    if (_hovering && widget.documentUrl != null)
                      Container(
                        decoration: BoxDecoration(
                          color: AColor.doveGray.withValues(alpha: 30),
                          borderRadius: const BorderRadius.only(
                            bottomLeft: Radius.circular(4),
                            bottomRight: Radius.circular(4),
                          ),
                        ),
                        child: Center(
                          child: IconButton(
                            icon: const Icon(
                              Icons.search,
                              size: 32,
                              color: AColor.white,
                            ),
                            onPressed: widget.onView,
                            tooltip: 'İncele'.hardcoded,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

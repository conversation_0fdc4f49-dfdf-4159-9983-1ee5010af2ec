part of '../add_personal_widgets.dart';

class AddPermitRecordWidgets extends HookConsumerWidget {
  const AddPermitRecordWidgets({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = GlobalKey<FormState>();

    final statusState = useState<EducationStatus>(EducationStatus.graduated);

    useEffect(
      () {},
      const [],
    );

    void saveEducationRecord() {
      // if (formKey.currentState?.validate() ?? false) {
      final newRecord = PermitRecord(
        startDate: DateTime(2022, 6, 15),
        endDate: DateTime(2022, 6, 18),
        period: '3',
        permitType: 'type',
        documentUrl: '',
      );

      final currentRecords = ref.read(permitRecordsProvider);
      ref.read(permitRecordsProvider.notifier).state = [
        ...currentRecords,
        newRecord,
      ];

      Navigator.pop(context);
      // }
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: formKey,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        child: Column(
          spacing: 16,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextWidget(
              'İzin Kaydı ekle'.hardcoded,
              style: ATextStyle.text12Bold,
            ),
            DateTimePickerField(
              header: 'İzin Başlangıç'.hardcoded,
              pickerMode: DateTimePickerMode.date,
              initialValue: DateTime.now(),
            ),
            DateTimePickerField(
              header: 'İzin Bitiş'.hardcoded,
              pickerMode: DateTimePickerMode.date,
              initialValue: DateTime.now(),
            ),
            CustomTextFormField(
              headerText: 'İzin Süresi'.hardcoded,
              keyboardType: TextInputType.name,
              textInputAction: TextInputAction.next,
              onChanged: (value) {},
            ),
            Container(
              decoration: CommonDecorations.containerDecoration(),
              child: ANewDropdownFormField<String>(
                header: 'İzin Türü'.hardcoded,
                placeholder: context.tr.select,
                itemList: [
                  'Yıllık ücretli izin'.hardcoded,
                  'Doğum izni'.hardcoded,
                  'Evlenme izni'.hardcoded,
                  'Babalık izni'.hardcoded,
                  'Engelli çocuk tedavisi izni'.hardcoded,
                  'Periyodik kontrol izni'.hardcoded,
                  'Süt izni'.hardcoded,
                  'Evlat edinme izni'.hardcoded,
                  'Yeni iş arama izni'.hardcoded,
                  'Mazeret izni'.hardcoded,
                  'Yarım gün doğum izni'.hardcoded,
                ],
                selectedItems: const [],
                onSelected: (item) {},
                validator: (value) => value == null || value.isEmpty
                    ? context.tr.fieldRequired
                    : null,
              ),
            ),
            SizedBox(
              width: 200,
              child: DocumentCard(
                title: 'Belge',
                documentUrl: '',
                onUpload: () {},
                onView: () {},
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AColor.white,
                        borderRadius: BorderRadius.circular(30),
                        border: Border.all(color: AColor.primaryColor),
                      ),
                      child: Text(
                        'İptal'.hardcoded,
                        style: ATextStyle.text12
                            .copyWith(color: AColor.primaryColor),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  InkWell(
                    onTap: saveEducationRecord,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AColor.primaryColor,
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Text(
                        context.tr.add,
                        style: ATextStyle.text12.copyWith(color: AColor.white),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

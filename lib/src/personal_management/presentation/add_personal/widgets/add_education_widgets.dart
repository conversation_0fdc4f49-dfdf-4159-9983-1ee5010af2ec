part of '../add_personal_widgets.dart';

class AddEducationWidgets extends HookConsumerWidget {
  const AddEducationWidgets({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = GlobalKey<FormState>();

    final statusState = useState<EducationStatus>(EducationStatus.graduated);
    final institutionController = useTextEditingController();
    final departmentController = useTextEditingController();
    final graduationDateController = useTextEditingController();

    useEffect(
      () {
        final now = DateTime.now();
        graduationDateController.text = '${now.day}/${now.month}/${now.year}';
        return null;
      },
      const [],
    );

    void saveEducationRecord() {
      if (formKey.currentState?.validate() ?? false) {
        final dateParts = graduationDateController.text.split('/');
        var graduationDate = DateTime.now();
        if (dateParts.length == 3) {
          try {
            graduationDate = DateTime(
              int.parse(dateParts[2]),
              int.parse(dateParts[1]),
              int.parse(dateParts[0]),
            );
          } catch (e) {
            // Use default date on parsing error
          }
        }

        final newRecord = EducationRecord(
          status: statusState.value,
          institution: institutionController.text,
          department: departmentController.text,
          graduationDate: graduationDate,
        );

        final currentRecords = ref.read(educationRecordsProvider);
        ref.read(educationRecordsProvider.notifier).state = [
          ...currentRecords,
          newRecord,
        ];

        Navigator.pop(context);
      }
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: formKey,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        child: Column(
          spacing: 16,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextWidget(
              'Eğitim Kaydı ekle'.hardcoded,
              style: ATextStyle.text12Bold,
            ),
            ANewDropdownFormField<String>(
              header: 'Durum'.hardcoded,
              placeholder: context.tr.select,
              itemList: EducationStatus.values.map((e) => e.label).toList(),
              onSelected: (value) {
                if (value != null) {
                  final status = EducationStatus.values
                      .firstWhere((e) => e.label == value);
                  statusState.value = status;
                }
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return context.tr.fieldRequired;
                }
                return null;
              },
            ),
            Container(
              decoration: CommonDecorations.containerDecoration(),
              child: ANewDropdownFormField<String>(
                header: 'Eğitim Kurumu'.hardcoded,
                placeholder: context.tr.select,
                itemList: [
                  'A'.hardcoded,
                  'B'.hardcoded,
                ],
                selectedItems: const [],
                onSelected: (item) {},
                validator: (value) => value == null || value.isEmpty
                    ? context.tr.fieldRequired
                    : null,
              ),
            ),
            CustomTextFormField(
              headerText: 'Bölümü'.hardcoded,
              keyboardType: TextInputType.name,
              textInputAction: TextInputAction.next,
              controller: departmentController,
              onChanged: (value) {},
              // validator: (value) => value.isValidName(context),
              // regexType: RegexType.name,
            ),
            DateTimePickerField(
              header: 'Mezuniyet Tarihi'.hardcoded,
              controller: graduationDateController,
              pickerMode: DateTimePickerMode.date,
              initialValue: DateTime.now(),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AColor.white,
                        borderRadius: BorderRadius.circular(30),
                        border: Border.all(color: AColor.primaryColor),
                      ),
                      child: Text(
                        'İptal'.hardcoded,
                        style: ATextStyle.text12
                            .copyWith(color: AColor.primaryColor),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  InkWell(
                    onTap: saveEducationRecord,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AColor.primaryColor,
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Text(
                        context.tr.add,
                        style: ATextStyle.text12.copyWith(color: AColor.white),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

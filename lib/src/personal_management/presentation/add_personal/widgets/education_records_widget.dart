part of '../add_personal_widgets.dart';

class EducationRecordsWidget extends HookConsumerWidget {
  const EducationRecordsWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final educationRecords = ref.watch(educationRecordsProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        TextWidget(
          'Eğitim Bilgileri'.hardcoded,
          style: ATextStyle.text12Bold,
        ),
        const SizedBox(height: 16),
        ...educationRecords.map(_buildEducationRecord),
        Row(
          children: [
            DynamicIconTextButton(
              title: '<PERSON>ğ<PERSON><PERSON>'.hardcoded,
              textStyle: ATextStyle.small.copyWith(
                fontWeight: FontWeight.bold,
                color: AColor.primaryColor,
              ),
              onTap: () {
                AppDialog.withoutHeader<void>(
                  context: context,
                  width: context.width * .50,
                  height: context.height * .50,
                  child: const AddEducationWidgets(),
                );
              },
              color: AColor.primaryColor,
              height: 30,
            ),
            const Spacer(),
          ],
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildEducationRecord(EducationRecord record) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Table(
          columnWidths: const {0: IntrinsicColumnWidth(), 1: FlexColumnWidth()},
          defaultVerticalAlignment: TableCellVerticalAlignment.middle,
          children: [
            _buildTableRow('Durum', record.status.label),
            _buildTableRow('Eğitim Kurumu', record.institution),
            _buildTableRow('Bölümü', record.department),
            _buildTableRow(
              'Mezuniyet Tarihi',
              '${record.graduationDate.day}.${record.graduationDate.month}.${record.graduationDate.year}',
            ),
          ],
        ),
      ),
    );
  }

  TableRow _buildTableRow(String label, String value) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Align(
            alignment: Alignment.centerLeft,
            child: TextWidget(label, style: ATextStyle.text12),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Align(
            alignment: Alignment.centerRight,
            child: TextWidget(value, style: ATextStyle.text12SemiBold),
          ),
        ),
      ],
    );
  }
}

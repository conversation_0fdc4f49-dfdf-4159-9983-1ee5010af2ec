part of '../add_personal_widgets.dart';

class AddGoodsRecordWidgets extends HookConsumerWidget {
  const AddGoodsRecordWidgets({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = GlobalKey<FormState>();

    useEffect(
      () {},
      const [],
    );

    void saveEducationRecord() {
      // if (formKey.currentState?.validate() ?? false) {
      final newRecord = GoodRecord(
          goodType: 'tip',
          goodDetail: 'detay',
          startDate: DateTime(2022, 6, 15),
          endDate: DateTime(2022, 6, 18),
          explanation: 'aciklama');

      final currentRecords = ref.read(goodsRecordsProvider);
      ref.read(goodsRecordsProvider.notifier).state = [
        ...currentRecords,
        newRecord,
      ];

      Navigator.pop(context);
      // }
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: formKey,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            TextWidget(
              'Zimmetli Mal Kaydı ekle'.hardcoded,
              style: ATextStyle.text12Bold,
            ),
            const SizedBox(height: 16),
            CustomTextFormField(
              headerText: 'Demirbaş Tipi'.hardcoded,
              keyboardType: TextInputType.name,
              textInputAction: TextInputAction.next,
              onChanged: (value) {},
            ),
            CustomTextFormField(
              headerText: 'Ürün Detayı'.hardcoded,
              keyboardType: TextInputType.name,
              textInputAction: TextInputAction.next,
              onChanged: (value) {},
            ),
            DateTimePickerField(
              header: 'Atama Tarihi'.hardcoded,
              pickerMode: DateTimePickerMode.date,
              initialValue: DateTime.now(),
            ),
            const SizedBox(height: 16),
            DateTimePickerField(
              header: 'İade Tarihi'.hardcoded,
              pickerMode: DateTimePickerMode.date,
              initialValue: DateTime.now(),
            ),
            const SizedBox(height: 16),
            CustomTextFormField(
              headerText: 'Açıklama'.hardcoded,
              keyboardType: TextInputType.name,
              textInputAction: TextInputAction.next,
              onChanged: (value) {},
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AColor.white,
                        borderRadius: BorderRadius.circular(30),
                        border: Border.all(color: AColor.primaryColor),
                      ),
                      child: Text(
                        'İptal'.hardcoded,
                        style: ATextStyle.text12
                            .copyWith(color: AColor.primaryColor),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  InkWell(
                    onTap: saveEducationRecord,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AColor.primaryColor,
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Text(
                        context.tr.add,
                        style: ATextStyle.text12.copyWith(color: AColor.white),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

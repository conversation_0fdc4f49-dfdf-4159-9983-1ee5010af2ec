part of '../add_personal_widgets.dart';

class AddDutySalaryRecordWidgets extends HookConsumerWidget {
  const AddDutySalaryRecordWidgets({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = GlobalKey<FormState>();

    useEffect(
      () {},
      const [],
    );

    void saveEducationRecord() {
      // if (formKey.currentState?.validate() ?? false) {
      final newRecord = DutySalary(
        department: 'departman',
        position: 'pozisyon',
        startDate: DateTime(2022, 6, 15),
        status: 'durum',
        brutSalary: '35',
      );

      final currentRecords = ref.read(dutySalaryRecordsProvider);
      ref.read(dutySalaryRecordsProvider.notifier).state = [
        ...currentRecords,
        newRecord,
      ];

      Navigator.pop(context);
      // }
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: formKey,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            TextWidget(
              'Görevlendirme ve Maaş Bilgi Kaydı ekle'.hardcoded,
              style: ATextStyle.text12Bold,
            ),
            const SizedBox(height: 16),
            CustomTextFormField(
              headerText: 'Departman'.hardcoded,
              keyboardType: TextInputType.name,
              textInputAction: TextInputAction.next,
              onChanged: (value) {},
            ),
            CustomTextFormField(
              headerText: 'Pozisyon'.hardcoded,
              keyboardType: TextInputType.name,
              textInputAction: TextInputAction.next,
              onChanged: (value) {},
            ),
            DateTimePickerField(
              header: 'Başlangıç Tarihi'.hardcoded,
              pickerMode: DateTimePickerMode.date,
              initialValue: DateTime.now(),
            ),
            const SizedBox(height: 16),
            CustomTextFormField(
              headerText: 'Durum'.hardcoded,
              keyboardType: TextInputType.name,
              textInputAction: TextInputAction.next,
              onChanged: (value) {},
            ),
            const SizedBox(height: 16),
            CustomTextFormField(
              headerText: 'Brüt Maaş'.hardcoded,
              keyboardType: TextInputType.name,
              textInputAction: TextInputAction.next,
              onChanged: (value) {},
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AColor.white,
                        borderRadius: BorderRadius.circular(30),
                        border: Border.all(color: AColor.primaryColor),
                      ),
                      child: Text(
                        'İptal'.hardcoded,
                        style: ATextStyle.text12
                            .copyWith(color: AColor.primaryColor),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  InkWell(
                    onTap: saveEducationRecord,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AColor.primaryColor,
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Text(
                        context.tr.add,
                        style: ATextStyle.text12.copyWith(color: AColor.white),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

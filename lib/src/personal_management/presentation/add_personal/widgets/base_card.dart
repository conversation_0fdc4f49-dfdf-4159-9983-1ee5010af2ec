part of '../add_personal_widgets.dart';

class _BaseCard extends StatelessWidget {
  const _BaseCard({
    required this.title,
    required this.child,
    this.onEdit,
  });

  final String title;
  final Widget child;
  final VoidCallback? onEdit;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AColor.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: AColor.pinBorderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            decoration: const BoxDecoration(
              color: AColor.lightGreyBackground,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
            child: ListTile(
              dense: true,
              title: TextWidget(title, style: ATextStyle.text14),
              trailing: onEdit != null
                  ? IconButton(
                      padding: EdgeInsets.zero,
                      icon: SmartTeamAssets.icons.edit.svg(),
                      onPressed: onEdit,
                    )
                  : null,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: child,
          ),
        ],
      ),
    );
  }
}

part of 'add_personal_widgets.dart';

class _PersonalInfo extends HookConsumerWidget {
  const _PersonalInfo({
    required this.initialPersonalInfo,
  });

  final PersonalInfoModel? initialPersonalInfo;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final educationRecords = ref.watch(educationRecordsProvider);

    final birthDateController =
        useState<DateTime>(initialPersonalInfo?.birthDate ?? DateTime.now());
    final genderController = useState<String?>(initialPersonalInfo?.gender);
    final nationalityController =
        useState<String?>(initialPersonalInfo?.nationality);
    final maritalStatusController =
        useState<String?>(initialPersonalInfo?.maritalStatus);

    final idNumberController = useTextEditingController(
      text: initialPersonalInfo?.idNumber ?? '',
    );
    final childrenCountController = useTextEditingController(
      text: initialPersonalInfo?.childrenCount ?? '',
    );
    final businessEmailController = useTextEditingController(
      text: initialPersonalInfo?.businessEmail ?? '',
    );
    final personalEmailController = useTextEditingController(
      text: initialPersonalInfo?.personalEmail ?? '',
    );
    final businessPhoneController = useTextEditingController(
      text: initialPersonalInfo?.businessPhone ?? '',
    );
    final personalPhoneController = useTextEditingController(
      text: initialPersonalInfo?.personalPhone ?? '',
    );
    final addressController = useTextEditingController(
      text: initialPersonalInfo?.address ?? '',
    );
    final emergencyContactNameController = useTextEditingController(
      text: initialPersonalInfo?.emergencyContact?.name ?? '',
    );
    final emergencyContactPhoneController = useTextEditingController(
      text: initialPersonalInfo?.emergencyContact?.phone ?? '',
    );
    final emergencyContactRelationController = useTextEditingController(
      text: initialPersonalInfo?.emergencyContact?.relation ?? '',
    );

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 40),
        child: Column(
          spacing: 16,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextWidget(
              'Profil'.hardcoded,
              style: ATextStyle.text13,
            ),
            Row(
              spacing: 16,
              children: [
                Expanded(
                  child: DateTimePickerField(
                    header: 'Doğum Tarihi'.hardcoded,
                    pickerMode: DateTimePickerMode.date,
                    initialValue: birthDateController.value,
                  ),
                ),
                Expanded(
                  child: Container(
                    decoration: CommonDecorations.containerDecoration(),
                    child: ANewDropdownFormField<String>(
                      header: 'Cinsiyeti'.hardcoded,
                      placeholder: context.tr.select,
                      itemList: ['Erkek'.hardcoded, 'Kadın'.hardcoded],
                      selectedItems: genderController.value != null
                          ? [genderController.value!]
                          : const [],
                      onSelected: (item) => genderController.value = item,
                      validator: (v) => v == null || v.isEmpty
                          ? context.tr.fieldRequired
                          : null,
                    ),
                  ),
                ),
              ],
            ),
            Row(
              spacing: 16,
              children: [
                Expanded(
                  child: Container(
                    decoration: CommonDecorations.containerDecoration(),
                    child: ANewDropdownFormField<String>(
                      header: 'Uyruğu'.hardcoded,
                      placeholder: context.tr.select,
                      itemList: ['Türk'.hardcoded],
                      selectedItems: nationalityController.value != null
                          ? [nationalityController.value!]
                          : const [],
                      onSelected: (item) => nationalityController.value = item,
                      validator: (v) => v == null || v.isEmpty
                          ? context.tr.fieldRequired
                          : null,
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    decoration: CommonDecorations.containerDecoration(),
                    child: CustomTextFormField(
                      headerText: 'Kimlik Numarası'.hardcoded,
                      controller: idNumberController,
                      keyboardType: TextInputType.number,
                      textInputAction: TextInputAction.next,
                      regexType: RegexType.id,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(11),
                      ],
                      validator: (v) => v.isValidId(context),
                    ),
                  ),
                ),
              ],
            ),
            Row(
              spacing: 16,
              children: [
                Expanded(
                  child: Container(
                    decoration: CommonDecorations.containerDecoration(),
                    child: ANewDropdownFormField<String>(
                      header: 'Medeni Hali'.hardcoded,
                      placeholder: context.tr.select,
                      itemList: ['Evli'.hardcoded, 'Bekar'.hardcoded],
                      selectedItems: maritalStatusController.value != null
                          ? [maritalStatusController.value!]
                          : const [],
                      onSelected: (item) =>
                          maritalStatusController.value = item,
                      validator: (v) => v == null || v.isEmpty
                          ? context.tr.fieldRequired
                          : null,
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    decoration: CommonDecorations.containerDecoration(),
                    child: CustomTextFormField(
                      headerText: 'Çocuk Sayısı'.hardcoded,
                      controller: childrenCountController,
                      keyboardType: TextInputType.number,
                      textInputAction: TextInputAction.next,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(2),
                      ],
                      validator: (v) => v == null || v.isEmpty
                          ? context.tr.fieldRequired
                          : null,
                    ),
                  ),
                ),
              ],
            ),
            TextWidget(
              'İletişim Bilgileri'.hardcoded,
              style: ATextStyle.text12Bold,
            ),
            _buildMailPhoneRows(
              businessEmailController,
              personalEmailController,
              businessPhoneController,
              personalPhoneController,
              context,
            ),
            Row(
              spacing: 16,
              children: [
                Expanded(
                  child: _buildField(
                    label: 'Açık Adres'.hardcoded,
                    controller: addressController,
                  ),
                ),
                const Spacer(),
              ],
            ),
            _buildEmergencyRows(
              emergencyContactNameController,
              emergencyContactPhoneController,
              emergencyContactRelationController,
              context,
            ),
            TextWidget(
              'Eğitim Bilgileri'.hardcoded,
              style: ATextStyle.text12Bold,
            ),
            Row(
              spacing: 16,
              children: [
                Expanded(
                  child: Column(
                    children:
                        educationRecords.map(_buildEducationRecord).toList(),
                  ),
                ),
                const Spacer(),
              ],
            ),
            Row(
              spacing: 16,
              children: [
                DynamicIconTextButton(
                  title: 'Eğitim Kaydı Ekle'.hardcoded,
                  textStyle: ATextStyle.small.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AColor.primaryColor,
                  ),
                  onTap: () => AppDialog.withoutHeader<void>(
                    context: context,
                    width: context.width * .50,
                    height: context.height * .50,
                    child: const AddEducationWidgets(),
                  ),
                  color: AColor.primaryColor,
                  height: 30,
                ),
                const Spacer(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMailPhoneRows(
    TextEditingController businessEmail,
    TextEditingController personalEmail,
    TextEditingController businessPhone,
    TextEditingController personalPhone,
    BuildContext context,
  ) {
    return Column(
      children: [
        Row(
          spacing: 16,
          children: [
            Expanded(
              child: _buildLabeledInput(
                label: 'İş E-posta Adresi'.hardcoded,
                controller: businessEmail,
                keyboardType: TextInputType.emailAddress,
                regex: RegexType.eMail,
                validator: (v) => v.isValidMail(context),
              ),
            ),
            Expanded(
              child: _buildLabeledInput(
                label: 'Kişisel E-posta Adresi'.hardcoded,
                controller: personalEmail,
                keyboardType: TextInputType.emailAddress,
                regex: RegexType.eMail,
                validator: (v) => v.isValidMail(context),
              ),
            ),
          ],
        ),
        Row(
          spacing: 16,
          children: [
            Expanded(
              child: _buildLabeledInput(
                label: 'İş Telefonu'.hardcoded,
                controller: businessPhone,
                keyboardType: TextInputType.phone,
                regex: RegexType.phone,
                validator: (v) => v.isValidPhone(context),
              ),
            ),
            Expanded(
              child: _buildLabeledInput(
                label: 'Kişisel Telefonu'.hardcoded,
                controller: personalPhone,
                keyboardType: TextInputType.phone,
                regex: RegexType.phone,
                validator: (v) => v.isValidPhone(context),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEmergencyRows(
    TextEditingController nameCtrl,
    TextEditingController phoneCtrl,
    TextEditingController relationCtrl,
    BuildContext context,
  ) {
    return Column(
      children: [
        Row(
          spacing: 16,
          children: [
            Expanded(
              child: _buildField(
                label: 'Acil Durum Adı-Soyadı'.hardcoded,
                controller: nameCtrl,
              ),
            ),
            Expanded(
              child: _buildLabeledInput(
                label: 'Acil Durum Telefonu'.hardcoded,
                controller: phoneCtrl,
                keyboardType: TextInputType.phone,
                regex: RegexType.phone,
                validator: (v) => v.isValidPhone(context),
              ),
            ),
          ],
        ),
        Row(
          spacing: 16,
          children: [
            Expanded(
              child: _buildField(
                label: 'Yakınlık Derecesi'.hardcoded,
                controller: relationCtrl,
              ),
            ),
            const Spacer(),
          ],
        ),
      ],
    );
  }

  Widget _buildLabeledInput({
    required String label,
    required TextEditingController controller,
    TextInputType keyboardType = TextInputType.text,
    RegexType? regex,
    String? Function(String?)? validator,
  }) {
    return Column(
      spacing: 8,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(label.hardcoded, style: ATextStyle.small),
        Container(
          decoration: CommonDecorations.containerDecoration(),
          child: CustomTextFormField(
            controller: controller,
            keyboardType: keyboardType,
            textInputAction: TextInputAction.next,
            regexType: regex,
            validator: validator,
          ),
        ),
      ],
    );
  }

  Widget _buildEducationRecord(EducationRecord record) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Table(
          columnWidths: const {0: IntrinsicColumnWidth(), 1: FlexColumnWidth()},
          defaultVerticalAlignment: TableCellVerticalAlignment.middle,
          children: [
            _buildTableRow('Durum'.hardcoded, record.status.label),
            _buildTableRow('Eğitim Kurumu'.hardcoded, record.institution),
            _buildTableRow('Bölümü'.hardcoded, record.department),
            _buildTableRow(
              'Mezuniyet Tarihi'.hardcoded,
              '${record.graduationDate.day}.${record.graduationDate.month}.${record.graduationDate.year}',
            ),
          ],
        ),
      ),
    );
  }

  TableRow _buildTableRow(String label, String value) => TableRow(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Align(
              alignment: Alignment.centerLeft,
              child: TextWidget(label, style: ATextStyle.text12),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Align(
              alignment: Alignment.centerRight,
              child: TextWidget(value, style: ATextStyle.text12SemiBold),
            ),
          ),
        ],
      );

  Widget _buildField({
    required String label,
    required TextEditingController controller,
    String? hint,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
  }) =>
      _buildLabeledInput(
        label: label,
        controller: controller,
        keyboardType: keyboardType,
        validator: validator,
      );
}

part of 'add_personal_widgets.dart';

class _Documents extends StatelessWidget {
  const _Documents({this.member});
  final WebTeamMemberModel? member;

  @override
  Widget build(BuildContext context) {
    final types = <_DocumentType>[
      const _DocumentType(key: 'idCard', label: 'Kimlik Kartı'),
      const _DocumentType(key: 'residence', label: 'İkametgah Belgesi'),
      const _DocumentType(key: 'record', label: '<PERSON>üfus Kayıt Örneği'),
      const _DocumentType(key: 'criminal', label: '<PERSON><PERSON>l <PERSON>'),
      const _DocumentType(key: 'graduation', label: 'Mezuniyet Belgesi'),
      const _DocumentType(
          key: 'familyStatus', label: 'Aile Durumunu Bildirir Belge'),
      const _DocumentType(
          key: 'military', label: 'Askerlik Durumunu Gösterir Belge'),
      const _DocumentType(
          key: 'bankInfo', label: '<PERSON><PERSON><PERSON> / Banka Bilgisi'),
      const _DocumentType(key: 'workRecord', label: '<PERSON>ş‑Ku<PERSON> Kaydı'),
    ];

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 16),
        child: LayoutBuilder(
          builder: (context, constraints) {
            const itemsPerRow = 6;
            const spacing = 16.0;
            const totalSpacing = spacing * (itemsPerRow - 1);
            final cardWidth =
                (constraints.maxWidth - totalSpacing) / itemsPerRow;

            final cards = types.map((t) {
              final url = member?.documents[t.key];
              return SizedBox(
                width: cardWidth,
                child: DocumentCard(
                  title: t.label,
                  documentUrl: url,
                  onUpload: () {},
                  onView: url != null ? () {} : null,
                ),
              );
            }).toList()
              ..add(
                SizedBox(
                  width: cardWidth,
                  child: GestureDetector(
                    onTap: () {
                      AppDialog.withoutHeader<void>(
                        context: context,
                        width: context.width * .50,
                        height: context.height * .50,
                        child: const AddDocumentWidgets(),
                      );
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: AColor.lightGreyBackground,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: AColor.pinBorderColor),
                      ),
                      child: const Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          SizedBox(
                            height: 20,
                          ),
                          Padding(
                            padding: EdgeInsets.all(8),
                            child: AspectRatio(
                              aspectRatio: 3 / 4,
                              child: Center(
                                child: Icon(
                                  Icons.add,
                                  size: 32,
                                  color: AColor.primaryColor,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 20,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );

            return Wrap(
              spacing: spacing,
              runSpacing: spacing,
              alignment: WrapAlignment.center,
              children: cards,
            );
          },
        ),
      ),
    );
  }
}

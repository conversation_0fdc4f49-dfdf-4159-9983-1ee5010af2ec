part of 'add_personal_widgets.dart';

class _WorkInfo extends HookConsumerWidget {
  const _WorkInfo({required this.initialWorkInfo});

  final WorkInfoModel? initialWorkInfo;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final permitRecords = ref.watch(permitRecordsProvider);
    final goodsRecords = ref.watch(goodsRecordsProvider);
    final dutySalaryRecords = ref.watch(dutySalaryRecordsProvider);

    final joinDate = useState<DateTime>(
      initialWorkInfo?.joinDate ?? DateTime.now(),
    );
    final selectedWorkType = useState<List<DisplayWorkType>>(
      initialWorkInfo?.workType != null
          ? [
              if (initialWorkInfo?.workType != null)
                DisplayWorkType(
                  newUserType: initialWorkInfo!.workType!,
                  context: context,
                ),
            ]
          : <DisplayWorkType>[],
    );

    final durationCtrl = useTextEditingController(
      text: _formatDuration(joinDate.value, DateTime.now()),
    );
    useEffect(
      () {
        durationCtrl.text = _formatDuration(joinDate.value, DateTime.now());
        return null;
      },
      [joinDate.value],
    );

    final usedLeaveDays = initialWorkInfo?.usedAnnualLeave ?? 0;
    final remainingLeaveDays = initialWorkInfo?.remainingAnnualLeave ?? 0;

    // ── UI ────────────────────────────────────────────────────
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 16,
          children: [
            TextWidget(
              'Çalışma Bilgileri'.hardcoded,
              style: ATextStyle.text12Bold,
            ),
            Row(
              spacing: 16,
              children: [
                Expanded(
                  child: Column(
                    spacing: 8,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextWidget(
                        'İşe Başlama Tarihi'.hardcoded,
                        style: ATextStyle.small,
                      ),
                      DateTimePickerField(
                        pickerMode: DateTimePickerMode.date,
                        initialValue: joinDate.value,
                        controller: TextEditingController(
                          text:
                              '${joinDate.value.day}/${joinDate.value.month}/${joinDate.value.year}',
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: _buildField(
                    label: 'Çalışma Süresi'.hardcoded,
                    controller: durationCtrl,
                    readOnly: true,
                  ),
                ),
              ],
            ),
            Row(
              spacing: 16,
              children: [
                Expanded(
                  child: Column(
                    spacing: 8,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextWidget(
                        'Çalışma Türü'.hardcoded,
                        style: ATextStyle.small,
                      ),
                      ANewDropDown<DisplayWorkType>(
                        placeholder: context.tr.select,
                        itemList: DisplayWorkType.getList(context),
                        selectedItems: selectedWorkType.value,
                        onSelected: (type) => selectedWorkType.value = [type],
                        itemBuilder: (type) => Text(
                          type?.label ?? context.tr.select,
                          style: ATextStyle.text14,
                        ),
                      ),
                    ],
                  ),
                ),
                const Spacer(),
              ],
            ),

            // ── Zimmet ────────────────────────────────────────
            TextWidget(
              'Üzerine Zimmetli Mallar'.hardcoded,
              style: ATextStyle.text12Bold,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: _responsiveWrap(
                goodsRecords,
                _buildGoodRecord,
              ),
            ),
            _addButton(
              context,
              label: 'Zimmetli Mal Ekle'.hardcoded,
              child: const AddGoodsRecordWidgets(),
            ),

            // ── Görev / Maaş ──────────────────────────────────
            TextWidget(
              'Görevlendirme ve Maaş Bilgileri'.hardcoded,
              style: ATextStyle.text12Bold,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: _responsiveWrap(
                dutySalaryRecords,
                _buildDutySalaryRecord,
              ),
            ),
            _addButton(
              context,
              label: 'Görevlendirme ve Maaş Kaydı Ekle'.hardcoded,
              child: const AddDutySalaryRecordWidgets(),
            ),

            // ── İzinler ──────────────────────────────────────
            TextWidget(
              'İzin Kullanımları'.hardcoded,
              style: ATextStyle.text12Bold,
            ),
            _leaveCounters(usedLeaveDays, remainingLeaveDays),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: _responsiveWrap(
                permitRecords,
                _buildPermitRecord,
              ),
            ),
            _addButton(
              context,
              label: 'İzin Kaydı Ekle'.hardcoded,
              child: const AddPermitRecordWidgets(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermitRecord(PermitRecord record) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Table(
          columnWidths: const {0: IntrinsicColumnWidth(), 1: FlexColumnWidth()},
          defaultVerticalAlignment: TableCellVerticalAlignment.middle,
          children: [
            _buildTableRow(
              'İzin Başlangıç',
              '${record.startDate.day}.${record.startDate.month}.${record.startDate.year}',
            ),
            _buildTableRow(
              'İzin Bitiş',
              '${record.endDate.day}.${record.endDate.month}.${record.endDate.year}',
            ),
            _buildTableRow('İzin Süresi', record.period),
            _buildTableRow(
              'İzin Türü',
              record.permitType,
            ),
            _buildTableRow(
              'Belge Görüntüle',
              record.documentUrl,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDutySalaryRecord(DutySalary record) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Table(
          columnWidths: const {0: IntrinsicColumnWidth(), 1: FlexColumnWidth()},
          defaultVerticalAlignment: TableCellVerticalAlignment.middle,
          children: [
            _buildTableRow('Departman', record.department),
            _buildTableRow('Pozisyon', record.position),
            _buildTableRow(
              'Başlangıç Tarihi',
              '${record.startDate.day}.${record.startDate.month}.${record.startDate.year}',
            ),
            _buildTableRow(
              'Durum',
              record.status,
            ),
            _buildTableRow(
              'Brüt Maaş',
              record.brutSalary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoodRecord(GoodRecord record) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Table(
          columnWidths: const {0: IntrinsicColumnWidth(), 1: FlexColumnWidth()},
          defaultVerticalAlignment: TableCellVerticalAlignment.middle,
          children: [
            _buildTableRow('Demirbaş Tipi', record.goodType),
            _buildTableRow(
              'Ürün Detayı',
              record.goodDetail,
            ),
            _buildTableRow(
              'Atama Tarihi',
              '${record.startDate.day}.${record.startDate.month}.${record.startDate.year}',
            ),
            _buildTableRow(
              'İade Tarihi',
              '${record.endDate.day}.${record.endDate.month}.${record.endDate.year}',
            ),
            _buildTableRow(
              'Açıklama',
              record.explanation,
            ),
          ],
        ),
      ),
    );
  }

  TableRow _buildTableRow(String label, String value) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Align(
            alignment: Alignment.centerLeft,
            child: TextWidget(label, style: ATextStyle.text12),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Align(
            alignment: Alignment.centerRight,
            child: TextWidget(value, style: ATextStyle.text12SemiBold),
          ),
        ),
      ],
    );
  }

  // ── yardımcı widget’lar ─────────────────────────────────────
  Widget _responsiveWrap<T>(
    List<T> list,
    Widget Function(T) itemBuilder,
  ) =>
      LayoutBuilder(
        builder: (context, constraints) {
          const spacing = 16.0;
          final cardWidth = (constraints.maxWidth - spacing) / 2;
          return Wrap(
            spacing: spacing,
            runSpacing: spacing,
            children: list
                .map((e) => SizedBox(width: cardWidth, child: itemBuilder(e)))
                .toList(),
          );
        },
      );

  Widget _addButton(
    BuildContext context, {
    required String label,
    required Widget child,
  }) =>
      Row(
        spacing: 16,
        children: [
          DynamicIconTextButton(
            title: label,
            textStyle: ATextStyle.small.copyWith(
              fontWeight: FontWeight.bold,
              color: AColor.primaryColor,
            ),
            onTap: () => AppDialog.withoutHeader<void>(
              context: context,
              width: context.width * .50,
              height: context.height * .50,
              child: child,
            ),
            color: AColor.primaryColor,
            height: 30,
          ),
          const Spacer(),
        ],
      );

  Widget _leaveCounters(int used, int remaining) => Row(
        spacing: 16,
        children: [
          Expanded(
            child: _buildField(
              label: 'Kullanılan Yıllık İzin'.hardcoded,
              controller: TextEditingController(text: '$used'),
              readOnly: true,
            ),
          ),
          Expanded(
            child: _buildField(
              label: 'Kalan Yıllık İzin Hakkı'.hardcoded,
              controller: TextEditingController(text: '$remaining'),
              readOnly: true,
            ),
          ),
        ],
      );

  // ── field helper ───────────────────────────────────────────
  Widget _buildField({
    required String label,
    TextEditingController? controller,
    bool readOnly = false,
    TextInputType keyboardType = TextInputType.text,
  }) =>
      Container(
        decoration: CommonDecorations.containerDecoration(),
        child: CustomTextFormField(
          headerText: label,
          controller: controller,
          keyboardType: keyboardType,
          textInputAction: TextInputAction.next,
          readOnly: readOnly,
        ),
      );

  // ── süre formatlayıcı ──────────────────────────────────────
  String _formatDuration(DateTime start, DateTime end) {
    final diff = end.difference(start);
    final months = (diff.inDays / 30).floor();
    final years = months ~/ 12;
    final mLeft = months % 12;
    if (years == 0) return '$mLeft ay';
    if (mLeft == 0) return '$years yıl';
    return '$years yıl $mLeft ay';
  }
}

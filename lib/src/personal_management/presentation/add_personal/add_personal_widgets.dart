import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_common/smart_team_common.dart' show SmartTeamAssets;
import 'package:smart_team_web/src/home/<USER>/emergency_contact_model.dart';
import 'package:smart_team_web/src/home/<USER>/personal_info_model.dart';
import 'package:smart_team_web/src/home/<USER>/team_member.dart';
import 'package:smart_team_web/src/home/<USER>/work_info_model.dart';
import 'package:smart_team_web/src/main/enum/education_status_enum.dart';
import 'package:smart_team_web/src/personal_management/application/selected_member_provider.dart';
import 'package:smart_team_web/src/personal_management/model/duty_salary_record.dart';
import 'package:smart_team_web/src/personal_management/model/education_record.dart';
import 'package:smart_team_web/src/personal_management/model/good_record.dart';
import 'package:smart_team_web/src/personal_management/model/permit_record.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/enums/date_time_picker_mode.dart';
import 'package:smart_team_web/src/shared/enums/regex_type.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/toast/toast_manager.dart';
import 'package:smart_team_web/src/users/enum/work_type.dart';
import 'package:smart_team_web/src/widgets/app_dialog/app_dialog.dart';
import 'package:smart_team_web/src/widgets/button/dynamic_icon_text_button.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/dropdown/a_new_dropdown.dart';
import 'package:smart_team_web/src/widgets/form_fields/dropdown_formfield.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';
import 'package:smart_team_web/src/widgets/text_form_field/date_time_picker_field.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

part 'detail_tabs.dart';
part 'personal_info.dart';
part 'widgets/add_education_widgets.dart';
part 'widgets/education_records_widget.dart';
part 'widgets/add_permit_record_widgets.dart';
part 'widgets/add_goods_record_widgets.dart';
part 'widgets/add_duty_salary_record_widgets.dart';
part 'widgets/add_document_widgets.dart';
part 'documents.dart';
part 'widgets/base_card.dart';
part 'work_info.dart';
part 'widgets/document_type.dart';

class AddPersonalWidgets extends HookConsumerWidget {
  const AddPersonalWidgets({
    super.key,
    this.initialTabIndex = 0,
  });

  final int initialTabIndex;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final member = ref.watch(selectedMemberProvider);
    final formKey = GlobalKey<FormState>();
    final tabIndex = useState<int>(initialTabIndex);

    final nameController = useTextEditingController(
      text: member != null ? member.userName.split(' ').first : '',
    );
    final surnameController = useTextEditingController(
      text: member != null && member.userName.split(' ').length > 1
          ? member.userName.split(' ').last
          : '',
    );
    final phoneController = useTextEditingController(
      text: member?.phoneNumber ?? '',
    );
    final emailController = useTextEditingController(
      text: member?.emailAddress ?? '',
    );
    final departmentController = useTextEditingController(
      text: member?.department ?? '',
    );
    final positionController = useTextEditingController(
      text: member?.position ?? '',
    );

    // Access personal info if available
    final personalInfo = member?.personalInfo;

    // Pre-populate personal info controllers if available
    final birthDateController = useState<DateTime>(
      personalInfo?.birthDate ?? DateTime.now(),
    );
    final genderController = useState<String?>(personalInfo?.gender);
    final nationalityController = useState<String?>(personalInfo?.nationality);
    final idNumberController = useTextEditingController(
      text: personalInfo?.idNumber ?? '',
    );
    final maritalStatusController =
        useState<String?>(personalInfo?.maritalStatus);
    final childrenCountController = useTextEditingController(
      text: personalInfo?.childrenCount ?? '',
    );
    final businessEmailController = useTextEditingController(
      text: personalInfo?.businessEmail ?? '',
    );
    final personalEmailController = useTextEditingController(
      text: personalInfo?.personalEmail ?? '',
    );
    final businessPhoneController = useTextEditingController(
      text: personalInfo?.businessPhone ?? '',
    );
    final personalPhoneController = useTextEditingController(
      text: personalInfo?.personalPhone ?? '',
    );
    final addressController = useTextEditingController(
      text: personalInfo?.address ?? '',
    );
    final emergencyContactNameController = useTextEditingController(
      text: personalInfo?.emergencyContact?.name ?? '',
    );
    final emergencyContactPhoneController = useTextEditingController(
      text: personalInfo?.emergencyContact?.phone ?? '',
    );
    final emergencyContactRelationController = useTextEditingController(
      text: personalInfo?.emergencyContact?.relation ?? '',
    );

    return Form(
      key: formKey,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 24),
              child: Row(
                spacing: 32,
                children: [
                  Row(
                    spacing: 8,
                    children: [
                      CircleAvatar(
                        radius: 32,
                        backgroundImage: member?.imageUrl != null
                            ? NetworkImage(member!.imageUrl!)
                            : null,
                        backgroundColor: member?.imageUrl == null
                            ? Colors.grey
                            : Colors.transparent,
                      ),
                      DynamicIconTextButton(
                        title: 'Yeni Profil Fotoğrafı'.hardcoded,
                        textStyle: ATextStyle.small.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        onTap: () async {},
                        suffixIcon: Icons.file_upload_outlined,
                        color: AColor.textColor,
                        height: 25,
                      ),
                    ],
                  ),
                  Expanded(
                    flex: 5,
                    child: Row(
                      spacing: 16,
                      children: [
                        Expanded(
                          flex: 2,
                          child: Container(
                            decoration: CommonDecorations.containerDecoration(),
                            child: CustomTextFormField(
                              headerText: 'Adı'.hardcoded,
                              controller: nameController,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Container(
                            decoration: CommonDecorations.containerDecoration(),
                            child: CustomTextFormField(
                              headerText: 'Soyadı'.hardcoded,
                              controller: surnameController,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: _DetailTabs(
                index: tabIndex.value,
                onChanged: (i) => tabIndex.value = i,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: context.height * 0.6,
              child: IndexedStack(
                index: tabIndex.value,
                children: [
                  _PersonalInfo(
                    initialPersonalInfo: personalInfo,
                  ),
                  _WorkInfo(
                    initialWorkInfo: member?.workInfo,
                  ),
                  _Documents(member: member),
                ],
              ),
            ),
            const SizedBox(height: 24),
            LoadingElevatedButton(
              height: 45,
              onPressed: () async {
                if (!formKey.currentState!.validate()) {
                  ref.read(toastManagerProvider).showToastErrorWithMessage(
                        'Lütfen zorunlu alanları doldurun'.hardcoded,
                      );
                  return;
                }

                // Create or update the team member
                final userNameStr =
                    '${nameController.text} ${surnameController.text}'.trim();

                // Create emergency contact model
                final emergencyContact = EmergencyContactModel(
                  name: emergencyContactNameController.text,
                  phone: emergencyContactPhoneController.text,
                  relation: emergencyContactRelationController.text,
                );

                // Create personal info model
                final personalInfo = PersonalInfoModel(
                  id: member?.personalInfo?.id ?? DateTime.now().toString(),
                  birthDate: birthDateController.value,
                  gender: genderController.value,
                  nationality: nationalityController.value,
                  idNumber: idNumberController.text,
                  maritalStatus: maritalStatusController.value,
                  childrenCount: childrenCountController.text,
                  businessEmail: businessEmailController.text,
                  personalEmail: personalEmailController.text,
                  businessPhone: businessPhoneController.text,
                  personalPhone: personalPhoneController.text,
                  address: addressController.text,
                  emergencyContact: emergencyContact,
                );

                // Create or update team member
                final updatedMember = WebTeamMemberModel(
                  id: member?.id ?? DateTime.now().toString(),
                  userName: userNameStr,
                  teamId: member?.teamId ?? '',
                  emailAddress: emailController.text,
                  department: departmentController.text,
                  position: positionController.text,
                  phoneNumber: phoneController.text,
                  isActive: member?.isActive ?? true,
                  imageUrl: member?.imageUrl,
                  joinedAt: member?.joinedAt ?? DateTime.now(),
                  documents: member?.documents ?? {},
                  personalInfo: personalInfo,
                  educationRecords: ref.read(educationRecordsProvider),
                  permitRecords: ref.read(permitRecordsProvider),
                  dutySalaryRecords: ref.read(dutySalaryRecordsProvider),
                  goodRecords: ref.read(goodsRecordsProvider),
                );

                // Update member in provider or save to backend
                // TODO: Implement actual save functionality

                // Show success message
                ref.read(toastManagerProvider).showToast(
                      'Kayıt başarıyla tamamlandı'.hardcoded,
                    );
              },
              text: context.tr.save,
            ),
          ],
        ),
      ),
    );
  }
}

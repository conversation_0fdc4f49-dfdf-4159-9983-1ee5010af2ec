import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/core/router/auto_router.gr.dart';
import 'package:smart_team_web/src/home/<USER>/team_member.dart';
import 'package:smart_team_web/src/home/<USER>/mock_data.dart';
import 'package:smart_team_web/src/personal_management/application/selected_member_provider.dart';
import 'package:smart_team_web/src/personal_management/constants/personal_management_constants.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/users/enum/work_type.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/hover_builder.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/widgets/bottom_navigation_buttons.dart';
import 'package:smart_team_web/src/widgets/multi_select_filter_dropdown/date_range_dropdown.dart';
import 'package:smart_team_web/src/widgets/multi_select_filter_dropdown/multi_select_filter_dropdown_widget.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

part 'components/employee_card.dart';
part 'components/left_container.dart';
part 'components/right_container.dart';
part 'widgets/search_field.dart';

@RoutePage(name: 'PersonalManagementRoute')
class PersonalManagementView extends HookConsumerWidget {
  const PersonalManagementView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final leftWidth = PersonalManagementConstants.leftContainerWidth(context);

    return Scaffold(
      backgroundColor: AColor.backgroundColor,
      body: Padding(
        padding: context.responsive(
          desktop: const EdgeInsets.all(24),
          tablet: const EdgeInsets.all(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: PersonalManagementConstants.formSpacing,
              ),
              child: Align(
                child: TextWidget(
                  'Personel Yönetimi'.hardcoded,
                  style: ATextStyle.semiLarge.copyWith(
                    color: AColor.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              /// For now adding new employee is disabled
              /* Row(
                children: [
                  const Spacer(),
                  Expanded(
                    child: TextWidget(
                      'Personel Yönetimi'.hardcoded,
                      style: ATextStyle.semiLarge.copyWith(
                        color: AColor.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  LoadingElevatedButton(
                    height: 35,
                    onPressed: () async {
                      await AppDialog.show<void>(
                        context: context,
                        title: 'Yeni Personel Ekle'.hardcoded,
                        width: context.width * .95,
                        height: context.height * .95,
                        child: const AddPersonalWidgets(),
                      );
                    },
                    text: 'Yeni Personel Ekle +'.hardcoded,
                    backgroundColor: AColor.primaryColor,
                  ),
                ],
              ), */
            ),
            const SizedBox(height: PersonalManagementConstants.formSpacing),
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: leftWidth,
                    child: const LeftContainer(),
                  ),
                  const SizedBox(
                      width: PersonalManagementConstants.formSpacing),
                  const Expanded(
                    child: RightContainer(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

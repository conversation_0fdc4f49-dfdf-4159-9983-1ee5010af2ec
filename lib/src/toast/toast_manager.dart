import 'package:fluttertoast/fluttertoast.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/error/domain/alert_model.dart';
import 'package:smart_team_web/src/theme/colors.dart';

final toastManagerProvider =
    Provider.autoDispose<ToastManager>((_) => ToastManager());

class ToastManager {
  void showToast(String message) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.BOTTOM,
      timeInSecForIosWeb: 3,
      fontSize: 16,
      textColor: AColor.white,
      webBgColor: '#DC2525',
    );
  }

  void showToastErrorWithMessage(String message) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.TOP,
      timeInSecForIosWeb: 3,
      fontSize: 16,
      textColor: AColor.white,
      webBgColor: '#DC2525',
    );
  }

  void showToastError(AlertModel alert) {
    Fluttertoast.showToast(
      msg: alert.message,
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.TOP,
      timeInSecForIosWeb: 3,
      fontSize: 16,
      textColor: AColor.white,
      webBgColor: '#DC2525',
    );
  }
}

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';

final deviceActiveProvider = StateProvider<bool>((ref) => true);

final brandsProvider = Provider<List<String>>((ref) {
  return <String>[
    'Teltonika'.hardcoded,
    'Armoli'.hardcoded,
    'Kingwoiot'.hardcoded,
  ];
});

final modelsProvider = Provider<List<String>>((ref) {
  return <String>[
    'A'.hardcoded,
    'B'.hardcoded,
    'C'.hardcoded,
  ];
});

final simCardOperatorProvider = Provider<List<String>>((ref) {
  return <String>[
    'Vodafone'.hardcoded,
    'Turkcell'.hardcoded,
    'Türk Telekom'.hardcoded,
  ];
});

final connectedClientsProvider = Provider<List<String>>((ref) {
  return <String>[
    'A Müşterisi'.hardcoded,
    'B Müşterisi'.hardcoded,
    'C Müşterisi'.hardcoded,
  ];
});

final carBrandsProvider = Provider<List<String>>((ref) {
  return <String>[
    'Mercedes'.hardcoded,
    'BMW'.hardcoded,
    'Audi'.hardcoded,
  ];
});

final carModelsProvider = Provider<List<String>>((ref) {
  return <String>[
    'A'.hardcoded,
    'B'.hardcoded,
    'C'.hardcoded,
  ];
});

final onlineOfflineProvider = Provider<List<String>>((ref) {
  return <String>[
    'Online'.hardcoded,
    'Offline'.hardcoded,
  ];
});

import 'dart:math';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/home/<USER>/dashboard_notifier.dart';
import 'package:smart_team_web/src/home/<USER>/dashboard_constants.dart';
import 'package:smart_team_web/src/home/<USER>/mock_data.dart';
import 'package:smart_team_web/src/home/<USER>/components/all_team_list_card.dart';
import 'package:smart_team_web/src/home/<USER>/components/map_card.dart';
import 'package:smart_team_web/src/home/<USER>/components/panel_summary_card.dart';
import 'package:smart_team_web/src/home/<USER>/models/panel_summary_field.dart';
import 'package:smart_team_web/src/main/application/main_provider.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/constants/constants.dart';
import 'package:smart_team_web/src/shared/constants/team_list_tree_constants.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/app_overlay.dart';
import 'package:smart_team_web/src/widgets/employee_tree_view/employee_tree_view.dart';
import 'package:smart_team_web/src/widgets/hover_builder.dart';

part 'components/left_container/left_container.dart';
part 'components/left_container/three_dot_menu.dart';
part 'components/top_container/panel_summaries.dart';
part 'components/top_container/top_container.dart';
part 'components/top_container/instant_status_card.dart';
part 'components/top_container/jobs.dart';
part 'components/top_container/meetings.dart';

@RoutePage(name: 'DashboardRoute')
class DashboardScreenView extends HookConsumerWidget {
  const DashboardScreenView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final paddingH = context.responsive(desktop: 24, tablet: 16);
    const gap = 16.0;
    final isSmallScreen = context.width <= kMidScreenBreakpoint;

    final double rightW;

    if (isSmallScreen) {
      // Küçük ekranlarda tam genişlik kullan
      rightW = DashboardConstants.rightContainerMinWidth(context);
    } else {
      // Büyük ekranlarda orijinal hesaplama
      final leftW = DashboardConstants.leftContainerWidth(context);
      final spaceW = paddingH * 2 + gap;
      const extraRight = 60.0;
      final rawRight = context.width - (leftW + spaceW) + extraRight;
      rightW = max(
        rawRight,
        DashboardConstants.rightContainerMinWidth(context),
      );
    }

    return Scaffold(
      backgroundColor: AColor.backgroundColor,
      body: Padding(
        padding: EdgeInsets.all(paddingH as double),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            spacing: isSmallScreen ? 0 : gap,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!isSmallScreen) const LeftContainer(),
              SizedBox(
                width: rightW,
                child: SingleChildScrollView(
                  child: Column(
                    spacing: gap,
                    children: [
                      SizedBox(
                        height: context.height * 0.41,
                        child: const _TopContainer(),
                      ),
                      SizedBox(
                        height: context.height * 0.41,
                        child: const AllTeamListCard(),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

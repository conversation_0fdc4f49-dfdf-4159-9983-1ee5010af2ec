import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:smart_team_web/src/users/enum/work_type.dart';

part 'work_info_model.freezed.dart';
part 'work_info_model.g.dart';

@freezed
abstract class WorkInfoModel with _$WorkInfoModel {
  const factory WorkInfoModel({
    DateTime? joinDate,
    WorkType? workType,
    @Default(0) int usedAnnualLeave,
    @Default(0) int remainingAnnualLeave,
  }) = _WorkInfoModel;

  const WorkInfoModel._();

  factory WorkInfoModel.fromJson(Map<String, dynamic> json) =>
      _$WorkInfoModelFromJson(json);
}

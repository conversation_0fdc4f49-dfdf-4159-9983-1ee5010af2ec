// import 'package:flutter/material.dart';
// import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';

// class DashboardConstants {
//   const DashboardConstants._();
//   static const double rightContainerMinWidth = 1250;

//   static double leftContainerWidth(BuildContext context) {
//     return context.responsive(
//       desktop: 400,
//       tablet: 300,
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/constants.dart';

class DashboardConstants {
  const DashboardConstants._();

  /// Sol container'ın responsive genişliği (%15 – %25 arası, clamp'li)
  static double leftContainerWidth(BuildContext ctx) {
    final w = ctx.width;
    // masaüstünde %20, tablette %30
    final ratio = w > kBigScreenBreakpoint ? 0.20 : 0.30;
    return (w * ratio).clamp(ctx.responsive(desktop: 300, tablet: 200),
        ctx.responsive(desktop: 400, tablet: 300));
  }

  /// Sağ container'ın minimum genişliğini ekran genişliğine oranla belirler
  /// Küçük ekranlarda left container gizlendiği için tam genişlik kullanılır
  static double rightContainerMinWidth(BuildContext ctx) {
    final w = ctx.width;
    final isSmallScreen = w <= kMidScreenBreakpoint;

    if (isSmallScreen) {
      // Küçük ekranlarda padding'leri çıkararak tam genişliği kullan
      final paddingH = ctx.responsive(desktop: 24, tablet: 16);
      return w - (paddingH * 2);
    }

    // Büyük ekranlarda orijinal mantık
    final ratio = w > kBigScreenBreakpoint ? 0.65 : 0.55;
    return w * ratio;
  }
}

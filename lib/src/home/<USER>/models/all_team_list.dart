import 'package:freezed_annotation/freezed_annotation.dart';

part 'all_team_list.freezed.dart';
part 'all_team_list.g.dart';

@freezed
abstract class AllTeamList with _$AllTeamList {
  const factory AllTeamList({
    required int id,
    required String name,
    required bool status,
    required String teamGroup,
    required DateTime dataDateTime,
    required String stAddress,
    required double lat,
    required double lng,
    required double speed,
    required double totalDistance,
    required double totalMovement,
  }) = _AllTeamList;

  factory AllTeamList.fromJson(Map<String, dynamic> json) =>
      _$AllTeamListFromJson(json);
}

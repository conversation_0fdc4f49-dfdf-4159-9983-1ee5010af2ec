import 'package:freezed_annotation/freezed_annotation.dart';

part 'emergency_contact_model.freezed.dart';
part 'emergency_contact_model.g.dart';

@freezed
abstract class EmergencyContactModel with _$EmergencyContactModel {
  const factory EmergencyContactModel({
    required String name,
    required String phone,
    required String relation,
  }) = _EmergencyContactModel;

  factory EmergencyContactModel.fromJson(Map<String, dynamic> json) =>
      _$EmergencyContactModelFromJson(json);
}

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:smart_team_web/src/home/<USER>/personal_info_model.dart';
import 'package:smart_team_web/src/home/<USER>/work_info_model.dart';
import 'package:smart_team_web/src/personal_management/model/duty_salary_record.dart';
import 'package:smart_team_web/src/personal_management/model/education_record.dart';
import 'package:smart_team_web/src/personal_management/model/good_record.dart';
import 'package:smart_team_web/src/personal_management/model/permit_record.dart';

part 'team_member.freezed.dart';
part 'team_member.g.dart';

// @deprecated
@freezed
abstract class WebTeamMemberModel with _$WebTeamMemberModel {
  const factory WebTeamMemberModel({
    required String id,
    required String userName,
    required String teamId,
    required String emailAddress,
    required String department,
    required String position,
    required String phoneNumber,
    @Default(false) bool isActive,
    String? imageUrl,
    DateTime? joinedAt,
    PersonalInfoModel? personalInfo,
    WorkInfoModel? workInfo,
    @Default([]) List<EducationRecord> educationRecords,
    @Default([]) List<PermitRecord> permitRecords,
    @Default([]) List<DutySalary> dutySalaryRecords,
    @Default([]) List<GoodRecord> goodRecords,
    @Default(<String, String?>{}) Map<String, String?> documents,
  }) = _WebTeamMemberModel;

  const WebTeamMemberModel._();

  factory WebTeamMemberModel.fromJson(Map<String, dynamic> json) =>
      _$WebTeamMemberModelFromJson(json);

  @override
  String toString() => userName;
}

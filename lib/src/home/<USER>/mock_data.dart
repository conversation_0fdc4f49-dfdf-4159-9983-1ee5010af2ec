// TODO(Levent): Delete after backend is ready

import 'package:flutter/material.dart';
import 'package:smart_team_web/src/home/<USER>/team_list_model.dart';
import 'package:smart_team_web/src/home/<USER>/team_member.dart';
import 'package:smart_team_web/src/home/<USER>/models/all_team_list.dart';
import 'package:smart_team_web/src/home/<USER>/models/panel_summary_field.dart';
import 'package:smart_team_web/src/job_list/enum/job_priority.dart';
import 'package:smart_team_web/src/shared/enums/task_status.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:uuid/uuid.dart';

class DashboardMockData {
  static const uuid = Uuid();

  // static List<TeamListModel> getMockTeams() {
  //   return [
  //     TeamListModel(
  //       id: uuid.v4(),
  //       name: '<PERSON><PERSON><PERSON><PERSON>'.hardcoded,
  //       members: [
  //         TeamMemberModel(
  //           id: uuid.v4(),
  //           userName: 'A <PERSON>şisi',
  //           teamId: '1',
  //           emailAddress: '<EMAIL>',
  //           department: 'Yönetim',
  //           position: 'Üye',
  //           phoneNumber: '555-1111111',
  //           isActive: true,
  //           joinedAt: DateTime.now().subtract(const Duration(days: 100)),
  //         ),
  //         TeamMemberModel(
  //           id: uuid.v4(),
  //           userName: 'B Kişisi',
  //           teamId: '1',
  //           emailAddress: '<EMAIL>',
  //           department: 'Yönetim',
  //           position: 'Üye',
  //           phoneNumber: '555-3333333',
  //           isActive: true,
  //           joinedAt: DateTime.now().subtract(const Duration(days: 200)),
  //         ),
  //       ],
  //     ),
  //     TeamListModel(
  //       id: uuid.v4(),
  //       name: 'Yazılım'.hardcoded,
  //       members: [
  //         TeamMemberModel(
  //           id: uuid.v4(),
  //           userName: 'C Kişisi',
  //           teamId: '2',
  //           emailAddress: '<EMAIL>',
  //           department: 'Yazılım',
  //           position: 'Üye',
  //           phoneNumber: '555-2222222',
  //           isActive: true,
  //           joinedAt: DateTime.now().subtract(const Duration(days: 150)),
  //         ),
  //         TeamMemberModel(
  //           id: uuid.v4(),
  //           userName: 'D Kişisi',
  //           teamId: '2',
  //           emailAddress: '<EMAIL>',
  //           department: 'Yazılım',
  //           position: 'Üye',
  //           phoneNumber: '555-2222222',
  //           isActive: true,
  //           joinedAt: DateTime.now().subtract(const Duration(days: 180)),
  //         ),
  //       ],
  //       subTeams: [
  //         TeamListModel(
  //           id: uuid.v4(),
  //           name: 'Frontend'.hardcoded,
  //           members: [
  //             TeamMemberModel(
  //               id: uuid.v4(),
  //               userName: 'E Kişisi',
  //               teamId: '3',
  //               emailAddress: '<EMAIL>',
  //               department: 'Yazılım / Frontend',
  //               position: 'Geliştirici',
  //               phoneNumber: '555-4444444',
  //               isActive: true,
  //               joinedAt: DateTime.now().subtract(const Duration(days: 90)),
  //             ),
  //           ],
  //         ),
  //         TeamListModel(
  //           id: uuid.v4(),
  //           name: 'Backend'.hardcoded,
  //           members: [
  //             TeamMemberModel(
  //               id: uuid.v4(),
  //               userName: 'F Kişisi',
  //               teamId: '4',
  //               emailAddress: '<EMAIL>',
  //               department: 'Yazılım / Backend',
  //               position: 'Geliştirici',
  //               phoneNumber: '555-3333333',
  //               joinedAt: DateTime.now().subtract(const Duration(days: 300)),
  //             ),
  //           ],
  //         ),
  //       ],
  //     ),
  //   ];
  // }

  static List<AllTeamList> getMockAllTeamList() {
    return [
      AllTeamList(
        id: 1,
        name: 'Ali',
        status: true,
        teamGroup: 'Group 1',
        dataDateTime: DateTime.now(),
        stAddress:
            'İstanbul lat: 41.015137, lat: 41.015137, İstanbul İstanbul İstanbul',
        speed: 12.5,
        totalDistance: 36.71,
        totalMovement: 2.44,
        lat: 41.015137,
        lng: 28.979530,
      ),
      AllTeamList(
        id: 2,
        name: 'Ayşe',
        status: false,
        teamGroup: 'Group 2',
        dataDateTime: DateTime.now(),
        stAddress: 'Ankara',
        speed: 0,
        totalDistance: 12.3,
        totalMovement: 0,
        lat: 39.9208,
        lng: 32.8541,
      ),
      AllTeamList(
        id: 3,
        name: 'Mehmet',
        status: true,
        teamGroup: 'Group 3',
        dataDateTime: DateTime.now(),
        stAddress: 'İzmir',
        speed: 5.2,
        totalDistance: 18.0,
        totalMovement: 1.2,
        lat: 38.4192,
        lng: 27.1287,
      ),
      AllTeamList(
        id: 4,
        name: 'Elif',
        status: false,
        teamGroup: 'Group 1',
        dataDateTime: DateTime.now(),
        stAddress: 'Bursa',
        speed: 0,
        totalDistance: 9.6,
        totalMovement: 0.3,
        lat: 40.1828,
        lng: 29.0663,
      ),
    ];
  }

  static List<PanelSummaryField> getPanelSummaryFields(BuildContext context) {
    return [
      PanelSummaryField(
        title: context.tr.dailyMovementDistanceInKm,
        icon: Icons.directions_walk_rounded,
        value: '2,44'.hardcoded,
      ),
      PanelSummaryField(
        title: context.tr.dailyDrivingDistanceInKm,
        icon: Icons.route_rounded,
        value: '36,71'.hardcoded,
      ),
      PanelSummaryField(
        title: context.tr.maxSpeedInKm,
        icon: Icons.speed_rounded,
        value: '112,68'.hardcoded,
      ),
      PanelSummaryField(
        title: context.tr.numberDailyAct,
        icon: Icons.calendar_month_rounded,
        value: '0'.hardcoded,
      ),
      PanelSummaryField(
        title: context.tr.totalViolation,
        icon: Icons.warning_amber_rounded,
        value: '6'.hardcoded,
      ),
      PanelSummaryField(
        title: context.tr.numberDevicesNoData,
        icon: Icons.wifi,
        value: '14'.hardcoded,
      ),
    ];
  }

  static List<WebTeamMemberModel> getMembers() {
    return [
      WebTeamMemberModel(
        id: '1',
        userName: 'Ali Derici',
        teamId: '1',
        emailAddress: '<EMAIL>',
        department: 'Satış Departmanı',
        position: 'Satış Temsilcisi',
        phoneNumber: '5059238130',
        isActive: true,
        joinedAt: DateTime(2023, 01, 15),
      ),
      WebTeamMemberModel(
        id: '2',
        userName: 'Ayşe',
        teamId: '1',
        emailAddress: '<EMAIL>',
        department: 'Yönetim',
        position: 'Üye',
        phoneNumber: '5324567890',
        isActive: true,
        joinedAt: DateTime(2023, 03, 10),
      ),
      WebTeamMemberModel(
        id: '3',
        userName: 'Mehmet',
        teamId: '1',
        emailAddress: '<EMAIL>',
        department: 'Yönetim',
        position: 'Üye',
        phoneNumber: '5421234567',
        isActive: true,
        joinedAt: DateTime(2023, 05, 05),
      ),
      WebTeamMemberModel(
        id: '4',
        userName: 'Elif',
        teamId: '2',
        emailAddress: '<EMAIL>',
        department: 'Yazılım',
        position: 'Geliştirici',
        phoneNumber: '5367891234',
        isActive: true,
        joinedAt: DateTime(2022, 11, 20),
      ),
      WebTeamMemberModel(
        id: '5',
        userName: 'Fatma',
        teamId: '2',
        emailAddress: '<EMAIL>',
        department: 'Yazılım',
        position: 'Geliştirici',
        phoneNumber: '5324567890',
        isActive: true,
        joinedAt: DateTime(2022, 11, 20),
      ),
      WebTeamMemberModel(
        id: '6',
        userName: 'Mehmet',
        teamId: '2',
        emailAddress: '<EMAIL>',
        department: 'Yazılım',
        position: 'Geliştirici',
        phoneNumber: '5324567890',
        isActive: true,
        joinedAt: DateTime(2022, 11, 20),
      ),
      WebTeamMemberModel(
        id: '7',
        userName: 'Mehmet',
        teamId: '2',
        emailAddress: '<EMAIL>',
        department: 'Yazılım',
        position: 'Geliştirici',
        phoneNumber: '5324567890',
        isActive: true,
        joinedAt: DateTime(2022, 11, 20),
      ),
      WebTeamMemberModel(
        id: '8',
        userName: 'Mehmet',
        teamId: '2',
        emailAddress: '<EMAIL>',
        department: 'Yazılım',
        position: 'Geliştirici',
        phoneNumber: '5324567890',
        isActive: true,
        joinedAt: DateTime(2022, 11, 20),
      ),
      WebTeamMemberModel(
        id: '9',
        userName: 'Mehmet',
        teamId: '2',
        emailAddress: '<EMAIL>',
        department: 'Yazılım',
        position: 'Geliştirici',
        phoneNumber: '5324567890',
        isActive: true,
        joinedAt: DateTime(2022, 11, 20),
      ),
      WebTeamMemberModel(
        id: '10',
        userName: 'Mehmet',
        teamId: '2',
        emailAddress: '<EMAIL>',
        department: 'Yazılım',
        position: 'Geliştirici',
        phoneNumber: '5324567890',
        isActive: true,
        joinedAt: DateTime(2022, 11, 20),
      ),
      WebTeamMemberModel(
        id: '11',
        userName: 'Mehmet',
        teamId: '2',
        emailAddress: '<EMAIL>',
        department: 'Yazılım',
        position: 'Geliştirici',
        phoneNumber: '5324567890',
        isActive: true,
        joinedAt: DateTime(2022, 11, 20),
      ),
      WebTeamMemberModel(
        id: '12',
        userName: 'Mehmet',
        teamId: '2',
        emailAddress: '<EMAIL>',
        department: 'Yazılım',
        position: 'Geliştirici',
        phoneNumber: '5324567890',
        isActive: true,
        joinedAt: DateTime(2022, 11, 20),
      ),
    ];
  }

  static final WebTeamMemberModel mockMember = WebTeamMemberModel(
    id: '1',
    userName: 'Ali Yıldız',
    teamId: 'team01',
    emailAddress: '<EMAIL>',
    department: 'Satış',
    position: 'Satış Temsilcisi',
    phoneNumber: '+90 ************',
    isActive: true,
    joinedAt: DateTime.now(),
    documents: {
      'idCard': 'https://via.placeholder.com/300x400.png?text=Kimlik+Kartı',
      'residence':
          'https://docs.google.com/document/d/1ySI7fhHgusRQtP0Vv7PQNFq2S4TqjXcG/edit?usp=sharing&ouid=107747436243383320036&rtpof=true&sd=true',
      'record':
          'https://via.placeholder.com/300x400.png?text=Nüfus+Kayıt+%C3%96rne%C4%9Fi',
      'criminal':
          'https://via.placeholder.com/300x400.png?text=Adli+Sicil+Kayd%C4%B1',
      'graduation':
          'https://via.placeholder.com/300x400.png?text=Mezuniyet+Belgesi',
      'familyStatus': null,
      'military':
          'https://via.placeholder.com/300x400.png?text=Askerlik+Belgesi',
      'bankInfo': 'https://via.placeholder.com/300x400.png?text=Banka+Bilgisi',
      'iscuk': null,
    },
  );
}

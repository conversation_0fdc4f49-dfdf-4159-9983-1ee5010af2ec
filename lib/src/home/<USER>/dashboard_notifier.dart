import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/home/<USER>/dashboard_state.dart';
import 'package:smart_team_web/src/home/<USER>/team_member.dart';

final dashboardNotifierProvider =
    AutoDisposeNotifierProvider<DashboardNotifier, DashboardState>(
  DashboardNotifier.new,
);

class DashboardNotifier extends AutoDisposeNotifier<DashboardState> {
  @override
  DashboardState build() {
    return const DashboardState();
  }

  void setAllMembersSelected() {
    state = state.copyWith(allMembersSelected: true, selectedMember: null);
  }

  void setSelectedMember(WebTeamMemberModel member) {
    state = state.copyWith(allMembersSelected: false, selectedMember: member);
  }

  void setMemberSearchText(String text) {
    state = state.copyWith(memberSearchText: text);
  }

  void setExpandAllMembers({required bool expandMembers}) {
    state = state.copyWith(expandAllMembers: expandMembers);
  }

  void setFilterByName({required bool filterByName}) {
    state = state.copyWith(filterByName: filterByName);
  }
}

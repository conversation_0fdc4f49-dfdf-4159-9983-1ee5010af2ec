import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/main/application/main_provider.dart';

final employeesFutureProvider =
    FutureProvider<List<TeamsWithMembersModel>>((ref) async {
  final company = ref.watch(selectedCompanyProvider).asData?.value;
  if (company == null || company.id == null) return [];

  return ref.read(teamRepositoryProvider).fetchTeamsWithMembers(company.id!);
});

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:smart_team_web/src/home/<USER>/team_member.dart';

part 'dashboard_state.freezed.dart';

@freezed
abstract class DashboardState with _$DashboardState {
  const factory DashboardState({
    @Default(true) bool allMembersSelected,
    WebTeamMemberModel? selectedMember,
    @Default('') String memberSearchText,
    @Default(true) bool filterByName,
    @Default(true) bool expandAllMembers,
  }) = _DashboardState;
}

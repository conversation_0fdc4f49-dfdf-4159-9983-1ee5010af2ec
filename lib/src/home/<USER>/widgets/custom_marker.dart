/* part of '../components/map_card.dart';

class CustomInfoWindow extends StatelessWidget {
  const CustomInfoWindow({
    required this.userName,
    required this.dateTime,
    required this.speed,
    required this.address,
    required this.state,
    super.key,
  });

  final String userName;
  final String dateTime;
  final double speed;
  final String address;
  final String state;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: getStateColor(state).withAlpha(100),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            userName,
            textAlign: TextAlign.right,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            dateTime,
            textAlign: TextAlign.right,
            style: const TextStyle(fontSize: 12, color: Colors.white70),
          ),
          Text(
            '${speed.toStringAsFixed(1)} ${context.tr.kmh}',
            textAlign: TextAlign.right,
            style: const TextStyle(fontSize: 12, color: Colors.white70),
          ),
          Text(
            address,
            textAlign: TextAlign.right,
            style: const TextStyle(fontSize: 12, color: Colors.white70),
          ),
        ],
      ),
    );
  }

  Color getStateColor(String state) {
    switch (state) {
      case 'sürüş':
        return Colors.green;
      case 'durma':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}

Future<Uint8List> widgetToImageBytes(
  Widget widget, {
  required int width,
  required int height,
}) async {
  final repaintBoundary = RenderRepaintBoundary();

  final view = WidgetsBinding.instance.platformDispatcher.views.first;

  final renderView = RenderView(
    view: view,
    configuration: ViewConfiguration(
      physicalConstraints:
          BoxConstraints.tight(Size(width.toDouble(), height.toDouble())),
      logicalConstraints:
          BoxConstraints.tight(Size(width.toDouble(), height.toDouble())),
      devicePixelRatio: view.devicePixelRatio,
    ),
    child: RenderPositionedBox(
      child: repaintBoundary,
    ),
  );

  final pipelineOwner = PipelineOwner()..rootNode = renderView;
  renderView.prepareInitialFrame();

  final buildOwner = BuildOwner();
  final rootElement = RenderObjectToWidgetAdapter<RenderBox>(
    container: repaintBoundary,
    child: widget,
  ).attachToRenderTree(buildOwner);

  buildOwner
    ..buildScope(rootElement)
    ..finalizeTree();

  pipelineOwner
    ..flushLayout()
    ..flushCompositingBits()
    ..flushPaint();

  final image =
      await repaintBoundary.toImage(pixelRatio: ui.window.devicePixelRatio);
  final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
  return byteData!.buffer.asUint8List();
}
 */

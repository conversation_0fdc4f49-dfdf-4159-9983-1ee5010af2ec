import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

class HoverColoredIcon<T> extends HookConsumerWidget {
  const HoverColoredIcon({
    required this.icon,
    super.key,
    this.options,
    this.onSelected,
    this.displayOption,
    this.defaultValue,
    this.size,
    this.text,
    this.color,
  });

  final IconData icon;
  final List<T>? options;
  final void Function(T?)? onSelected;
  final String Function(T)? displayOption;
  final T? defaultValue;
  final double? size;
  final String? text;
  final Color? color;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isHovering = useState(false);
    final selectedValue = useState<T?>(defaultValue);

    return MouseRegion(
      onEnter: (_) => isHovering.value = true,
      onExit: (_) => isHovering.value = false,
      child: options != null && options!.isNotEmpty
          ? PopupMenuButton<T>(
              icon: Icon(
                icon,
                size: size,
                color: isHovering.value ? AColor.primaryColor : AColor.grey,
              ),
              onSelected: (value) {
                selectedValue.value = value;
                onSelected?.call(value);
              },
              itemBuilder: (context) {
                return options!
                    .map(
                      (option) => PopupMenuItem<T>(
                        value: option,
                        child: Row(
                          children: [
                            Icon(
                              selectedValue.value == option
                                  ? Icons.radio_button_checked
                                  : Icons.radio_button_off,
                              color: selectedValue.value == option
                                  ? AColor.primaryColor
                                  : color ?? AColor.grey,
                            ),
                            TextWidget(
                              displayOption != null
                                  ? displayOption!(option)
                                  : option.toString(),
                            ),
                          ],
                        ),
                      ),
                    )
                    .toList();
              },
            )
          : InkWell(
              onTap: () {
                onSelected?.call(defaultValue);
              },
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    icon,
                    size: size,
                    color: isHovering.value
                        ? AColor.primaryColor
                        : color ?? AColor.grey,
                  ),
                  if (text != null && text!.isNotEmpty)
                    TextWidget(
                      text!,
                      style: ATextStyle.small.copyWith(
                        fontSize: 10,
                        color: isHovering.value
                            ? AColor.primaryColor
                            : color ?? AColor.grey,
                      ),
                    ),
                ],
              ),
            ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:smart_team_web/src/home/<USER>/components/panel_summary_card.dart';
import 'package:smart_team_web/src/home/<USER>/models/panel_summary_field.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';

class PanelSummaryRow extends StatelessWidget {
  const PanelSummaryRow({
    required this.items,
    super.key,
    this.crossAxiscount = 1,
  });

  final List<PanelSummaryField> items;
  final int crossAxiscount;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: context.height * 0.4,
      child: Column(
        spacing: 16,
        children: items
            .map(
              (field) => Expanded(
                child: PanelSummaryCard(field: field),
              ),
            )
            .toList(),
      ),
    );
  }
}

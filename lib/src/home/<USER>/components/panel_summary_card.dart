import 'package:flutter/material.dart';
import 'package:smart_team_web/src/home/<USER>/models/panel_summary_field.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/popup/feature_dialog.dart';

class PanelSummaryCard extends StatelessWidget {
  const PanelSummaryCard({
    required this.field,
    super.key,
  });

  final PanelSummaryField field;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => _showFeatureDialog(context, field),
      child: Container(
        padding: const EdgeInsets.all(10),
        decoration: CommonDecorations.containerDecoration(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          spacing: 2,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(
                  field.icon,
                  size: context.responsive<double>(
                    desktop: 15,
                    tablet: 10,
                  ),
                  color: AColor.primaryColor,
                ),
                InkWell(
                  onTap: () => _showFeatureDialog(context, field),
                  child: Icon(
                    Icons.more_vert_outlined,
                    size: context.responsive<double>(
                      desktop: 10,
                      tablet: 10,
                    ),
                    color: AColor.black,
                  ),
                ),
              ],
            ),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    field.value ?? '0'.hardcoded,
                    style: context.responsive<TextStyle>(
                      desktop: ATextStyle.text18SemiBold,
                      tablet: ATextStyle.text16SemiBold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  Text(
                    field.title,
                    style: context.responsive<TextStyle>(
                      desktop: ATextStyle.text12,
                      tablet: ATextStyle.small,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showFeatureDialog(BuildContext context, PanelSummaryField field) {
    final columnsByFeature = <String, List<String>>{
      context.tr.dailyMovementDistanceInKm: [
        context.tr.gsm,
        context.tr.deviceName,
        context.tr.distance,
        context.tr.duration,
      ],
      context.tr.dailyDrivingDistanceInKm: [
        context.tr.gsm,
        context.tr.deviceName,
        context.tr.distance,
        context.tr.duration,
      ],
      context.tr.maxSpeedInKm: [
        context.tr.gsm,
        context.tr.deviceName,
        context.tr.speed,
        context.tr.address,
      ],
      context.tr.numberDailyAct: [
        context.tr.gsm,
        context.tr.actType,
        context.tr.eventType,
        context.tr.contentType,
        context.tr.status,
        context.tr.conclusion,
        context.tr.content,
        context.tr.title,
        context.tr.company,
        context.tr.province,
        context.tr.district,
        context.tr.contact,
        context.tr.beginning,
        context.tr.finish,
      ],
      context.tr.totalViolation: [
        context.tr.gsm,
        context.tr.deviceName,
        context.tr.type,
        context.tr.date,
        context.tr.address,
      ],
      context.tr.numberDevicesNoData: [
        context.tr.gsm,
        context.tr.deviceName,
        context.tr.time,
        context.tr.reason,
        context.tr.gps,
        context.tr.inAppLocationSharing,
      ],
    };

    final data = _fetchFeatureData(field.title);
    final columns = columnsByFeature[field.title] ?? ['Column 1', 'Column 2'];

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return FeatureDialog(
          title: field.title,
          columns: columns,
          data: data,
        );
      },
    );
  }

  List<List<String>> _fetchFeatureData(String feature) {
    return [];
  }
}

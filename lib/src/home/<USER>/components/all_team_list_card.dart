import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/home/<USER>/employees_provider.dart';
import 'package:smart_team_web/src/home/<USER>/widgets/hover_colored_icon.dart';
import 'package:smart_team_web/src/main/application/main_provider.dart';
import 'package:smart_team_web/src/map/application/map_provider.dart';
import 'package:smart_team_web/src/map/domain/team_member_location_model.dart';
import 'package:smart_team_web/src/map/presentation/dynamic_map_view.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/utils/dt_format.dart';
import 'package:smart_team_web/src/shared/utils/dt_util.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_column_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_row_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/st_data_table.dart';

class AllTeamListCard extends ConsumerWidget {
  const AllTeamListCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabsRouter = AutoTabsRouter.of(context);

    final selectedMembers = ref.watch(selectedMembersProvider);
    final allTeamList = ref.watch(allTeamProvider);

    final filteredTeamList = selectedMembers.isEmpty
        ? allTeamList
        : allTeamList.where((teamMember) {
            return selectedMembers.any(
              (selected) => selected.userName == teamMember.name,
            );
          }).toList();

    final rowData = filteredTeamList.map((teamMember) {
      return [
        RowDataModel<bool?>(
          columnName: context.tr.status,
          value: teamMember.status,
          cellBuilder: () => Image.asset(
            package: 'smart_team_common',
            teamMember.status
                ? SmartTeamAssets.images.walking.path
                : SmartTeamAssets.images.stopping.path,
            width: 24,
            height: 24,
          ),
        ),
        RowDataModel<String>(
          columnName: context.tr.teamGroup,
          value: teamMember.teamGroup,
        ),
        RowDataModel<String>(
          columnName: context.tr.person,
          value: teamMember.name,
        ),
        RowDataModel<DateTime>(
          columnName: context.tr.dataTime,
          value: teamMember.dataDateTime,
          cellBuilder: () => Text(
            DTUtil.dtToString(teamMember.dataDateTime, format: DTFormat.human1),
            style: ATextStyle.semiSmallRegular,
          ),
        ),
        RowDataModel<String>(
          columnName: context.tr.headAddress,
          value: teamMember.stAddress,
        ),
        RowDataModel<double>(
          columnName: context.tr.speed,
          value: teamMember.speed,
        ),
        RowDataModel<double>(
          columnName: context.tr.drivingTot,
          value: teamMember.totalDistance,
        ),
        RowDataModel<double>(
          columnName: context.tr.movingTot,
          value: teamMember.totalMovement,
        ),
        RowDataModel(
          columnName: context.tr.animation,
          cellBuilder: () => HoverColoredIcon(
            icon: Icons.smart_display_rounded,
            onSelected: (_) {
              final locationModel = TeamMemberLocationModel(
                id: teamMember.id.toString(),
                userName: teamMember.name,
                lat: 41.015137,
                lng: 28.979530,
                address: teamMember.stAddress,
                updatedAt: teamMember.dataDateTime,
              );

              ref.read(teamMemberLocationProvider.notifier).state =
                  locationModel;

              showDialog(
                context: context,
                builder: (_) => const MapTimeRangeSelectionDialog(),
              ).then((_) {
                tabsRouter.setActiveIndex(7);
              });
            },
          ),
        ),
      ];
    }).toList();

    return Container(
      decoration: CommonDecorations.containerDecoration(
        borderColor: AColor.black.withValues(alpha: .08),
      ),
      child: SmartTeamDataTable(
        columns: [
          TableColumnModel(
            columnName: context.tr.status,
            width: 60,
            ignoreWhenExporting: true,
          ),
          TableColumnModel(
            columnName: context.tr.teamGroup,
            width: 80,
          ),
          TableColumnModel(
            columnName: context.tr.person,
            width: 100,
          ),
          TableColumnModel(
            columnName: context.tr.dataTime,
            width: 100,
          ),
          TableColumnModel(columnName: context.tr.headAddress),
          TableColumnModel(
            columnName: context.tr.speed,
            width: 70,
          ),
          TableColumnModel(
            columnName: context.tr.drivingTot,
            width: 70,
          ),
          TableColumnModel(
            columnName: context.tr.movingTot,
            width: 70,
          ),
          TableColumnModel(
            columnName: context.tr.animation,
            width: 60,
            ignoreWhenExporting: true,
            filterable: false,
            sortable: false,
          ),
        ],
        rowData: rowData,
        onRowClick: (index) {},
      ),
    );
  }
}

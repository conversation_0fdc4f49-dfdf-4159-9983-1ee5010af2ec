import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/calendar/application/calendar_provider.dart';
import 'package:smart_team_web/src/home/<USER>/dashboard_provider.dart';
import 'package:smart_team_web/src/home/<USER>/widgets/hover_colored_icon.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/shared/utils/dt_format.dart';
import 'package:smart_team_web/src/shared/utils/dt_util.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/app_dialog/app_dialog.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_column_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_row_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/st_data_table.dart';
import 'package:smart_team_web/src/job_list/presentation/dialogs/add_new_job_widgets.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

class TodoListCard extends HookConsumerWidget {
  const TodoListCard({
    super.key,
    this.height,
    this.isJobListView = false,
  });
  final double? height;
  final bool isJobListView;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tasks = ref.watch(filteredTasksProvider);

    late final List<TableColumnModel> columns;
    late final List<List<RowDataModel<dynamic>>> rowData;

    columns = [
      TableColumnModel(columnName: context.tr.gsm),
      TableColumnModel(columnName: context.tr.name),
      TableColumnModel(columnName: '${context.tr.team}/${context.tr.group}'),
      TableColumnModel(columnName: context.tr.priority),
      TableColumnModel(columnName: context.tr.status),
      TableColumnModel(columnName: context.tr.title),
      TableColumnModel(columnName: context.tr.description),
      TableColumnModel(columnName: context.tr.beginning),
      TableColumnModel(columnName: context.tr.finish),
      const TableColumnModel(columnName: ''),
      const TableColumnModel(columnName: ''),
    ];

    rowData = tasks.asMap().entries.map((entry) {
      final index = entry.key;
      final task = entry.value;
      return [
        RowDataModel<String>(
          columnName: context.tr.gsm,
          value: task.createdByUser.phone ?? '',
          cellBuilder: () => TextWidget(task.createdByUser.phone ?? ''),
        ),
        RowDataModel<String>(
          columnName: context.tr.name,
          value: task.assignedToUser.name,
          cellBuilder: () => TextWidget(task.assignedToUser.name),
        ),
        RowDataModel<String>(
          columnName: '${context.tr.team}/${context.tr.group}',
          value: '--/--',
          cellBuilder: () => const TextWidget('--/--'),
        ),
        RowDataModel<String>(
          columnName: context.tr.priority,
          value: task.priority.name,
          cellBuilder: () => TextWidget(task.priority.name),
        ),
        RowDataModel<String>(
          columnName: context.tr.status,
          value: _statusToString(context, task.status),
          cellBuilder: () => TextWidget(_statusToString(context, task.status)),
        ),
        RowDataModel<String>(
          columnName: context.tr.title,
          value: task.title,
          cellBuilder: () => TextWidget(task.title),
        ),
        RowDataModel<String>(
          columnName: context.tr.description,
          value: task.description ?? task.title,
          cellBuilder: () => TextWidget(task.description ?? task.title),
        ),
        RowDataModel<String>(
          columnName: context.tr.beginning,
          value: task.createdAt != null
              ? DTUtil.dtToString(task.createdAt!, format: DTFormat.human1)
              : '',
          cellBuilder: () => TextWidget(
            task.createdAt != null
                ? DTUtil.dtToString(task.createdAt!, format: DTFormat.human1)
                : '',
          ),
        ),
        RowDataModel<String>(
          columnName: context.tr.finish,
          value: task.completedAt != null
              ? DTUtil.dtToString(task.completedAt!, format: DTFormat.human1)
              : '',
          cellBuilder: () => TextWidget(
            task.completedAt != null
                ? DTUtil.dtToString(task.completedAt!, format: DTFormat.human1)
                : '',
          ),
        ),
        RowDataModel<Widget>(
          columnName: '',
          cellBuilder: () => IconButton(
            icon: SmartTeamAssets.icons.edit.svg(),
            onPressed: () {
              AppDialog.show<void>(
                context: context,
                title: 'İşi Düzenle'.hardcoded,
                width: context.width * .95,
                height: context.height * .95,
                child: const AddNewJobWidgets(),
              );
            },
          ),
        ),
        RowDataModel<Widget>(
          columnName: '',
          cellBuilder: () => IconButton(
            icon: const Icon(
              Icons.delete,
              color: AColor.textColor,
            ),
            onPressed: () {
              ref.read(taskProvider.notifier).deleteTaskAt(index);
            },
          ),
        ),
      ];
    }).toList();

    return Container(
      height: height ?? context.height * 0.45,
      padding: const EdgeInsets.all(16),
      decoration: CommonDecorations.containerDecoration(),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: TextWidget(
                    isJobListView ? context.tr.jobList : 'Todo List'.hardcoded,
                    style: ATextStyle.medium.copyWith(color: AColor.black),
                  ),
                ),
                const Spacer(),
                Expanded(
                  flex: 5,
                  child: CustomTextFormField(
                    hintText: context.tr.searchNameTask,
                    onChanged: (value) {
                      ref.read(queryProvider.notifier).state = value;
                    },
                    suffixIcon: HoverColoredIcon(
                      icon: Icons.search,
                      onSelected: (_) {
                        ref.refresh(queryProvider.notifier);
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: SmartTeamDataTable(
              columns: columns,
              rowData: rowData,
            ),
          ),
        ],
      ),
    );
  }

  String _statusToString(BuildContext context, TaskStatusEnum status) {
    switch (status) {
      case TaskStatusEnum.pending:
        return 'context.tr.pending';
      case TaskStatusEnum.accepted:
        return context.tr.accepted;
      case TaskStatusEnum.rejected:
        return context.tr.declined;
      case TaskStatusEnum.inProgress:
        return 'context.tr.inProgress';
      case TaskStatusEnum.waitingForInfo:
        return 'context.tr.waitingForInfo';
      case TaskStatusEnum.completed:
        return context.tr.done;
      case TaskStatusEnum.cancelled:
        return 'context.tr.cancelled';
      case TaskStatusEnum.overdue:
        return 'context.tr.overdue';
    }
  }
}

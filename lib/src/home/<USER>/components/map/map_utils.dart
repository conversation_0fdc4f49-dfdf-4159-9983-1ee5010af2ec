part of '../map_card.dart';

Future<Uint8List> widgetToImageBytes(
  Widget widget, {
  required int width,
  required int height,
}) async {
  try {
    final repaintBoundary = RenderRepaintBoundary();
    final view = WidgetsBinding.instance.platformDispatcher.views.first;

    final pipelineOwner = PipelineOwner();
    final renderView = RenderView(
      view: view,
      configuration: ViewConfiguration(
        physicalConstraints:
            BoxConstraints.tight(Size(width.toDouble(), height.toDouble())),
        logicalConstraints:
            BoxConstraints.tight(Size(width.toDouble(), height.toDouble())),
        devicePixelRatio: view.devicePixelRatio,
      ),
      child: RenderPositionedBox(
        child: repaintBoundary,
      ),
    );

    pipelineOwner.rootNode = renderView;
    renderView.prepareInitialFrame();

    final buildOwner = BuildOwner(focusManager: FocusManager());
    final rootElement = RenderObjectToWidgetAdapter<RenderBox>(
      container: repaintBoundary,
      child: Directionality(
        textDirection: TextDirection.ltr,
        child: widget,
      ),
    ).attachToRenderTree(buildOwner);

    buildOwner
      ..buildScope(rootElement)
      ..finalizeTree();

    pipelineOwner
      ..flushLayout()
      ..flushCompositingBits()
      ..flushPaint();

    await Future.microtask(() {});

    final image =
        await repaintBoundary.toImage(pixelRatio: view.devicePixelRatio);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    renderView.detach();
    pipelineOwner.dispose();

    return byteData!.buffer.asUint8List();
  } catch (e) {
    return Uint8List(0);
  }
}

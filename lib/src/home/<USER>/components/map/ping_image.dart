part of '../map_card.dart';

class PingImage extends StatefulWidget {
  const PingImage({
    required this.imgPath,
    this.size = 40,
    super.key,
  });

  final String imgPath;
  final double size;

  @override
  PingImageState createState() => PingImageState();
}

class PingImageState extends State<PingImage>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<int> _alphaAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _scaleAnimation = Tween<double>(begin: 1, end: 2.5).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    );

    _alphaAnimation = IntTween(begin: 128, end: 0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size * 2.5,
      height: widget.size * 2.5,
      child: Stack(
        alignment: Alignment.center,
        children: [
          AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Container(
                width: widget.size * _scaleAnimation.value,
                height: widget.size * _scaleAnimation.value,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AColor.dustyGray.withAlpha(_alphaAnimation.value),
                ),
              );
            },
          ),
          AImage(
            imgPath: widget.imgPath,
            width: widget.size,
            height: widget.size,
          ),
        ],
      ),
    );
  }
}

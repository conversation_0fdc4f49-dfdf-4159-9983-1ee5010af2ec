part of '../../dashboard_screen_view.dart';

class _TopContainer extends ConsumerWidget {
  const _TopContainer();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final fields = DashboardMockData.getPanelSummaryFields(context);
    final leftCards = fields.take(3).toList();
    final rightCards = fields.skip(3).take(3).toList();

    final selectedMembers = ref.watch(selectedMembersProvider);
    final allTeamList = ref.watch(allTeamProvider);

    final selectedNames = selectedMembers
        .map((e) => e.userName?.trim())
        .where((name) => name != null && name.isNotEmpty)
        .cast<String>()
        .toSet();
    final filteredLocations = selectedNames.isEmpty
        ? allTeamList
        : allTeamList.where((u) => selectedNames.contains(u.name)).toList();

    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 12,
        children: [
          Expanded(
            child: _PanelSummaries(fields: leftCards),
          ),
          Expanded(
            flex: context.responsive(desktop: 5, tablet: 4),
            child: MapCard(locations: filteredLocations),
          ),
          Expanded(
            child: _PanelSummaries(fields: rightCards),
          ),
          const Expanded(
            flex: 3,
            child: Column(
              spacing: 12,
              children: [
                Expanded(child: _InstantStatusCard()),
                Expanded(child: _Jobs()),
                Expanded(child: _Meetings()),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

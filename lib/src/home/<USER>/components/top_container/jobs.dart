part of '../../dashboard_screen_view.dart';

class _Jobs extends StatelessWidget {
  const _Jobs();

  @override
  Widget build(BuildContext context) {
    Widget buildJobContainer({
      required String title,
      required List<String> jobs,
    }) {
      final extraJobs = jobs.length - 2;
      final jobItems = jobs.take(2).toList();

      final tabsRouter = AutoTabsRouter.of(context);
      return Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          HoverBuilder(
            onTap: () {
              tabsRouter.setActiveIndex(3);
            },
            builder: (isHovering, isPressed) {
              return Container(
                width: double.infinity,
                height: context.responsive<double>(
                  desktop: 30,
                  tablet: 25,
                ),
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: const BoxDecoration(
                  color: AColor.backgroundColor,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      title,
                      style: context.responsive<TextStyle>(
                        desktop: ATextStyle.text14.copyWith(
                          color: isHovering ? AColor.primaryColor : null,
                        ),
                        tablet: ATextStyle.text12.copyWith(
                          color: isHovering ? AColor.primaryColor : null,
                        ),
                      ),
                    ),
                    Text(
                      jobs.length.toString(),
                      style: context.responsive<TextStyle>(
                        desktop: ATextStyle.text16.copyWith(
                          color: isHovering ? AColor.primaryColor : null,
                        ),
                        tablet: ATextStyle.text14.copyWith(
                          color: isHovering ? AColor.primaryColor : null,
                        ),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              );
            },
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: jobItems
                  .map(
                    (e) => Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        spacing: 8,
                        children: [
                          Expanded(
                            child: Text(
                              ' • $e',
                              style: ATextStyle.text11,
                              maxLines: 1,
                              textAlign: TextAlign.start,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const Icon(
                            Icons.arrow_forward_ios,
                            size: 11,
                          ),
                        ],
                      ),
                    ),
                  )
                  .toList(),
            ),
          ),
          if (extraJobs > 0)
            HoverBuilder(
              onTap: () {
                tabsRouter.setActiveIndex(3);
              },
              builder: (isHovering, isPressed) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '+$extraJobs daha',
                      style: context.responsive<TextStyle>(
                        desktop: ATextStyle.text12.copyWith(
                          color: isHovering ? AColor.primaryColor : null,
                        ),
                        tablet: ATextStyle.text11.copyWith(
                          color: isHovering ? AColor.primaryColor : null,
                        ),
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: context.responsive<double>(
                        desktop: 12,
                        tablet: 11,
                      ),
                      color: isHovering ? AColor.primaryColor : null,
                    ),
                  ],
                );
              },
            ),
        ],
      );
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: CommonDecorations.containerDecoration(),
      child: Row(
        children: [
          Expanded(
            child: buildJobContainer(
              title: 'Açık İşler',
              jobs: [
                'Müşteri İlişkileri Yönetimi ve Strateji Geliştirme',
                'Yazılım Geliştirme ve Sistem Entegrasyonu',
                'Veri Analizi ve Raporlama Süreçleri',
                'Proje Yönetimi ve Kaynak Planlaması',
                'Sistem Altyapı Bakımı ve Performans İyileştirmesi',
              ],
            ),
          ),
          const VerticalDivider(
            thickness: 1,
            width: 1,
          ),
          Expanded(
            child: buildJobContainer(
              title: 'Önemli İşler',
              jobs: [
                'Veri Analizi ve Raporlama Süreçleri',
                'Proje Yönetimi ve Kaynak Planlaması',
                'Sistem Altyapı Bakımı ve Performans İyileştirmesi',
              ],
            ),
          ),
        ],
      ),
    );
  }
}

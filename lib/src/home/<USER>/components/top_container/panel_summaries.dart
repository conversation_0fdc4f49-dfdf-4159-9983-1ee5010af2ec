part of '../../dashboard_screen_view.dart';

class _PanelSummaries extends StatelessWidget {
  const _PanelSummaries({
    required this.fields,
  });

  final List<PanelSummaryField> fields;

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 12,
      children: fields
          .map((field) => Expanded(child: PanelSummaryCard(field: field)))
          .toList(),
    );
  }
}

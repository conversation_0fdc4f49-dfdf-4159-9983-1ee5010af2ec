part of '../../dashboard_screen_view.dart';

class _InstantStatusCard extends StatelessWidget {
  const _InstantStatusCard();

  @override
  Widget build(BuildContext context) {
    const instantMovement = 9;
    const instantStoppage = 4;
    const totalStatus = instantMovement + instantStoppage;
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: CommonDecorations.containerDecoration(),
      child: Row(
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: Text(
                    'An<PERSON>ık Hareketler'.hardcoded,
                    style: context.responsive<TextStyle>(
                      desktop: ATextStyle.text16,
                      tablet: ATextStyle.text14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                LayoutBuilder(
                  builder: (_, constraints) {
                    return Container(
                      width:
                          instantMovement * constraints.maxWidth / totalStatus,
                      height: 10,
                      decoration: const BoxDecoration(
                        color: AColor.green,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(25),
                          bottomLeft: Radius.circular(25),
                        ),
                      ),
                    );
                  },
                ),
                Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: Text(
                    instantMovement.toString(),
                    style: context.responsive<TextStyle>(
                      desktop: ATextStyle.text18,
                      tablet: ATextStyle.text16,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const VerticalDivider(
            thickness: 1,
            width: 1,
          ),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 12),
                  child: Text(
                    'Anlık Durma'.hardcoded,
                    style: context.responsive<TextStyle>(
                      desktop: ATextStyle.text16,
                      tablet: ATextStyle.text14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                LayoutBuilder(
                  builder: (_, constraints) {
                    return Container(
                      width:
                          instantStoppage * constraints.maxWidth / totalStatus,
                      height: 10,
                      decoration: const BoxDecoration(
                        color: AColor.red,
                        borderRadius: BorderRadius.only(
                          topRight: Radius.circular(25),
                          bottomRight: Radius.circular(25),
                        ),
                      ),
                    );
                  },
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 12),
                  child: Text(
                    instantStoppage.toString(),
                    style: context.responsive<TextStyle>(
                      desktop: ATextStyle.text18,
                      tablet: ATextStyle.text16,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

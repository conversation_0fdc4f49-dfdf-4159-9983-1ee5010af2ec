part of '../../dashboard_screen_view.dart';

class _Meetings extends StatelessWidget {
  const _Meetings();

  @override
  Widget build(BuildContext context) {
    Widget buildMeetingItem({
      required String title,
      required String time,
      required String personName,
    }) {
      return Row(
        children: [
          Expanded(
            child: Text(' • $title', style: ATextStyle.text11),
          ),
          Expanded(
            child: Text(time, style: ATextStyle.text11),
          ),
          Expanded(
            child: Text(personName, style: ATextStyle.text11),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Detay'.hardcoded,
                style: ATextStyle.text11,
              ),
              const Icon(
                Icons.arrow_forward_ios,
                size: 11,
              ),
            ],
          ),
        ],
      );
    }

    final tabsRouter = AutoTabsRouter.of(context);
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: CommonDecorations.containerDecoration(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          HoverBuilder(
            onTap: () {
              tabsRouter.setActiveIndex(2);
            },
            builder: (isHovering, isPressed) {
              return Container(
                width: double.infinity,
                height: context.responsive<double>(
                  desktop: 30,
                  tablet: 25,
                ),
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: const BoxDecoration(
                  color: AColor.backgroundColor,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Günün Toplantıları'.hardcoded,
                      style: context.responsive<TextStyle>(
                        desktop: ATextStyle.text14.copyWith(
                          color: isHovering ? AColor.primaryColor : null,
                        ),
                        tablet: ATextStyle.text12.copyWith(
                          color: isHovering ? AColor.primaryColor : null,
                        ),
                      ),
                    ),
                    Text(
                      '6'.hardcoded,
                      style: context.responsive<TextStyle>(
                        desktop: ATextStyle.text16.copyWith(
                          color: isHovering ? AColor.primaryColor : null,
                        ),
                        tablet: ATextStyle.text14.copyWith(
                          color: isHovering ? AColor.primaryColor : null,
                        ),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              );
            },
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  buildMeetingItem(
                    title: 'Toplantı 1',
                    time: '10:00',
                    personName: 'Ahmet Yılmaz',
                  ),
                  buildMeetingItem(
                    title: 'Toplantı 2',
                    time: '10:00 - 14:00',
                    personName: 'Gözde Karayel',
                  ),
                ],
              ),
            ),
          ),
          HoverBuilder(
            onTap: () {
              tabsRouter.setActiveIndex(2);
            },
            builder: (isHovering, isPressed) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '+4 daha',
                      style: context.responsive<TextStyle>(
                        desktop: ATextStyle.text13.copyWith(
                          color: isHovering ? AColor.primaryColor : null,
                        ),
                        tablet: ATextStyle.text12.copyWith(
                          color: isHovering ? AColor.primaryColor : null,
                        ),
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: context.responsive<double>(
                        desktop: 13,
                        tablet: 12,
                      ),
                      color: isHovering ? AColor.primaryColor : null,
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

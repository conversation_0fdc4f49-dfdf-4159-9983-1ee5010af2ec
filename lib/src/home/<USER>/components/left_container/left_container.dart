part of '../../dashboard_screen_view.dart';

class LeftContainer extends HookConsumerWidget {
  const LeftContainer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedMembers = ref.watch(selectedMembersProvider);

    return Container(
      height: TeamListTreeConstants.leftContainerHeight(context),
      width: TeamListTreeConstants.leftContainerWidth(context),
      padding: context.responsive(
        desktop: const EdgeInsets.symmetric(horizontal: 10),
        tablet: const EdgeInsets.symmetric(horizontal: 5),
      ),
      child: Align(
        alignment: Alignment.topCenter,
        child: EmployeeTreeView(
          selectedMembers: selectedMembers,
          onTeamMemberSelected: (members) {
            ref.read(selectedMembersProvider.notifier).state = members;
          },
        ),
      ),
    );
  }
}

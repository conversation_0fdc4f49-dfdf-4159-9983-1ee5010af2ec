// part of '../../dashboard_screen_view.dart';

// class _MembersList extends HookConsumerWidget {
//   const _MembersList();

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final dashboardState = ref.watch(dashboardNotifierProvider);
//     final filteredMembers = DashboardMockData.getMembers()

//     final nonEmptyTeamHeaders = ['Ekip 1', 'Ekip 2', 'Ekip 3'].take(filteredMembers.length).toList();

//     return AppTreeView<TeamMemberModel>(
//       expandHeader: dashboardState.expandAllMembers,
//       subListHeaders: nonEmptyTeamHeaders,
//       subListData: filteredMembers,
//       onHeaderPressed: () {
//         ref.read(dashboardNotifierProvider.notifier).setAllMembersSelected();
//       },
//       isHeaderSelected: dashboardState.allMembersSelected,
//       itemBuilder: (teamMember) => HoverBuilder(
//         onTap: () => ref.read(dashboardNotifierProvider.notifier).setSelectedMember(teamMember),
//         builder: (isHovered, _) {
//           final isSelected = dashboardState.selectedMember == teamMember;
//           final hasBackground = isHovered || isSelected;
//           return AnimatedContainer(
//             duration: const Duration(milliseconds: 300),
//             color: hasBackground ? AColor.grayMedium : AColor.lightGreyBackground,
//             padding: const EdgeInsets.all(4),
//             child: Row(
//               spacing: 4,
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 Icon(
//                   Icons.wifi,
//                   color: teamMember.isActive ? AColor.green : AColor.red,
//                   size: 20,
//                 ),
//                 Flexible(
//                   child: AnimatedDefaultTextStyle(
//                     duration: const Duration(milliseconds: 150),
//                     style: hasBackground
//                         ? ATextStyle.mediumRegular.copyWith(color: AColor.white)
//                         : ATextStyle.mediumRegular,
//                     child: Text(dashboardState.filterByName ? teamMember.toString() : teamMember.phoneNumber),
//                   ),
//                 ),
//               ],
//             ),
//           );
//         },
//       ),
//     );
//   }
// }

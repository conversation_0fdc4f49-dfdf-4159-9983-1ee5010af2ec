part of '../../dashboard_screen_view.dart';

class _ThreeDotMenu extends HookConsumerWidget {
  const _ThreeDotMenu({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isMenuOpen = useState(false);
    final filterByName = ref.watch(dashboardNotifierProvider.select((state) => state.filterByName));

    Widget buildRadioListTile(String text, {required VoidCallback onTap, bool isSelected = false}) {
      return InkWell(
        onTap: onTap,
        child: Row(
          spacing: 4,
          children: [
            Container(
              width: 13,
              height: 13,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: isSelected ? AColor.scienceBlue : AColor.loginBorderColor),
              ),
              child: Center(
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: isSelected ? AColor.scienceBlue : AColor.white,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
            Expanded(child: Text(text, style: ATextStyle.semiSmallRegular)),
          ],
        ),
      );
    }

    return AppOverlay(
      isOpen: isMenuOpen.value,
      onOutsideClick: () {
        isMenuOpen.value = false;
      },
      followerMatchTargetSize: false,
      position: AppOverlayPosition.bottomLeft,
      buildOverlay: (_) => Container(
        width: 140,
        padding: const EdgeInsets.symmetric(vertical: 4).copyWith(left: 14),
        decoration: BoxDecoration(
          color: AColor.white,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          spacing: 4,
          children: [
            InkWell(
              onTap: () =>
                  ref.read(dashboardNotifierProvider.notifier).setExpandAllMembers(expandMembers: true),
              child: Text('Tümünü genişlet', style: ATextStyle.semiSmallRegular),
            ),
            InkWell(
              onTap: () =>
                  ref.read(dashboardNotifierProvider.notifier).setExpandAllMembers(expandMembers: false),
              child: Text('Tümünü daralt', style: ATextStyle.semiSmallRegular),
            ),
            buildRadioListTile(
              'Sürücü',
              onTap: () => ref.read(dashboardNotifierProvider.notifier).setFilterByName(filterByName: true),
              isSelected: filterByName,
            ),
            buildRadioListTile(
              'Gsm',
              onTap: () => ref.read(dashboardNotifierProvider.notifier).setFilterByName(filterByName: false),
              isSelected: !filterByName,
            ),
          ],
        ),
      ),
      buildTrigger: (_) => HoverBuilder(
        onTap: () {
          isMenuOpen.value = !isMenuOpen.value;
        },
        builder: (isHovered, _) {
          return Container(
            height: 32,
            decoration: BoxDecoration(
              color: AColor.white,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(
              Icons.more_vert_rounded,
              color: isHovered ? AColor.primaryColor : AColor.black,
            ),
          );
        },
      ),
    );
  }
}

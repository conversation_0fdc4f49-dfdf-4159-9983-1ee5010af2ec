import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:smart_team_common/src/models/teams/team_member/team_member.dart';

part 'team_list_model.freezed.dart';
part 'team_list_model.g.dart';

@freezed
abstract class TeamListModel with _$TeamListModel {
  const factory TeamListModel({
    required String id,
    required String name,
    @Default([]) List<TeamMember> members,
    @Default([]) List<TeamListModel> subTeams,
  }) = _TeamListModel;

  factory TeamListModel.fromJson(Map<String, dynamic> json) =>
      _$TeamListModelFromJson(json);
}

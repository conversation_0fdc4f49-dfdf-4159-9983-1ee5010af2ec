import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/home/<USER>/employees_provider.dart';
import 'package:smart_team_web/src/home/<USER>/team_list_model.dart';

final queryProvider = StateProvider<String>((ref) => '');

final filteredTeamsProvider =
    Provider<AsyncValue<List<TeamsWithMembersModel>>>((ref) {
  final teamsAsync = ref.watch(employeesFutureProvider);
  final query = ref.watch(queryProvider).trim().toLowerCase();

  return teamsAsync.when(
    data: (teams) {
      if (query.isEmpty) {
        return AsyncValue.data(teams);
      }

      final filtered = teams
          .map((team) {
            final members = team.userList.where((m) {
              final name = (m.userName ?? '').toLowerCase();
              final phone = (m.phone ?? '').toLowerCase();
              return name.contains(query) || phone.contains(query);
            }).toList();

            return team.copyWith(userList: members);
          })
          .where(
            (team) =>
                team.userList.isNotEmpty ||
                team.teamName.toLowerCase().contains(query),
          )
          .toList();

      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: AsyncValue.error,
  );
});

class FilteredTeamNotifier extends StateNotifier<List<TeamListModel>> {
  FilteredTeamNotifier(List<TeamListModel> initialTeams) : super(initialTeams) {
    _allTeams = initialTeams;
  }

  late final List<TeamListModel> _allTeams;

  void filterTeams(String query) {
    if (query.isEmpty) {
      state = _allTeams;
    } else {
      state = _allTeams
          .where(
            (team) =>
                team.name.toLowerCase().contains(query.toLowerCase()) ||
                team.members.any(
                  (member) =>
                      (member.user?.name ?? '')
                          .toLowerCase()
                          .contains(query.toLowerCase()) ||
                      (member.user?.phone ?? '')
                          .toLowerCase()
                          .contains(query.toLowerCase()),
                ),
          )
          .toList();
    }
  }
}

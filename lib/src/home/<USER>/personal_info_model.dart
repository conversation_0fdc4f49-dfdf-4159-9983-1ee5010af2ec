import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:smart_team_web/src/home/<USER>/emergency_contact_model.dart';

part 'personal_info_model.freezed.dart';
part 'personal_info_model.g.dart';

@freezed
abstract class PersonalInfoModel with _$PersonalInfoModel {
  const factory PersonalInfoModel({
    required String id,
    DateTime? birthDate,
    String? gender,
    String? nationality,
    String? idNumber,
    String? maritalStatus,
    String? childrenCount,
    String? businessEmail,
    String? personalEmail,
    String? businessPhone,
    String? personalPhone,
    String? address,
    EmergencyContactModel? emergencyContact,
  }) = _PersonalInfoModel;

  factory PersonalInfoModel.fromJson(Map<String, dynamic> json) =>
      _$PersonalInfoModelFromJson(json);
}

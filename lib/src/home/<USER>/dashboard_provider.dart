import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/home/<USER>/employees_provider.dart';
import 'package:smart_team_web/src/home/<USER>/team_list_model.dart';

final totalMembersProvider = Provider<AsyncValue<int>>((ref) {
  final teamsAsync = ref.watch(employeesFutureProvider);

  return teamsAsync.when(
    data: (teams) {
      final total = teams.fold<int>(
        0,
        (sum, team) => sum + team.userList.length,
      );
      return AsyncValue.data(total);
    },
    loading: () => const AsyncValue.loading(),
    error: AsyncValue.error,
  );
});

int calculateTotalMembers(TeamListModel team) {
  var total = team.members.length;
  for (final subTeam in team.subTeams) {
    total += calculateTotalMembers(subTeam);
  }
  return total;
}

final queryProvider = StateProvider<String>((ref) => '');
final sortAscendingProvider = StateProvider<bool>((ref) => true);

final selectedDayProvider = StateProvider<DateTime>(
  (ref) => DateTime.now(),
);

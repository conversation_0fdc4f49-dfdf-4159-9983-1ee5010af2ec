import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';

enum CompanyType {
  commercial,
  individual,
}

extension CompanyTypeX on CompanyType {
  String toLocalizedString(BuildContext context) {
    switch (this) {
      case CompanyType.commercial:
        return context.tr.companyType.commercial;
      case CompanyType.individual:
        return context.tr.companyType.individual;
    }
  }
}

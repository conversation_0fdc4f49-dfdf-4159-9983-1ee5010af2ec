// part of '../admin_page.dart';

// class AddNewGPSDeviceWidgets extends HookConsumerWidget {
//   const AddNewGPSDeviceWidgets({
//     super.key,
//     this.device,
//   });

//   final GPSDeviceModel? device;

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final formKey = GlobalKey<FormState>();
//     final isActive = ref.watch(deviceActiveProvider);

//     final onlineOffline = ref.watch(onlineOfflineProvider);
//     final selectedOnlineStatus = useState<String?>(device?.onlineStatus);
//     final deviceIdController =
//         useTextEditingController(text: device?.deviceId ?? '');
//     final imeiController = useTextEditingController(text: device?.imei ?? '');
//     final odometerController =
//         useTextEditingController(text: device?.odometer ?? '0');

//     final brands = ref.watch(brandsProvider);
//     final models = ref.watch(modelsProvider);
//     final simCardOperators = ref.watch(simCardOperatorProvider);
//     final clients = ref.watch(connectedClientsProvider);
//     final carBrands = ref.watch(carBrandsProvider);
//     final carModels = ref.watch(carModelsProvider);

//     final selectedBrand = useState<String?>(device?.brand);
//     final selectedModel = useState<String?>(device?.model);
//     final selectedSimCardOperator = useState<String?>(device?.simCardOperator);
//     final selectedConnectedClient = useState<String?>(device?.client);
//     final selectedCarBrand = useState<String?>(device?.carBrand);
//     final selectedCarModel = useState<String?>(device?.carModel);
//     final simCardController =
//         useTextEditingController(text: device?.simCardNumber ?? '');

//     useEffect(() {
//       if (device != null) {
//         ref.read(newGpsDeviceProvider).setDevice(device!);
//       }
//       return null;
//     }, []);

//     return Form(
//       key: formKey,
//       autovalidateMode: AutovalidateMode.onUserInteraction,
//       child: SingleChildScrollView(
//         padding: const EdgeInsets.all(16),
//         child: Column(
//           spacing: 16,
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               spacing: 16,
//               children: [
//                       CustomSwitch(
//                         header: 'Deaktif/Aktif'.hardcoded,
//                         value: isActive,
//                         onChanged: (value) {
//                           ref.read(deviceActiveProvider.notifier).state = value;
//                         },
//                       ),
//                 LoadingElevatedButton(
//                   height: 45,
//                   onPressed: () async {},
//                   text: 'Reset'.hardcoded,
//                 ),
//                 LoadingElevatedButton(
//                   height: 45,
//                   onPressed: () async {},
//                   text: 'Konum İste'.hardcoded,
//                 ),
//                 LoadingElevatedButton(
//                   height: 45,
//                   onPressed: () async {},
//                   text: 'Sil'.hardcoded,
//                 ),
//                 LoadingElevatedButton(
//                   height: 45,
//                   onPressed: () async {
//                     if (!formKey.currentState!.validate()) {
//                       ref
//                           .read(toastManagerProvider)
//                           .showToast('Tüm alanları doldurunuz.'.hardcoded);
//                       return;
//                     }
//                     if (device == null) {
//                       await ref.read(newGpsDeviceProvider).createDevice();
//                     } else {
//                       await ref.read(newGpsDeviceProvider).updateDevice();
//                     }
//                   },
//                   text: context.tr.save,
//                 ),
//               ],
//             ),
//             Row(
//               spacing: 16,
//               children: [
//                 Expanded(
//                   child: GPSDeviceFormLeftColumn(
//                     deviceIdController: deviceIdController,
//                     imeiController: imeiController,
//                     odometerController: odometerController,
//                     selectedOnlineStatus: selectedOnlineStatus,
//                     onlineOffline: onlineOffline,
//                   ),
//                 ),
//                 Expanded(
//                   child: GPSDeviceFormRightColumn(
//                     brands: brands,
//                     models: models,
//                     simCardOperators: simCardOperators,
//                     clients: clients,
//                     carBrands: carBrands,
//                     carModels: carModels,
//                     selectedBrand: selectedBrand,
//                     selectedModel: selectedModel,
//                     selectedSimCardOperator: selectedSimCardOperator,
//                     selectedConnectedClient: selectedConnectedClient,
//                     selectedCarBrand: selectedCarBrand,
//                     selectedCarModel: selectedCarModel,
//                     simCardController: simCardController,
//                   ),
//                 ),
//               ],
//             ),
//             Container(
//               height: 300,
//               decoration: CommonDecorations.containerDecoration(),
//               child: ClipRRect(
//                 borderRadius: BorderRadius.circular(10),
//                 child: GoogleMap(
//                   initialCameraPosition: const CameraPosition(
//                     target: LatLng(41.015137, 28.979530),
//                     zoom: 6,
//                   ),
//                   markers: {
//                     const Marker(
//                       markerId: MarkerId('mockLocation'),
//                       position: LatLng(41.015137, 28.979530),
//                     ),
//                   },
//                   onMapCreated: (controller) {},
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

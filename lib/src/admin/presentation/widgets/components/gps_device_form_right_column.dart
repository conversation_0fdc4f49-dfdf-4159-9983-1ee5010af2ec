part of '../../admin_page.dart';

class GPSDeviceFormRightColumn extends HookConsumerWidget {
  const GPSDeviceFormRightColumn({
    required this.brands,
    required this.models,
    required this.simCardOperators,
    required this.clients,
    required this.carBrands,
    required this.carModels,
    required this.selectedBrand,
    required this.selectedModel,
    required this.selectedSimCardOperator,
    required this.selectedConnectedClient,
    required this.selectedCarBrand,
    required this.selectedCarModel,
    required this.simCardController,
    super.key,
  });

  final List<String> brands;
  final List<String> models;
  final List<String> simCardOperators;
  final List<String> clients;
  final List<String> carBrands;
  final List<String> carModels;

  final ValueNotifier<String?> selectedBrand;
  final ValueNotifier<String?> selectedModel;
  final ValueNotifier<String?> selectedSimCardOperator;
  final ValueNotifier<String?> selectedConnectedClient;
  final ValueNotifier<String?> selectedCarBrand;
  final ValueNotifier<String?> selectedCarModel;

  final TextEditingController simCardController;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      spacing: 16,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ANewDropdownFormField<String>(
          header: 'Marka'.hardcoded,
          placeholder: context.tr.select,
          itemList: brands,
          selectedItems:
              selectedBrand.value == null ? [] : [selectedBrand.value!],
          onSelected: (value) {
            selectedBrand.value = value;
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return context.tr.fieldRequired;
            }
            return null;
          },
        ),
        ANewDropdownFormField<String>(
          header: 'Model'.hardcoded,
          placeholder: context.tr.select,
          itemList: models,
          selectedItems:
              selectedModel.value == null ? [] : [selectedModel.value!],
          onSelected: (value) {
            selectedModel.value = value;
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return context.tr.fieldRequired;
            }
            return null;
          },
        ),
        ANewDropdownFormField<String>(
          header: 'Sim Kartı'.hardcoded,
          placeholder: context.tr.select,
          itemList: simCardOperators,
          selectedItems: selectedSimCardOperator.value == null
              ? []
              : [selectedSimCardOperator.value!],
          onSelected: (value) {
            selectedSimCardOperator.value = value;
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return context.tr.fieldRequired;
            }
            return null;
          },
        ),
        ANewDropdownFormField<String>(
          header: 'Bağlı Müşteri'.hardcoded,
          placeholder: context.tr.select,
          itemList: clients,
          selectedItems: selectedConnectedClient.value == null
              ? []
              : [selectedConnectedClient.value!],
          onSelected: (value) {
            selectedConnectedClient.value = value;
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return context.tr.fieldRequired;
            }
            return null;
          },
        ),
        Container(
          decoration: CommonDecorations.containerDecoration(),
          child: DateTimePickerField(
            header: 'Aktivasyon Tarihi'.hardcoded,
          ),
        ),
        Container(
          decoration: CommonDecorations.containerDecoration(),
          child: CustomTextFormField(
            headerText: 'Sim Kart No'.hardcoded,
            controller: simCardController,
            hintText: '',
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(12),
            ],
            regexType: RegexType.id,
            suffixIcon: Padding(
              padding: const EdgeInsets.all(1),
              child: Container(
                decoration: const BoxDecoration(
                  color: AColor.mercury,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(2),
                    bottomRight: Radius.circular(2),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    PressIconButton(
                      icon: const Icon(Icons.arrow_drop_up),
                      onTap: () {
                        final currentValue = int.tryParse(
                              simCardController.text,
                            ) ??
                            0;
                        if (currentValue < 999) {
                          simCardController.text =
                              (currentValue + 1).toString();
                        }
                      },
                    ),
                    PressIconButton(
                      icon: const Icon(Icons.arrow_drop_down),
                      onTap: () {
                        final currentValue = int.tryParse(
                              simCardController.text,
                            ) ??
                            0;
                        simCardController.text = (currentValue - 1).toString();
                      },
                    ),
                  ],
                ),
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Sim kart no alanı boş kalamaz'.hardcoded;
              }
              return null;
            },
          ),
        ),
        ANewDropdownFormField<String>(
          header: 'Araç Marka'.hardcoded,
          placeholder: context.tr.select,
          itemList: carBrands,
          selectedItems:
              selectedCarBrand.value == null ? [] : [selectedCarBrand.value!],
          onSelected: (value) {
            selectedCarBrand.value = value;
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return context.tr.fieldRequired;
            }
            return null;
          },
        ),
        ANewDropdownFormField<String>(
          header: 'Araç Model'.hardcoded,
          placeholder: context.tr.select,
          itemList: carModels,
          selectedItems:
              selectedCarModel.value == null ? [] : [selectedCarModel.value!],
          onSelected: (value) {
            selectedCarModel.value = value;
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return context.tr.fieldRequired;
            }
            return null;
          },
        ),
      ],
    );
  }
}

part of '../admin_page.dart';

class SettingsWidgets extends HookConsumerWidget {
  const SettingsWidgets({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabController = useTabController(
      initialLength: 5,
      animationDuration: Duration.zero,
    );

    return SizedBox(
      width: 800,
      height: 600,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TabBar(
            tabAlignment: TabAlignment.start,
            controller: tabController,
            isScrollable: true,
            labelColor: AColor.red,
            unselectedLabelColor: AColor.black,
            indicatorColor: AColor.transparent,
            dividerColor: AColor.loginBorderColor,
            indicatorSize: TabBarIndicatorSize.label,
            labelPadding: EdgeInsets.zero,
            padding: EdgeInsets.zero,
            indicator: const BoxDecoration(
              border: Border(
                top: BorderSide(color: AColor.loginBorderColor),
                left: BorderSide(color: AColor.loginBorderColor),
                right: BorderSide(color: AColor.loginBorderColor),
                bottom: BorderSide(color: AColor.white, width: 2),
              ),
            ),
            tabs: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Tab(text: 'SMS'.hardcoded),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Tab(text: 'Mail'.hardcoded),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Tab(text: 'Firebase'.hardcoded),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Tab(text: 'Push Mesaj'.hardcoded),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Tab(text: 'Sistem Sabitleri'.hardcoded),
              ),
            ],
          ),
          Expanded(
            child: TabBarView(
              controller: tabController,
              physics: const NeverScrollableScrollPhysics(),
              children: const [
                _SmsSettings(),
                _MailSettings(),
                _FirebaseSettings(),
                _PushNotificationSettings(),
                _SystemConstantsSettings(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _SmsSettings extends HookConsumerWidget {
  const _SmsSettings();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final apiUrl = useState('');
    final headerCode = useState('');
    final sender = useState('');
    final userName = useState('');
    final password = useState('');
    final newDeviceMessage = useState('');

    return SingleChildScrollView(
      child: Container(
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(color: AColor.loginBorderColor),
            left: BorderSide(color: AColor.loginBorderColor),
            right: BorderSide(color: AColor.loginBorderColor),
            top: BorderSide(color: AColor.transparent, width: 0),
          ),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: CommonDecorations.containerDecoration(),
              child: CustomTextFormField(
                headerText: 'Api Url'.hardcoded,
                onChanged: (value) => apiUrl.value = value,
              ),
            ),
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: CommonDecorations.containerDecoration(),
              child: CustomTextFormField(
                headerText: 'Header Code'.hardcoded,
                onChanged: (value) => headerCode.value = value,
              ),
            ),
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: CommonDecorations.containerDecoration(),
              child: CustomTextFormField(
                headerText: 'Gönderen'.hardcoded,
                onChanged: (value) => sender.value = value,
              ),
            ),
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: CommonDecorations.containerDecoration(),
              child: CustomTextFormField(
                headerText: context.tr.userName,
                onChanged: (value) => userName.value = value,
              ),
            ),
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: CommonDecorations.containerDecoration(),
              child: CustomTextFormField(
                headerText: context.tr.password,
                onChanged: (value) => password.value = value,
              ),
            ),
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: CommonDecorations.containerDecoration(),
              child: CustomTextFormField(
                headerText: 'Yeni cihaz mesaj'.hardcoded,
                minLines: 2,
                maxLines: 5,
                onChanged: (value) => newDeviceMessage.value = value,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _MailSettings extends HookConsumerWidget {
  const _MailSettings();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mailSMTP = useState('');
    final mailPort = useState('');
    final userName = useState('');
    final password = useState('');
    final newMailMessage = useState('');

    return SingleChildScrollView(
      child: Container(
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(color: AColor.loginBorderColor),
            left: BorderSide(color: AColor.loginBorderColor),
            right: BorderSide(color: AColor.loginBorderColor),
            top: BorderSide(color: AColor.transparent, width: 0),
          ),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: CommonDecorations.containerDecoration(),
              child: CustomTextFormField(
                headerText: 'Mail SMTP'.hardcoded,
                onChanged: (value) => mailSMTP.value = value,
              ),
            ),
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: CommonDecorations.containerDecoration(),
              child: CustomTextFormField(
                headerText: 'Mail Port'.hardcoded,
                onChanged: (value) => mailPort.value = value,
              ),
            ),
            CustomSwitch(
              header: 'SSL Aktif'.hardcoded,
              value: false,
              onChanged: (value) {},
            ),
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: CommonDecorations.containerDecoration(),
              child: CustomTextFormField(
                headerText: context.tr.userName,
                onChanged: (value) => userName.value = value,
              ),
            ),
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: CommonDecorations.containerDecoration(),
              child: CustomTextFormField(
                headerText: context.tr.password,
                onChanged: (value) => password.value = value,
              ),
            ),
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: CommonDecorations.containerDecoration(),
              child: CustomTextFormField(
                headerText: 'Yeni Mail mesaj'.hardcoded,
                minLines: 2,
                maxLines: 5,
                onChanged: (value) => newMailMessage.value = value,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _FirebaseSettings extends HookConsumerWidget {
  const _FirebaseSettings();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final serverKey = useState('');

    return SingleChildScrollView(
      child: Container(
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(color: AColor.loginBorderColor),
            left: BorderSide(color: AColor.loginBorderColor),
            right: BorderSide(color: AColor.loginBorderColor),
            top: BorderSide(color: AColor.transparent, width: 0),
          ),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: CommonDecorations.containerDecoration(),
              child: CustomTextFormField(
                headerText: 'Server Key'.hardcoded,
                onChanged: (value) => serverKey.value = value,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _PushNotificationSettings extends HookConsumerWidget {
  const _PushNotificationSettings();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final serverKey = useState('');

    return SingleChildScrollView(
      child: Container(
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(color: AColor.loginBorderColor),
            left: BorderSide(color: AColor.loginBorderColor),
            right: BorderSide(color: AColor.loginBorderColor),
            top: BorderSide(color: AColor.transparent, width: 0),
          ),
        ),
        padding: const EdgeInsets.all(16),
        child: Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: CommonDecorations.containerDecoration(),
          child: CustomTextFormField(
            headerText: 'Server Key'.hardcoded,
            onChanged: (value) => serverKey.value = value,
          ),
        ),
      ),
    );
  }
}

class _SystemConstantsSettings extends HookConsumerWidget {
  const _SystemConstantsSettings();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final serverKey = useState('');

    return SingleChildScrollView(
      child: Container(
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(color: AColor.loginBorderColor),
            left: BorderSide(color: AColor.loginBorderColor),
            right: BorderSide(color: AColor.loginBorderColor),
            top: BorderSide(color: AColor.transparent, width: 0),
          ),
        ),
        padding: const EdgeInsets.all(16),
        child: Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: CommonDecorations.containerDecoration(),
          child: CustomTextFormField(
            headerText: 'Server Key'.hardcoded,
            onChanged: (value) => serverKey.value = value,
          ),
        ),
      ),
    );
  }
}

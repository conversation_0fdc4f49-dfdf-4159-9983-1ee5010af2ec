part of '../admin_page.dart';

class AddNewUsersWidgets extends HookConsumerWidget {
  const AddNewUsersWidgets({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = GlobalKey<FormState>();
    final selectedRole = useState<DisplayPanelUserRole?>(null);

    return Form(
      key: formKey,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          spacing: 16,
          children: [
            Row(
              spacing: 16,
              children: [
                Expanded(
                  child: CustomTextFormField(
                    headerText: context.tr.nameSurname,
                    keyboardType: TextInputType.name,
                    textInputAction: TextInputAction.next,
                    onChanged: (value) {},
                    validator: (value) => value.isValidName(context),
                    regexType: RegexType.name,
                  ),
                ),
                Expanded(
                  child: CustomTextForm<PERSON>ield(
                    headerText: context.tr.email,
                    regexType: RegexType.eMail,
                    keyboardType: TextInputType.emailAddress,
                    hintText: '',
                    validator: (value) {
                      // if (controller.mailNotification) {
                      return value.isValidMail(context);
                      // }
                      // return null;
                    },
                  ),
                ),
              ],
            ),
            Row(
              spacing: 16,
              children: [
                Expanded(
                  child: ANewDropdownFormField<DisplayPanelUserRole>(
                    header: 'Rol'.hardcoded,
                    placeholder: context.tr.select,
                    itemList: DisplayPanelUserRole.getList(context),
                    selectedItems:
                        selectedRole.value == null ? [] : [selectedRole.value!],
                    onSelected: (role) {
                      selectedRole.value = role;
                    },
                    validator: (val) {
                      if (val == null) {
                        return 'Lütfen rol seçiniz'.hardcoded;
                      }
                      return null;
                    },
                  ),
                ),
                const Spacer(),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                LoadingElevatedButton(
                  height: 45,
                  onPressed: () async {
                    if (!formKey.currentState!.validate()) {
                      ref
                          .read(toastManagerProvider)
                          .showToast('Tüm alanları doldurunuz.'.hardcoded);
                      return;
                    }
                    if (formKey.currentState!.validate()) {}
                  },
                  text: context.tr.save,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

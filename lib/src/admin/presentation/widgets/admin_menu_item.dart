part of '../admin_page.dart';

class AdminMenuItem extends HookConsumerWidget {
  const AdminMenuItem({
    required this.icon,
    required this.title,
    required this.description,
    super.key,
    this.showOpen = true,
    this.showList = true,
    this.showAdd = true,
    this.onOpenSelected,
    this.onListSelected,
    this.onAddSelected,
  });
  final IconData icon;
  final String title;
  final String description;
  final bool showOpen;
  final bool showList;
  final bool showAdd;
  final void Function()? onOpenSelected;
  final void Function()? onListSelected;
  final void Function()? onAddSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final styles = ref.watch(appStyleProvider);
    return Container(
      width: context.width / 8,
      height: context.width / 8,
      padding: const EdgeInsets.all(8),
      decoration: CommonDecorations.containerDecoration(),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Icon(
              icon,
              size: 24 * styles.scale,
              color: AColor.primaryColor,
            ),
          ),
          Column(
            children: [
              Text(
                title,
                style: styles.text.body,
              ),
              Text(
                description,
                style: styles.text.bodyXXSmall,
              ),
            ],
          ),
          Row(
            spacing: 4,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              if (showOpen)
                HoverColoredIcon(
                  icon: Icons.folder,
                  text: context.tr.open,
                  size: 16 * styles.scale,
                  onSelected: (_) => onOpenSelected?.call(),
                  color: AColor.textColor,
                ),
              if (showList)
                HoverColoredIcon(
                  icon: Icons.list_rounded,
                  text: context.tr.list,
                  size: 16 * styles.scale,
                  onSelected: (_) => onListSelected?.call(),
                  color: AColor.textColor,
                ),
              if (showAdd)
                HoverColoredIcon(
                  icon: Icons.add_box_rounded,
                  text: context.tr.addNew,
                  size: 16 * styles.scale,
                  onSelected: (_) => onAddSelected?.call(),
                  color: AColor.textColor,
                ),
            ],
          ),
        ],
      ),
    );
  }
}

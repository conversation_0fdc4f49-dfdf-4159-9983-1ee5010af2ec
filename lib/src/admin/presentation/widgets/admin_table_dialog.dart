import 'package:flutter/material.dart';
import 'package:smart_team_web/src/home/<USER>/admin_devices/widgets/enhanced_admin_table_dialog.dart';

class AdminTableDialog extends StatelessWidget {
  const AdminTableDialog({
    required this.columns,
    required this.data,
    this.showExportButtons = false,
    super.key,
  });

  final List<String> columns;
  final List<List<String>> data;
  final bool showExportButtons;

  @override
  Widget build(BuildContext context) {
    return EnhancedAdminTableDialog(
      columns: columns,
      data: data,
    );
  }
}

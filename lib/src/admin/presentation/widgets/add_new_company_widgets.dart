part of '../admin_page.dart';

class AddNewCompanyWidgets extends HookConsumerWidget {
  const AddNewCompanyWidgets({
    super.key,
    this.company,
    this.companyBilling,
  });

  final Company? company;
  final CompanyBilling? companyBilling;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.watch(addCompanyProvider);
    final isEditing = company != null;
    final customerNameController =
        useTextEditingController(text: company?.name);
    final taxNumberController =
        useTextEditingController(text: companyBilling?.vatNumber);
    final taxOfficeController =
        useTextEditingController(text: companyBilling?.taxId);
    final contactNameController =
        useTextEditingController(text: company?.contactName);
    final contactPhoneController =
        useTextEditingController(text: company?.contactPhone);

    useEffect(
      () {
        ref.read(addCompanyProvider).setCompany(company);
        ref.read(addCompanyProvider).setCompanyBilling(companyBilling);
        return null;
      },
      [],
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 16,
        children: [
          Expanded(
            child: Column(
              spacing: 8,
              children: [
                CustomTextFormField(
                  controller: customerNameController,
                  headerText: context.tr.customerName,
                  keyboardType: TextInputType.name,
                  textInputAction: TextInputAction.next,
                  validator: (value) => value.isValidName(context),
                  regexType: RegexType.name,
                  onChanged: ref.read(addCompanyProvider).setCompanyName,
                ),
                CustomTextFormField(
                  controller: taxNumberController,
                  headerText: context.tr.taxNumber,
                  keyboardType: TextInputType.number,
                  textInputAction: TextInputAction.next,
                  validator: (value) => value.isValidId(context),
                  regexType: RegexType.id,
                  onChanged: ref.read(addCompanyProvider).setVatNumber,
                ),
              ],
            ),
          ),
          Expanded(
            child: Column(
              spacing: 8,
              children: [
                CustomTextFormField(
                  controller: taxOfficeController,
                  headerText: context.tr.taxOffice,
                  keyboardType: TextInputType.text,
                  textInputAction: TextInputAction.next,
                  onChanged: ref.read(addCompanyProvider).setTaxId,
                  validator: (value) => value.isValidName(context),
                ),
                CustomTextFormField(
                  controller: contactNameController,
                  headerText: context.tr.contactName,
                  keyboardType: TextInputType.name,
                  textInputAction: TextInputAction.next,
                  onChanged: ref.read(addCompanyProvider).setCompanyContactName,
                  validator: (value) => value.isValidName(context),
                ),
                CustomTextFormField(
                  controller: contactPhoneController,
                  headerText: context.tr.contactGSM,
                  keyboardType: TextInputType.number,
                  textInputAction: TextInputAction.next,
                  onChanged:
                      ref.read(addCompanyProvider).setCompanyContactPhone,
                  validator: (value) => value.isValidPhone(context),
                  regexType: RegexType.phone,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

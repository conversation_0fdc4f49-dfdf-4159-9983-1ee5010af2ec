part of '../../admin_page.dart';

class _AdminLeftContainer extends HookConsumerWidget {
  const _AdminLeftContainer();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: 280,
      margin: const EdgeInsets.only(top: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 10,
        children: [
          CustomTextFormField(
            hintText: context.tr.search,
            prefixIcon: const Icon(Icons.search),
            debounceDuration: const Duration(milliseconds: 500),
            onChanged: ref
                .read(adminDashboardNotifierProvider.notifier)
                .setCompanySearchText,
          ),
          const Expanded(
            child: _CompaniesList(),
          ),
        ],
      ),
    );
  }
}

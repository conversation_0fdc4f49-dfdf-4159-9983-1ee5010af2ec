part of '../../admin_page.dart';

class _CompaniesList extends HookConsumerWidget {
  const _CompaniesList();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final companiesAsync = ref.watch(companiesFutureProvider);
    final selectedAsync = ref.watch(selectedCompanyProvider);

    return BaseAsyncProviderWidget<List<Company>>(
      value: companiesAsync,
      loadingWidget: ListView.separated(
        itemCount: 10,
        separatorBuilder: (_, __) => const SizedBox(height: 10),
        itemBuilder: (_, __) => const AppShimmer(
          width: double.infinity,
          height: 20,
        ),
      ),
      builder: (companies) {
        final selected = selectedAsync.value;

        return ListView.separated(
          itemCount: companies.length,
          separatorBuilder: (_, __) => const SizedBox(height: 10),
          itemBuilder: (context, index) {
            final company = companies[index];
            final isSelected = selected?.id == company.id;

            return HoverBuilder(
              onTap: () {
                ref
                    .read(selectedCompanyProvider.notifier)
                    .selectCompany(company);
                ref.read(globalStateManagerProvider.notifier).setSelectedCompany(company);
              },
              builder: (isHovered, _) {
                final hasBackground = isHovered || isSelected;
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  color: hasBackground
                      ? AColor.grayMedium
                      : AColor.lightGreyBackground,
                  padding: const EdgeInsets.all(4),
                  child: TextWidget(
                    company.name,
                    style: hasBackground
                        ? ATextStyle.mediumRegular.copyWith(color: AColor.white)
                        : ATextStyle.mediumRegular,
                  ),
                );
              },
            );
          },
        );
      },
    );
  }
}

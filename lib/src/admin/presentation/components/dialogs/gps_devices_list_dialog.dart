// part of '../../admin_page.dart';

// class _GPSDevicesListDialog extends HookConsumerWidget {
//   const _GPSDevicesListDialog({this.onEditClicked});

//   final void Function(GPSDeviceModel)? onEditClicked;

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final devicesAsync = ref.watch(gpsDevicesFutureProvider);

//     List<List<RowDataModel<dynamic>>> generateRowData(
//       List<GPSDeviceModel> devices,
//     ) {
//       return devices.map((d) {
//         return [
//           // Zorunlu ve non-null
//           RowDataModel<String>(
//             columnName: 'Cihaz ID'.hardcoded,
//             value: d.deviceId,
//             cellBuilder: () => TextWidget(d.deviceId, style: ATextStyle.small),
//           ),

//           RowDataModel<String>(
//             columnName: 'IMEI'.hardcoded,
//             value: d.imei,
//             cellBuilder: () => TextWidget(d.imei, style: ATextStyle.small),
//           ),

//           // Nullable alanlar için coalesce
//           RowDataModel<String>(
//             columnName: 'Marka'.hardcoded,
//             value: d.brand ?? '',
//             cellBuilder: () =>
//                 TextWidget(d.brand ?? '', style: ATextStyle.small),
//           ),
//           RowDataModel<String>(
//             columnName: 'Model'.hardcoded,
//             value: d.model ?? '',
//             cellBuilder: () =>
//                 TextWidget(d.model ?? '', style: ATextStyle.small),
//           ),
//           RowDataModel<String>(
//             columnName: 'SIM Operatörü'.hardcoded,
//             value: d.simCardOperator ?? '',
//             cellBuilder: () =>
//                 TextWidget(d.simCardOperator ?? '', style: ATextStyle.small),
//           ),
//           RowDataModel<String>(
//             columnName: 'SIM Kart No'.hardcoded,
//             value: d.simCardNumber ?? '',
//             cellBuilder: () =>
//                 TextWidget(d.simCardNumber ?? '', style: ATextStyle.small),
//           ),
//           RowDataModel<String>(
//             columnName: 'Müşteri'.hardcoded,
//             value: d.client ?? '',
//             cellBuilder: () =>
//                 TextWidget(d.client ?? '', style: ATextStyle.small),
//           ),
//           RowDataModel<String>(
//             columnName: 'Araç Markası'.hardcoded,
//             value: d.carBrand ?? '',
//             cellBuilder: () =>
//                 TextWidget(d.carBrand ?? '', style: ATextStyle.small),
//           ),
//           RowDataModel<String>(
//             columnName: 'Araç Modeli'.hardcoded,
//             value: d.carModel ?? '',
//             cellBuilder: () =>
//                 TextWidget(d.carModel ?? '', style: ATextStyle.small),
//           ),

//           // Diğer alanlarda da aynı şekilde…
//           // Örneğin kilometre:
//           RowDataModel<String>(
//             columnName: 'Kilometre'.hardcoded,
//             value: d.odometer,
//             cellBuilder: () => TextWidget(d.odometer, style: ATextStyle.small),
//           ),

//           // Durum ve aktiflik örneği
//           RowDataModel<String>(
//             columnName: 'Durum'.hardcoded,
//             value: d.onlineStatus,
//             cellBuilder: () =>
//                 TextWidget(d.onlineStatus, style: ATextStyle.small),
//           ),
//           RowDataModel<String>(
//             columnName: 'Aktif'.hardcoded,
//             value: d.isActive ? 'Evet' : 'Hayır',
//             cellBuilder: () => TextWidget(d.isActive ? 'Evet' : 'Hayır',
//                 style: ATextStyle.small),
//           ),

//           // Düzenle butonu
//           RowDataModel(
//             columnName: '',
//             cellBuilder: () => LoadingElevatedButton(
//               text: 'Düzenle'.hardcoded,
//               height: 30,
//               width: 100,
//               onPressed: () async {
//                 await context.maybePop();
//                 onEditClicked?.call(d);
//               },
//             ),
//           ),
//         ];
//       }).toList();
//     }

//     return BaseAsyncProviderWidget<List<GPSDeviceModel>>(
//       value: devicesAsync,
//       loadingWidget: const Placeholder(),
//       builder: (devices) {
//         return Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 16),
//           child: SmartTeamDataTable(
//             columns: [
//               TableColumnModel(
//                 columnName: 'Cihaz ID'.hardcoded,
//                 filterable: false,
//                 sortable: false,
//                 width: 120,
//               ),
//               TableColumnModel(columnName: 'IMEI'.hardcoded),
//               TableColumnModel(columnName: 'Kilometre'.hardcoded),
//               TableColumnModel(columnName: 'Durum'.hardcoded),
//               TableColumnModel(columnName: 'Marka'.hardcoded),
//               TableColumnModel(columnName: 'Model'.hardcoded),
//               TableColumnModel(columnName: 'SIM Operatörü'.hardcoded),
//               TableColumnModel(columnName: 'SIM Kart No'.hardcoded),
//               TableColumnModel(columnName: 'Müşteri'.hardcoded),
//               TableColumnModel(columnName: 'Araç Markası'.hardcoded),
//               TableColumnModel(columnName: 'Araç Modeli'.hardcoded),
//               TableColumnModel(columnName: 'Aktif'.hardcoded),
//               const TableColumnModel(
//                 columnName: '',
//                 width: 100,
//                 filterable: false,
//                 sortable: false,
//               ),
//             ],
//             rowData: generateRowData(devices),
//             initialRowsPerPage: 10,
//           ),
//         );
//       },
//     );
//   }
// }

// import 'package:hooks_riverpod/hooks_riverpod.dart';
// import 'package:smart_team_common/smart_team_common.dart';
// import 'package:smart_team_web/src/admin/domain/gps_device_model.dart';

// final gpsDevicesFutureProvider =
//     FutureProvider<List<GPSDeviceModel>>((ref) async {
//   final searchQuery = ref.watch(
//     gpsDashboardNotifierProvider.select((state) => state.deviceSearchText),
//   );
//   final repository = ref.read(mobileDeviceRepositoryProvider);
//   if (searchQuery.isEmpty) {
//     return repository.fetchAll();
//   }
//   return repository.searchDevices(searchQuery);
// });

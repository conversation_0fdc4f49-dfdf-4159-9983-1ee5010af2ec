// import 'package:smart_team_common/smart_team_common.dart';
// import 'package:smart_team_web/src/admin/application/gps_devices_provider.dart';
// import 'package:smart_team_web/src/admin/domain/gps_device_model.dart';

// final addGpsDeviceProvider =
//     Provider.autoDispose<_AddGpsDeviceProvider>(_AddGpsDeviceProvider.new);

// class _AddGpsDeviceProvider {
//   _AddGpsDeviceProvider(this.ref);
//   final Ref ref;

//   GPSDeviceModel device = const GPSDeviceModel(
//     id: '',
//     deviceId: '',
//     imei: '',
//     odometer: '0',
//     isActive: true,
//     onlineStatus: 'Online',
//   );

//   void setDevice(GPSDeviceModel? d) {
//     if (d != null) device = d;
//   }

//   void setDeviceId(String v) => device = device.copyWith(deviceId: v);
//   void setImei(String v) => device = device.copyWith(imei: v);
//   void setOdometer(String v) => device = device.copyWith(odometer: v);
//   void setIsActive(bool v) => device = device.copyWith(isActive: v);
//   void setOnlineStatus(String v) => device = device.copyWith(onlineStatus: v);
//   void setBrand(String? v) => device = device.copyWith(brand: v);
//   void setModel(String? v) => device = device.copyWith(model: v);
//   void setSimCardOperator(String? v) =>
//       device = device.copyWith(simCardOperator: v);
//   void setSimCardNumber(String? v) =>
//       device = device.copyWith(simCardNumber: v);
//   void setClient(String? v) => device = device.copyWith(client: v);
//   void setCarBrand(String? v) => device = device.copyWith(carBrand: v);
//   void setCarModel(String? v) => device = device.copyWith(carModel: v);

//   Future<bool> createDevice() async {
//     try {
//       final created =
//           await ref.read(mobileDeviceRepositoryProvider).create(device);
//       device = created;
//       ref.invalidate(gpsDevicesFutureProvider);
//       return true;
//     } catch (_) {
//       return false;
//     }
//   }

//   /// Var olan cihazı günceller, sonra listeyi yeniler
//   Future<bool> updateDevice() async {
//     try {
//       final updated =
//           await ref.read(mobileDeviceRepositoryProvider).update(device);
//       if (updated != null) {
//         device = updated;
//         ref.invalidate(gpsDevicesFutureProvider);
//         return true;
//       }
//       return false;
//     } catch (_) {
//       return false;
//     }
//   }
// }

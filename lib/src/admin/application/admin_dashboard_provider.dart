import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/admin/domain/admin_dashboard_state.dart';

final adminDashboardNotifierProvider =
    AutoDisposeNotifierProvider<AdminDashboardNotifier, AdminDashboardState>(
  AdminDashboardNotifier.new,
);

class AdminDashboardNotifier extends AutoDisposeNotifier<AdminDashboardState> {
  @override
  AdminDashboardState build() {
    return const AdminDashboardState();
  }

  void setSelectedCompany(Company company) {
    state =
        state.copyWith(selectedCompany: company);
  }

  void setCompanySearchText(String text) {
    state = state.copyWith(companySearchText: text);
  }

}

import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/admin/application/admin_dashboard_provider.dart';

final companiesFutureProvider = FutureProvider<List<Company>>((ref) async {
  final searchQuery = ref.watch(
    adminDashboardNotifierProvider.select((value) => value.companySearchText),
  );
  if (searchQuery.isEmpty) {
    return ref.read(companyRepositoryProvider).fetchAll();
  }
  return ref.read(companyRepositoryProvider).searchCompanies(searchQuery);
});

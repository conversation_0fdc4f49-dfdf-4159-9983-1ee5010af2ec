import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/online_user_manager/online_user_manager.dart';
import 'package:smart_team_web/src/admin/application/companies_provider.dart';
import 'package:smart_team_web/src/shared/extensions/riverpod_extensions.dart';

final addCompanyProvider =
    Provider.autoDispose<_AddCompanyProvider>(_AddCompanyProvider.new);

class _AddCompanyProvider {
  _AddCompanyProvider(this.ref);

  final Ref ref;

  Company company = const Company(name: '');

  CompanyBilling companyBilling = const CompanyBilling();

  void setCompany(Company? company) {
    if (company == null) return;
    this.company = company.copyWith();
  }

  void setCompanyBilling(CompanyBilling? companyBilling) {
    if (companyBilling == null) return;
    this.companyBilling = companyBilling.copyWith();
  }

  void setCompanyName(String name) {
    company = company.copyWith(name: name);
  }

  void setCompanyCode(String code) {
    company = company.copyWith(code: code);
  }

  void setCompanyContactName(String contactName) {
    company = company.copyWith(contactName: contactName);
  }

  void setCompanyContactPhone(String contactPhone) {
    company = company.copyWith(contactPhone: contactPhone);
  }

  void setVatNumber(String vatNumber) {
    companyBilling = companyBilling.copyWith(vatNumber: vatNumber);
  }

  void setTaxId(String taxId) {
    companyBilling = companyBilling.copyWith(taxId: taxId);
  }

  Future<bool> createCompany() async {
    try {
      final currentUser = ref.futureValue(onlineUserManagerProvider);
      if (currentUser == null || !currentUser.isAdminUser) return false;
      await ref.read(companyRepositoryProvider).createCompany(company);
      companyBilling = companyBilling.copyWith(company: company);
      await ref
          .read(companyBillingRepositoryProvider)
          .createCompanyBilling(companyBilling);
      ref.invalidate(companiesFutureProvider);
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> updateCompany() async {
    try {
      final currentUser = ref.futureValue(onlineUserManagerProvider);
      if (currentUser == null || !currentUser.isAdminUser) return false;
      await ref.read(companyRepositoryProvider).updateCompany(company);
      companyBilling = companyBilling.copyWith(company: company);
      await ref
          .read(companyBillingRepositoryProvider)
          .updateCompanyBilling(companyBilling);
      ref.invalidate(companiesFutureProvider);
      return true;
    } catch (e, stack) {
      debugPrint(e.toString());
      debugPrint(stack.toString());
      return false;
    }
  }
}

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:smart_team_web/src/shared/enums/company_type_enum.dart';

part 'company_model.freezed.dart';
part 'company_model.g.dart';

@freezed
abstract class CompanyModel with _$CompanyModel {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory CompanyModel({
    required String id,
    required String name,
    @Default(CompanyTypeEnum.commercial) CompanyTypeEnum type,
    String? taxNumber,
    String? taxOffice,
    String? phoneNumber,
    String? website,
    String? contactName,
    String? contactGsm,
    @Default('(UTC+03:00) Istanbul') String regionalTime,
    @Default(true) bool animationColorEnabled,
    String? serviceIp,
    String? servicePort,
  }) = _CompanyModel;
  const CompanyModel._();

  factory CompanyModel.fromJson(Map<String, dynamic> json) =>
      _$CompanyModelFromJson(json);
}

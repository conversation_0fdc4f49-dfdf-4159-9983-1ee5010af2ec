import 'package:freezed_annotation/freezed_annotation.dart';

part 'gps_device_model.freezed.dart';
part 'gps_device_model.g.dart';

@freezed
abstract class GPSDeviceModel with _$GPSDeviceModel {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory GPSDeviceModel({
    required String id,
    required String deviceId,
    required String imei,
    required String odometer,
    required bool isActive,
    required String onlineStatus,
    String? brand,
    String? model,
    String? simCardOperator,
    String? client,
    String? carBrand,
    String? carModel,
    String? simCardNumber,
    DateTime? lastSeenAt,
    DateTime? registeredAt,
  }) = _GPSDeviceModel;
  const GPSDeviceModel._();

  factory GPSDeviceModel.fromJson(Map<String, dynamic> json) =>
      _$GPSDeviceModelFromJson(json);
}

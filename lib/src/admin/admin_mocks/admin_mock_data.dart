import 'package:smart_team_web/src/admin/domain/company_model.dart';

class AdminDashboardMockData {
  static List<CompanyModel> getCompanies() {
    return [
      const CompanyModel(
        id: '1',
        name: '<PERSON> Firmasi',
      ),
      const CompanyModel(
        id: '2',
        name: '<PERSON> Firmasi',
      ),
      const CompanyModel(
        id: '3',
        name: 'C Firmasi',
      ),
      const CompanyModel(
        id: '4',
        name: 'D Firmasi',
      ),
      const CompanyModel(
        id: '5',
        name: 'E Firmasi',
      ),
      const CompanyModel(
        id: '6',
        name: 'F Firmasi',
      ),
      const CompanyModel(
        id: '7',
        name: '<PERSON> Firmasi',
      ),
      const CompanyModel(
        id: '8',
        name: '<PERSON> Firmasi',
      ),
      const CompanyModel(
        id: '9',
        name: '<PERSON> Firmasi',
      ),
      const CompanyModel(
        id: '10',
        name: 'K Firmasi',
      ),
      const CompanyModel(
        id: '11',
        name: '<PERSON>si',
      ),
      const CompanyModel(
        id: '12',
        name: '<PERSON>rmasi',
      ),
      const CompanyModel(
        id: '13',
        name: '<PERSON> Firmasi',
      ),
      const CompanyModel(
        id: '1',
        name: 'A Firmasi',
      ),
      const CompanyModel(
        id: '2',
        name: 'B Firmasi',
      ),
      const CompanyModel(
        id: '3',
        name: 'C Firmasi',
      ),
      const CompanyModel(
        id: '4',
        name: 'D Firmasi',
      ),
      const CompanyModel(
        id: '5',
        name: 'E Firmasi',
      ),
      const CompanyModel(
        id: '6',
        name: 'F Firmasi',
      ),
      const CompanyModel(
        id: '7',
        name: 'G Firmasi',
      ),
      const CompanyModel(
        id: '8',
        name: 'H Firmasi',
      ),
      const CompanyModel(
        id: '9',
        name: 'J Firmasi',
      ),
      const CompanyModel(
        id: '10',
        name: 'K Firmasi',
      ),
      const CompanyModel(
        id: '11',
        name: 'L Firmasi',
      ),
      const CompanyModel(
        id: '12',
        name: 'M Firmasi',
      ),
      const CompanyModel(
        id: '13',
        name: 'N Firmasi',
      ),
      const CompanyModel(
        id: '1',
        name: 'A Firmasi',
      ),
      const CompanyModel(
        id: '2',
        name: 'B Firmasi',
      ),
      const CompanyModel(
        id: '3',
        name: 'C Firmasi',
      ),
      const CompanyModel(
        id: '4',
        name: 'D Firmasi',
      ),
      const CompanyModel(
        id: '5',
        name: 'E Firmasi',
      ),
      const CompanyModel(
        id: '6',
        name: 'F Firmasi',
      ),
      const CompanyModel(
        id: '7',
        name: 'G Firmasi',
      ),
      const CompanyModel(
        id: '8',
        name: 'H Firmasi',
      ),
      const CompanyModel(
        id: '9',
        name: 'J Firmasi',
      ),
      const CompanyModel(
        id: '10',
        name: 'K Firmasi',
      ),
      const CompanyModel(
        id: '11',
        name: 'L Firmasi',
      ),
      const CompanyModel(
        id: '12',
        name: 'M Firmasi',
      ),
      const CompanyModel(
        id: '13',
        name: 'N Firmasi',
      ),
      const CompanyModel(
        id: '1',
        name: 'A Firmasi',
      ),
      const CompanyModel(
        id: '2',
        name: 'B Firmasi',
      ),
      const CompanyModel(
        id: '3',
        name: 'C Firmasi',
      ),
      const CompanyModel(
        id: '4',
        name: 'D Firmasi',
      ),
      const CompanyModel(
        id: '5',
        name: 'E Firmasi',
      ),
      const CompanyModel(
        id: '6',
        name: 'F Firmasi',
      ),
      const CompanyModel(
        id: '7',
        name: 'G Firmasi',
      ),
      const CompanyModel(
        id: '8',
        name: 'H Firmasi',
      ),
      const CompanyModel(
        id: '9',
        name: 'J Firmasi',
      ),
      const CompanyModel(
        id: '10',
        name: 'K Firmasi',
      ),
      const CompanyModel(
        id: '11',
        name: 'L Firmasi',
      ),
      const CompanyModel(
        id: '12',
        name: 'M Firmasi',
      ),
      const CompanyModel(
        id: '13',
        name: 'N Firmasi',
      ),
      const CompanyModel(
        id: '1',
        name: 'A Firmasi',
      ),
      const CompanyModel(
        id: '2',
        name: 'B Firmasi',
      ),
      const CompanyModel(
        id: '3',
        name: 'C Firmasi',
      ),
      const CompanyModel(
        id: '4',
        name: 'D Firmasi',
      ),
      const CompanyModel(
        id: '5',
        name: 'E Firmasi',
      ),
      const CompanyModel(
        id: '6',
        name: 'F Firmasi',
      ),
      const CompanyModel(
        id: '7',
        name: 'G Firmasi',
      ),
      const CompanyModel(
        id: '8',
        name: 'H Firmasi',
      ),
      const CompanyModel(
        id: '9',
        name: 'J Firmasi',
      ),
      const CompanyModel(
        id: '10',
        name: 'K Firmasi',
      ),
      const CompanyModel(
        id: '11',
        name: 'L Firmasi',
      ),
      const CompanyModel(
        id: '12',
        name: 'M Firmasi',
      ),
      const CompanyModel(
        id: '13',
        name: 'N Firmasi',
      ),
    ];
  }
}

import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/global_state_manager/global_state_manager.dart';
import 'package:smart_team_web/src/shared/extensions/list_extensions.dart';

final jobCountFutureProvider = FutureProvider.autoDispose<TaskCountModel>((ref) async {
  final selectedCompany = ref.watch(globalStateManagerProvider.select((state) => state.selectedCompany));
  final searchQuery = ref.watch(jobListSearchQueryProvider);
  final jobCount = await ref
      .read(taskRepositoryProvider)
      .fetchTaskCounts(companyId: selectedCompany!.id!, searchQuery: searchQuery);
  return jobCount;
});

final jobListNotifier = AutoDisposeAsyncNotifierProviderFamily<JobListNotifier, PaginatedDataModel<Task>,
    ({List<TaskStatusEnum>? statuses, List<int>? priorities})>(
  JobListNotifier.new,
);

class JobListNotifier extends AutoDisposeFamilyAsyncNotifier<PaginatedDataModel<Task>,
        ({List<TaskStatusEnum>? statuses, List<int>? priorities})>
    with PaginationFamilyMixin<Task, ({List<TaskStatusEnum>? statuses, List<int>? priorities})> {
  late String _searchQuery;

  @override
  Future<PaginatedDataModel<Task>> build(
    ({List<TaskStatusEnum>? statuses, List<int>? priorities}) arg,
  ) async {
    _searchQuery = ref.watch(jobListSearchQueryProvider);
    return getData(
      filters: {
        'status': arg.statuses?.map((e) => e.id).toList(),
        'priorities': arg.priorities,
        'searchQuery': _searchQuery,
      },
    );
  }

  Future<void> fetchNextPage() async {
    await getNextData(
      filters: {
        'status': arg.statuses?.map((e) => e.id).toList(),
        'priorities': arg.priorities,
        'searchQuery': _searchQuery,
      },
    );
  }

  Future<void> deleteTask(int taskIndex) async {
    final taskToDelete = state.value!.items[taskIndex];
    await ref.read(taskRepositoryProvider).deleteByModel(taskToDelete);
    state = AsyncData(state.value!.copyWith(items: [...state.value!.items]..removeAt(taskIndex)));
    ref.invalidate(jobCountFutureProvider);
  }

  Future<void> updateTask(Task task, int taskIndex) async {
    state = AsyncData(state.value!.copyWith(items: [...state.value!.items]..replaceAt(taskIndex, task)));
  }
}

final jobListSearchQueryProvider = AutoDisposeNotifierProvider<JobListSearchQueryNotifier, String>(
  JobListSearchQueryNotifier.new,
);

class JobListSearchQueryNotifier extends AutoDisposeNotifier<String> {
  @override
  String build() {
    return '';
  }

  void setSearchQuery(String? searchQuery) {
    if (searchQuery != null && searchQuery != state) {
      state = searchQuery;
    }
  }
}

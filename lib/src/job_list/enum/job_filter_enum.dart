import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';

enum JobFilter {
  all,
  important,
  completed,
  pending,
}

extension JobFilterX on JobFilter {
  String label(BuildContext ctx) {
    switch (this) {
      case JobFilter.all:
        return 'Tü<PERSON>şler'.hardcoded;
      case JobFilter.important:
        return '<PERSON>nemli İşler'.hardcoded;
      case JobFilter.completed:
        return 'Tamamlanan <PERSON>şler'.hardcoded;
      case JobFilter.pending:
        return 'Tamamlanacak İşler'.hardcoded;
    }
  }
}

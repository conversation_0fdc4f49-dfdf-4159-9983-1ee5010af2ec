import 'package:flutter/material.dart';
import 'package:smart_team_web/i18n/strings.g.dart';

enum TaskTypeEnum {
  visit, // ziyaret
  collection, // tahsilat
  service, // servis
  onlineMeeting, // online toplantı
  phoneCall, // telefon görüşmesi
}

extension TaskTypeEnumX on TaskTypeEnum {
  String label(BuildContext context) {
    switch (this) {
      case TaskTypeEnum.visit:
        return context.t.taskType.visit;
      case TaskTypeEnum.collection:
        return context.t.taskType.collection;
      case TaskTypeEnum.service:
        return context.t.taskType.service;
      case TaskTypeEnum.onlineMeeting:
        return context.t.taskType.onlineMeeting;
      case TaskTypeEnum.phoneCall:
        return context.t.taskType.phoneCall;
    }
  }
}

part of '../job_list_view.dart';

class _JobTabSwitcher extends HookConsumerWidget {
  const _JobTabSwitcher();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final jobCountProvider = ref.watch(jobCountFutureProvider);
    final styles = ref.watch(appStyleProvider);
    final switcherWidth = context.responsive<double>(
      desktop: 1000,
      tablet: 800,
      mobile: 500,
    );
    final tabTitles = ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>lana<PERSON><PERSON>ş<PERSON>'];
    final tabController = DefaultTabController.of(context);

    return BaseAsyncProviderWidget<TaskCountModel>(
      value: jobCountProvider,
      loadingWidget: AppShimmer(
        width: switcherWidth,
        height: 50,
        borderRadius: BorderRadius.circular(12),
      ),
      builder: (jobCountInfo) {
        return AppSegmentedSwitcher(
          onTabChange: tabController.animateTo,
          tabs: tabTitles,
          width: switcherWidth,
          suffixWidgets: List.generate(
            tabTitles.length,
            (index) {
              final count = jobCountInfo.getCountByIndex(index);
              if (count == 0) {
                return const SizedBox.shrink();
              }
              return Container(
                height: 20,
                padding: const EdgeInsets.symmetric(horizontal: 8),
                decoration: BoxDecoration(
                  color: AColor.primaryColor,
                  borderRadius: BorderRadius.circular(60),
                ),
                child: Center(
                  child: Text(
                    count.toString(),
                    style: styles.text.bodyXSmall.copyWith(
                      color: AColor.white,
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}

part of '../job_list_view.dart';

Widget _jobStatusTabs({
  required BuildContext context,
  required ValueNotifier<JobFilter> selectedFilter,
  required int allJobsCount,
  required int importantJobsCount,
  required int completedJobsCount,
  required int pendingJobsCount,
}) {
  return Container(
    padding: const EdgeInsets.all(2),
    decoration: BoxDecoration(
      color: Colors.grey[300],
      borderRadius: BorderRadius.circular(10),
    ),
    child: Row(
      spacing: 8,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildStatusTab(
          label: JobFilter.all.label(context),
          count: allJobsCount,
          isSelected: selectedFilter.value == JobFilter.all,
          onTap: () => selectedFilter.value = JobFilter.all,
        ),
        _buildStatusTab(
          label: JobFilter.important.label(context),
          count: importantJobsCount,
          isSelected: selectedFilter.value == JobFilter.important,
          onTap: () => selectedFilter.value = JobFilter.important,
        ),
        _buildStatusTab(
          label: JobFilter.completed.label(context),
          count: completedJobsCount,
          isSelected: selectedFilter.value == JobFilter.completed,
          onTap: () => selectedFilter.value = JobFilter.completed,
        ),
        _buildStatusTab(
          label: JobFilter.pending.label(context),
          count: pendingJobsCount,
          isSelected: selectedFilter.value == JobFilter.pending,
          onTap: () => selectedFilter.value = JobFilter.pending,
        ),
      ],
    ),
  );
}

Widget _buildStatusTab({
  required String label,
  required int count,
  required bool isSelected,
  required VoidCallback onTap,
}) {
  return InkWell(
    onTap: onTap,
    child: Container(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      height: 36,
      decoration: BoxDecoration(
        color: isSelected ? AColor.white : Colors.grey[300],
        borderRadius:
            isSelected ? BorderRadius.circular(10) : BorderRadius.zero,
      ),
      child: Row(
        spacing: 16,
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Text(
            label,
            style: ATextStyle.text12.copyWith(
              fontWeight: FontWeight.w400,
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AColor.primaryColor,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              '$count',
              style: ATextStyle.small.copyWith(
                color: AColor.white,
              ),
            ),
          ),
        ],
      ),
    ),
  );
}

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/enums/date_time_picker_mode.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/text_form_field/date_time_picker_field.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

class ScheduleTable extends HookConsumerWidget {
  const ScheduleTable({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final daysOfWeek = [
      context.tr.monday,
      context.tr.tuesday,
      context.tr.wednesday,
      context.tr.thursday,
      context.tr.friday,
      context.tr.saturday,
      context.tr.sunday,
    ];

    final outOfHours = useState<List<bool>>(List.generate(7, (_) => false));

    Widget buildTimeRow(String label, List<bool> outOfHours) {
      return Row(
        children: [
          Expanded(
            flex: 2,
            child: TextWidget(
              label,
              style: ATextStyle.text13,
            ),
          ),
          for (int i = 0; i < 7; i++)
            Expanded(
              flex: 5,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: AbsorbPointer(
                  absorbing: outOfHours[i],
                  child: Opacity(
                    opacity: outOfHours[i] ? 0.5 : 1.0,
                    child: Container(
                      decoration: CommonDecorations.containerDecoration(),
                      child: const DateTimePickerField(
                        pickerMode: DateTimePickerMode.time,
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      );
    }

    Widget buildCheckboxRow(
      String label,
      ValueNotifier<List<bool>> checkboxValues,
    ) {
      return Row(
        children: [
          Expanded(
            flex: 2,
            child: TextWidget(
              label,
              style: ATextStyle.text13,
            ),
          ),
          for (int i = 0; i < 7; i++)
            Expanded(
              flex: 5,
              child: Center(
                child: Checkbox(
                  value: checkboxValues.value[i],
                  onChanged: (bool? newValue) {
                    final newList = List<bool>.from(checkboxValues.value);
                    newList[i] = newValue ?? false;
                    checkboxValues.value = newList;
                    outOfHours.value = newList;
                  },
                  checkColor: AColor.white,
                  activeColor: AColor.primaryColor,
                  side: const BorderSide(width: 1.5, color: AColor.grey),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
        ],
      );
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: CommonDecorations.containerDecoration(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 16,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Spacer(),
              for (final day in daysOfWeek)
                Expanded(
                  flex: 3,
                  child: Center(
                    child: TextWidget(
                      day,
                      style: ATextStyle.text13,
                    ),
                  ),
                ),
            ],
          ),
          buildTimeRow(context.tr.startTime, outOfHours.value),
          buildTimeRow(context.tr.endTime, outOfHours.value),
          buildCheckboxRow(
            context.tr.outOfHours,
            ValueNotifier(outOfHours.value),
          ),
        ],
      ),
    );
  }
}

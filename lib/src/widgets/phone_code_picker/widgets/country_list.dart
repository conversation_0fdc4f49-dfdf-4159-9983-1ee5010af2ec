part of '../phone_code_picker.dart';

class _CountryList extends HookConsumerWidget {
  const _CountryList({required this.onSelected, required this.countries, this.selectedCountry});

  final void Function(Country) onSelected;
  final List<Country> countries;
  final Country? selectedCountry;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      constraints: const BoxConstraints(maxHeight: 300),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        boxShadow: const [
          BoxShadow(
            color: Color(0x66210A38),
            blurRadius: 40,
            offset: Offset(8, 20),
            spreadRadius: -4,
          ),
        ],
      ),
      child: ListView.separated(
        itemCount: countries.length,
        padding: const EdgeInsets.all(12),
        separatorBuilder: (_, __) => const Gap(4),
        itemBuilder: (_, index) {
          final country = countries[index];
          final isSelected = country == selectedCountry;
          return HoverBuilder(
            onTap: () => onSelected(country),
            builder: (isHovering, _) {
              return AnimatedContainer(
                decoration: BoxDecoration(
                  color: isSelected
                      ? Colors.blue[100]
                      : isHovering
                          ? Colors.blue[50]
                          : Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
                duration: const Duration(milliseconds: 300),
                child: Row(
                  spacing: 12,
                  children: <Widget>[
                    country.getFlagWidget(),
                    Text(
                      '+${country.phoneCode}',
                      style: ATextStyle.mediumRegular,
                    ),
                    Expanded(
                      child: Text(
                        CountryLocalizations.of(context)
                                ?.countryName(countryCode: country.countryCode)
                                ?.replaceAll(RegExp(r'\s+'), ' ') ??
                            country.name,
                        style: ATextStyle.mediumRegular,
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}

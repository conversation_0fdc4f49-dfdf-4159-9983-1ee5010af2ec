import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/iterable_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/hover_builder.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';

part 'widgets/country_list.dart';

class PhoneCodePicker extends StatefulHookConsumerWidget {
  const PhoneCodePicker({
    required this.phoneNumberInfo, 
    this.headerText,
    this.headerTextStyle, 
    this.onPhoneNumberChanged,
    super.key,
  });

  final ({String countryCode, String phoneNumber}) phoneNumberInfo;
  final String? headerText;
  final TextStyle? headerTextStyle;
  final void Function(({String countryCode, String phoneNumber}))? onPhoneNumberChanged;

  @override
  ConsumerState<PhoneCodePicker> createState() => _PhoneCodePickerState();
}

class _PhoneCodePickerState extends ConsumerState<PhoneCodePicker> {
  final MenuController _controller = MenuController();

  @override
  Widget build(BuildContext context) {
    final style = ref.watch(appStyleProvider);
    final countryList = useMemoized(() => ref.read(countryServiceProvider).countries);
    final phoneController = useTextEditingController(text: widget.phoneNumberInfo.phoneNumber);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 8,
      children: [
        if (widget.headerText != null)
          Text(
            widget.headerText!,
            style: widget.headerTextStyle ?? style.text.bodyXXSmall,
          ),
        RawMenuAnchor(
          controller: _controller,
          overlayBuilder: (_, info) {
            return Positioned(
              top: info.anchorRect.bottom + 6,
              left: info.anchorRect.left,
              child: TapRegion(
                onTapOutside: (_) => _controller.close(),
                groupId: info.tapRegionGroupId,
                child: SizedBox(
                  width: info.anchorRect.size.width,
                  child: _CountryList(
                    countries: countryList,
                    onSelected: (country) {
                      _controller.close();
                      widget.onPhoneNumberChanged?.call((
                        countryCode: country.phoneCode,
                        phoneNumber: phoneController.text,
                      ));
                    },
                    selectedCountry:
                        countryList.firstWhereOrNull((e) => e.phoneCode == widget.phoneNumberInfo.countryCode),
                  ),
                ),
              ),
            );
          },
          child: Row(
            spacing: 8,
            children: [
              InkWell(
                onTap: _controller.open,
                child: Container(
                  decoration: CommonDecorations.containerDecoration(
                    borderColor: Colors.grey.shade400,
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 7,
                  ),
                  child: Text(
                    '+${widget.phoneNumberInfo.countryCode}',
                    style: style.text.bodyXSmall.copyWith(
                      color: AColor.black,
                    ),
                  ),
                ),
              ),
              Expanded(
                child: CustomTextFormField(
                  controller: phoneController,
                  hintText: '555 555 55 55'.hardcoded,
                  keyboardType: TextInputType.phone,
                  textInputAction: TextInputAction.next,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 12,
                  ),
                  onChanged: (value) {
                    widget.onPhoneNumberChanged?.call((
                      countryCode: widget.phoneNumberInfo.countryCode,
                      phoneNumber: value,
                    ));
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

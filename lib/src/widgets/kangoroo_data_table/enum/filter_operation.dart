import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';

enum FilterOperation {
  equal,
  notEqual,
  startsWith,
  contains,
  notContains,
  endsWith,
  isNull,
  isNotNull,
  isEmpty,
  isNotEmpty,
  hasValue,
  hasNoValue,
}

extension FilterOperationX on FilterOperation {
  String label() {
    switch (this) {
      case FilterOperation.equal:
        return 'Eşit'.hardcoded;
      case FilterOperation.notEqual:
        return 'Eşit değil'.hardcoded;
      case FilterOperation.startsWith:
        return 'İle başlar'.hardcoded;
      case FilterOperation.contains:
        return 'İçeriyor'.hardcoded;
      case FilterOperation.notContains:
        return 'İçermiyor'.hardcoded;
      case FilterOperation.endsWith:
        return 'İle biter'.hardcoded;
      case FilterOperation.isNull:
        return 'Null'.hardcoded;
      case FilterOperation.isNotNull:
        return 'Null değil'.hardcoded;
      case FilterOperation.isEmpty:
        return 'Boş'.hardcoded;
      case FilterOperation.isNotEmpty:
        return '<PERSON><PERSON> değil'.hardcoded;
      case FilterOperation.hasValue:
        return 'Değer içeriyor'.hardcoded;
      case FilterOperation.hasNoValue:
        return 'Değer içermiyor'.hardcoded;
    }
  }
}

enum FilterLogic {
  and,
  or,
}

extension FilterLogicX on FilterLogic {
  String label() {
    switch (this) {
      case FilterLogic.and:
        return 'Ve'.hardcoded;
      case FilterLogic.or:
        return 'Veya'.hardcoded;
    }
  }
}

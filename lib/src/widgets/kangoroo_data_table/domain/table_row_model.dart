import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

part 'table_row_model.freezed.dart';

@optionalTypeArgs
@freezed
abstract class RowDataModel<T> with _$RowDataModel<T> {
  const factory RowDataModel({
    required String columnName,
    T? value,
    Widget Function()? cellBuilder,
  }) = _RowDataModel;

  const RowDataModel._();

  DataGridCell<T> toDataGridCell() {
    return DataGridCell<T>(columnName: columnName, value: value);
  }
}

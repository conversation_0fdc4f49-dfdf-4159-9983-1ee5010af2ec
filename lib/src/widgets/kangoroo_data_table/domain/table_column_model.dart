import 'package:freezed_annotation/freezed_annotation.dart';

part 'table_column_model.freezed.dart';

@freezed
abstract class TableColumnModel with _$TableColumnModel {
  const factory TableColumnModel({
    required String columnName,
    double? width,
    double? minimumWidth,
    @Default(false) bool ignoreWhenExporting,
    @Default(true) bool sortable,
    @Default(true) bool filterable,
  }) = _TableColumnModel;
}

part of '../st_data_table.dart';

class _ExportButtons extends HookConsumerWidget {
  const _ExportButtons({
    required this.dataGridKey,
    required this.columns,
  });

  final List<TableColumnModel> columns;
  final GlobalKey<SfDataGridState> dataGridKey;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    List<String> getFilteredColumnsNames() {
      return columns
          .where((column) => column.ignoreWhenExporting)
          .map((e) => e.columnName)
          .toList();
    }

    void exportToExcel() {
      final workbook = dataGridKey.currentState!.exportToExcelWorkbook(
        excludeColumns: getFilteredColumnsNames(),
      );
      ref
          .read(documentManagerProvider)
          .createExcelFromWorkbook(workbook: workbook);
    }

    void exportToPDF() {
      final pdfGrid = dataGridKey.currentState!.exportToPdfGrid(
        fitAllColumnsInOnePage: true,
        excludeColumns: getFilteredColumnsNames(),
      );
      ref.read(documentManagerProvider).createAndDownloadPDF(grid: pdfGrid);
    }

    Widget buildExportButton({
      required String title,
      required IconData icon,
      required Color color,
      required VoidCallback onPressed,
    }) {
      return InkWell(
        onTap: onPressed,
        child: Container(
          height: 30,
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: AColor.doveGray),
          ),
          child: Row(
            spacing: 4,
            children: [
              Icon(
                icon,
                color: color,
                size: 16,
              ),
              Text(
                title,
                style: ATextStyle.mediumRegular.copyWith(color: color),
              ),
            ],
          ),
        ),
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      spacing: 8,
      children: [
        buildExportButton(
          title: 'Export to Excel'.hardcoded,
          icon: Icons.description,
          color: AColor.doveGray,
          onPressed: exportToExcel,
        ),
        buildExportButton(
          title: 'Export to PDF'.hardcoded,
          icon: Icons.picture_as_pdf,
          color: AColor.doveGray,
          onPressed: exportToPDF,
        ),
      ],
    );
  }
}

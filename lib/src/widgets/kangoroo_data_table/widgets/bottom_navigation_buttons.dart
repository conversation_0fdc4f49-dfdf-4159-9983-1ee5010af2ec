import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

class BottomNavigationButtons extends HookConsumerWidget {
  const BottomNavigationButtons({
    required this.onPrevious,
    required this.onNext,
    required this.currentPage,
    required this.totalPages,
    required this.totalCount,
    super.key,
    this.onFirst,
    this.onLast,
  });

  final VoidCallback? onFirst;
  final VoidCallback onPrevious;
  final VoidCallback onNext;
  final VoidCallback? onLast;

  final int currentPage;
  final int totalPages;
  final int totalCount;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: Row(
        children: [
          Row(
            spacing: 2,
            children: [
              InkWell(
                onTap: onFirst,
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: (onFirst == null) ? AColor.grey.withAlpha(100) : AColor.grey,
                    ),
                  ),
                  child: Icon(
                    Icons.skip_previous_rounded,
                    color: (onFirst == null) ? AColor.grey.withAlpha(100) : AColor.grey,
                  ),
                ),
              ),
              InkWell(
                onTap: (currentPage > 1) ? onPrevious : null,
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: (currentPage > 1) ? AColor.grey : AColor.grey.withAlpha(100),
                    ),
                  ),
                  child: Icon(
                    Icons.arrow_left_rounded,
                    color: (currentPage > 1) ? AColor.grey : AColor.grey.withAlpha(100),
                  ),
                ),
              ),
              InkWell(
                onTap: (currentPage < totalPages) ? onNext : null,
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: (currentPage < totalPages) ? AColor.grey : AColor.grey.withAlpha(100),
                    ),
                  ),
                  child: Icon(
                    Icons.arrow_right_rounded,
                    color: (currentPage < totalPages) ? AColor.grey : AColor.grey.withAlpha(100),
                  ),
                ),
              ),
              InkWell(
                onTap: onLast,
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: (onLast == null) ? AColor.grey.withAlpha(100) : AColor.grey,
                    ),
                  ),
                  child: Icon(
                    Icons.skip_next_rounded,
                    color: (onLast == null) ? AColor.grey.withAlpha(100) : AColor.grey,
                  ),
                ),
              ),
            ],
          ),
          const Spacer(),
          Expanded(
            child: TextWidget(
              '${context.tr.page} $currentPage / $totalPages '
              '($totalCount ${context.tr.record})',
              style: ATextStyle.small.copyWith(color: AColor.grey),
              maxLines: 1,
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }
}

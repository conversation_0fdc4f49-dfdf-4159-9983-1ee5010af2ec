part of '../st_data_table.dart';

class _LoadMoreLoadingIndicator extends HookWidget {
  const _LoadMoreLoadingIndicator({required this.onLoadNext});

  final Future<void> Function() onLoadNext;

  @override
  Widget build(BuildContext context) {
    final isLoading = useState(true);

    useEffect(() {
      Future.microtask(() async {
        await onLoadNext();
        isLoading.value = false;
      });
      return null;
    }, []);

    if (isLoading.value) {
      return Container(
          height: 60,
          width: double.infinity,
          decoration: const BoxDecoration(
              color: Colors.white,
              border: BorderDirectional(top: BorderSide(width: 1, color: Color.fromRGBO(0, 0, 0, 0.26)))),
          alignment: Alignment.center,
          child: CircularProgressIndicator(),);
    }

    return const SizedBox.shrink();
  }
}

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/shared/constants/app_path.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/image/asset_image.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

class AppScaffold extends ConsumerWidget {
  AppScaffold({
    required this.body,
    super.key,
    this.title,
    this.customAppBar,
    this.denyAppBar = false,
    this.denyLeading = false,
    this.denyActions = false,
    this.titleColor = AColor.white,
    this.backgroundColor = AColor.primaryColor,
    this.leading,
    this.customBackPressed,
    this.denyFooter = false,
  });
  String? title;
  final Widget body;
  final AppBar? customAppBar;
  final bool denyAppBar;
  final bool denyLeading;
  final bool denyActions;
  Color titleColor;
  Color backgroundColor;
  Widget? leading;
  final VoidCallback? customBackPressed;
  final bool denyFooter;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final scaffoldKey = GlobalKey<ScaffoldState>();
    return SelectionArea(
      child: Scaffold(
        appBar: denyAppBar
            ? null
            : customAppBar ??
                AppBar(
                  leadingWidth: 200,
                  leading: denyLeading
                      ? null
                      : const Padding(
                          padding: EdgeInsets.all(8),
                          child: AImage(
                            imgPath: APath.appLogoWhite,
                            fit: BoxFit.contain,
                          ),
                        ),
                  title: title != null && title!.isNotEmpty
                      ? TextWidget(
                          title!,
                          textAlign: TextAlign.center,
                          style: ATextStyle.semiLarge.copyWith(
                            color: titleColor,
                          ),
                        )
                      : null,
                  backgroundColor: backgroundColor,
                  iconTheme: const IconThemeData(
                    color: AColor.white,
                  ),
                ),
        key: scaffoldKey,
        body: Column(
          children: [
            Expanded(child: body),
            if (!denyFooter)
              Container(
                alignment: Alignment.centerLeft,
                color: AColor.primaryColor,
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: TextWidget(
                    'Akıllı Konum Teknolojileri A.Ş. © 2025'.hardcoded,
                    style: ATextStyle.small.copyWith(
                      color: titleColor,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

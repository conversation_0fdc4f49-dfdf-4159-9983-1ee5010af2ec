/*
 * ---------------------------
 * File : asset_image.dart
 * ---------------------------
 * Author : <PERSON><PERSON><PERSON><PERSON> (nesmin)
 * Email : <EMAIL>
 * ---------------------------
 */

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AImage extends StatelessWidget {
  const AImage({
    super.key,
    required this.imgPath,
    this.height,
    this.width,
    this.color,
    this.fit = BoxFit.cover,
  });
  final String imgPath;
  final double? height;
  final double? width;
  final BoxFit fit;
  final Color? color;

  @override
  Widget build(BuildContext context) => (imgPath.contains('.svg'))
      ? SvgPicture.asset(
          package: 'smart_team_common',
          imgPath,
          width: width,
          height: height,
          fit: fit,
          colorFilter:
              color == null ? null : ColorFilter.mode(color!, BlendMode.srcIn),
        )
      : Image.asset(
          package: 'smart_team_common',
          imgPath,
          width: width,
          height: height,
          fit: fit,
          color: color,
        );
}

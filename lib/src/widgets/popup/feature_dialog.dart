import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_column_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_row_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/st_data_table.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

void showFeatureDialog(
  BuildContext context,
  String title, {
  List<String>? columns,
  List<List<String>>? data,
  Function? onExportToExcel,
  Function? onExportToPdf,
  List<Widget>? customExportRow,
  bool showExportButtons = true,
}) {
  showDialog(
    context: context,
    builder: (context) => FeatureDialog(
      title: title,
      columns: columns ?? ['Kolon1'.hardcoded, 'Kolon2'.hardcoded],
      data: data ??
          [
            ['Veri1'.hardcoded, 'Veri2'.hardcoded],
          ],
      showExportButtons: showExportButtons,
    ),
  );
}

class FeatureDialog extends StatelessWidget {
  const FeatureDialog({
    required this.title,
    required this.columns,
    required this.data,
    super.key,
    this.showExportButtons = true,
  });

  final String title;
  final List<String> columns;
  final List<List<String>> data;
  final bool showExportButtons;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      child: Container(
        width: context.width,
        height: context.height,
        decoration: CommonDecorations.containerDecoration(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
              child: Container(
                color: AColor.primaryColor,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextWidget(
                      title,
                      style: ATextStyle.semiSmall.copyWith(color: AColor.white),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, color: AColor.white),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              child: SmartTeamDataTable(
                showExportButtons: showExportButtons,
                columns: columns
                    .map((col) => TableColumnModel(columnName: col))
                    .toList(),
                rowData: data.map((row) {
                  return row.asMap().entries.map((entry) {
                    final colIndex = entry.key;
                    final cellText = entry.value;
                    return RowDataModel<String>(
                      columnName: columns[colIndex],
                      value: cellText,
                      cellBuilder: () =>
                          TextWidget(cellText, style: ATextStyle.small),
                    );
                  }).toList();
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

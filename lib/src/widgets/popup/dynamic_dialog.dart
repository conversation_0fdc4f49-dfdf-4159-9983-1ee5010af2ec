import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/button/dynamic_icon_text_button.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

void showAddDialog(
  BuildContext context, {
  required String title,
  required Widget fields,
  FutureOr<void> Function(BuildContext context)? onSave,
  List<Widget>? actions,
  bool showBottomActions = true,
}) {
  showDialog(
    context: context,
    builder: (context) => DynamicAddDialog(
      title: title,
      fields: fields,
      actions: actions,
      showBottomActions: showBottomActions,
      onSave: onSave != null
          ? () {
              onSave(context);
            }
          : () {
              context.maybePop();
            },
    ),
  );
}

class DynamicAddDialog extends StatelessWidget {
  const DynamicAddDialog({
    required this.title,
    required this.fields,
    required this.onSave,
    super.key,
    this.actions,
    this.width,
    this.height,
    this.showBottomActions = true,
  });

  final String title;
  final Widget fields;
  final FutureOr<void> Function() onSave;
  final List<Widget>? actions;
  final double? width;
  final double? height;
  final bool showBottomActions;

  @override
  Widget build(BuildContext context) {

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
      ),
      child: Container(
        width: width ?? context.width,
        height: height ?? context.height,
        decoration: CommonDecorations.containerDecoration(),
        child: Column(
          spacing: 16,
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
              child: Container(
                color: AColor.primaryColor,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextWidget(
                      title,
                      style: ATextStyle.medium.copyWith(
                        color: AColor.white,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(
                        Icons.close,
                        color: AColor.white,
                      ),
                      onPressed: context.maybePop,
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              child: fields,
            ),
            if (showBottomActions)
              Container(
                decoration: const BoxDecoration(
                  color: AColor.primaryColor,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(4),
                  bottomRight: Radius.circular(4),
                ),
              ),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  spacing: 8,
                  children: actions ??
                      [
                        DynamicIconTextButton(
                          title: context.tr.cancel,
                          prefixIcon: Icons.cancel_outlined,
                          onTap: () => context.maybePop(),
                          height: 40,
                        ),
                        DynamicIconTextButton(
                          title: context.tr.save,
                          prefixIcon: Icons.save_alt_outlined,
                          onTap: onSave,
                          height: 40,
                        ),
                      ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';

class DeleteEntryDialog extends HookConsumerWidget {
  const DeleteEntryDialog({super.key});

  static Future<bool?> show(BuildContext context) async {
    return showDialog<bool?>(
      context: context,
      builder: (_) => const DeleteEntryDialog(),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final style = ref.watch(appStyleProvider);
    return Center(
      child: Container(
        width: 300,
        padding: EdgeInsets.all(style.insets.sm),
        decoration: CommonDecorations.containerDecoration(),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          spacing: 24,
          children: [
            Icon(
              Icons.warning,
              color: style.colors.primaryColor,
              size: 60 * style.scale,
            ),
            Text(
              '<PERSON><PERSON><PERSON> silmek istediğinize emin misiniz?',
              style: style.text.bodyBold,
              textAlign: TextAlign.center,
            ),
            Row(
              spacing: 12,
              children: [
                Expanded(
                  child: LoadingElevatedButton(
                    onPressed: context.maybePop,
                    height: 30,
                    text: 'İptal'.hardcoded,
                    textColor: style.colors.primaryColor,
                    backgroundColor: Colors.white,
                  ),
                ),
                Expanded(
                  child: LoadingElevatedButton(
                    height: 30,
                    onPressed: () => context.maybePop(true),
                    text: 'Sil'.hardcoded,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

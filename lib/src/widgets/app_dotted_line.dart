import 'package:flutter/material.dart';
import 'package:smart_team_web/src/theme/colors.dart';

/// A widget that draws a dotted line with customizable properties
///
/// [height] - Height of the line, defaults to 2
/// [dashWidth] - Width of each dash, defaults to 10
/// [color] - Color of the line, defaults to AppColors.darkGreen
/// [direction] - Direction of the line (horizontal/vertical), defaults to horizontal
/// [mainAxisAlignment] - Alignment of dashes, defaults to spaceAround
class AppDottedLine extends StatelessWidget {
  const AppDottedLine({
    super.key,
    this.height = 1,
    this.dashWidth = 2,
    this.color = AColor.loginBorderColor,
    this.direction = Axis.horizontal,
    this.mainAxisAlignment = MainAxisAlignment.spaceAround,
  });
  final double height;
  final double dashWidth;
  final Color color;
  final Axis direction;
  final MainAxisAlignment mainAxisAlignment;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final boxWidth = constraints.constrainWidth();
        final dashHeight = height;
        final dashCount = (boxWidth / (2 * dashWidth)).floor();
        return Flex(
          mainAxisAlignment: mainAxisAlignment,
          direction: direction,
          children: List.generate(dashCount, (_) {
            return SizedBox(
              width: dashWidth,
              height: dashHeight,
              child: DecoratedBox(
                decoration: BoxDecoration(color: color),
              ),
            );
          }),
        );
      },
    );
  }
}

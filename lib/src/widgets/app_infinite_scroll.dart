import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class AppInfiniteScroll extends HookWidget {
  const AppInfiniteScroll({
    required this.child,
    required this.scrollController,
    this.onLoad,
    this.onRefresh,
    this.loadTriggerOffset,
    super.key,
  });

  final Widget child;
  final ScrollController scrollController;
  final Future<void> Function()? onLoad;
  final Future<void> Function()? onRefresh;
  final int? loadTriggerOffset;

  @override
  Widget build(BuildContext context) {
    final isLoading = useState(false);

    Future<void> listenScroll() async {
      if (scrollController.position.maxScrollExtent - scrollController.offset < (loadTriggerOffset ?? 50) &&
          !isLoading.value &&
          onLoad != null) {
        isLoading.value = true;
        await onLoad!();
        if (context.mounted) {
          isLoading.value = false;
        }
      }
    }

    useEffect(
      () {
        scrollController.addListener(listenScroll);
        return () => scrollController.removeListener(listenScroll);
      },
      [],
    );

    if (onRefresh == null) {
      return child;
    }

    return RefreshIndicator(
      onRefresh: onRefresh!,
      child: child,
    );
  }
}

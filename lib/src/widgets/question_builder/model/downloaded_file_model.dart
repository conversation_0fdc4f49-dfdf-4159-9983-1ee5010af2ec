import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'downloaded_file_model.freezed.dart';
part 'downloaded_file_model.g.dart';

@freezed
abstract class DownloadedFileModel with _$DownloadedFileModel {
  const factory DownloadedFileModel({
    required String name,
    String? url,
  }) = _DownloadedFileModel;

  const DownloadedFileModel._();

  factory DownloadedFileModel.fromJson(Map<String, dynamic> json) =>
      _$DownloadedFileModelFromJson(json);

  String? get nameInBucket {
    if (url == null) return null;
    return url!.split('/').last;
  }
}

extension DownloadedFileModelListExtension on List<DownloadedFileModel>? {
  String? toJsonString() {
    if (this == null) return null;
    return jsonEncode(this!.map((file) => file.toJson()).toList());
  }
}

extension DownloadedFileModelStringExtension on String? {
  List<DownloadedFileModel> toDownloadedFileModelList() {
    if (this == null) return [];
    final jsonList = jsonDecode(this!) as List<dynamic>;
    return jsonList
        .map((json) => DownloadedFileModel.fromJson(json as Map<String, dynamic>))
        .toList();
  }
}

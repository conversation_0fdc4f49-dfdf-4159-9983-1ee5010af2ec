part of '../../question_builder.dart';

class _FileItem extends StatelessWidget {
  const _FileItem({
    required this.downloadedFile,
    required this.onDelete,
    super.key,
  });

  final DownloadedFileModel downloadedFile;
  final void Function(DownloadedFileModel downloadedFile) onDelete;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 36,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        border: Border.all(color: AColor.dustyGray),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(downloadedFile.name, style: ATextStyle.text14),
          const VerticalDivider(color: AColor.dustyGray),
          InkWell(
            onTap: () => onDelete(downloadedFile),
            child: const Padding(
              padding: EdgeInsets.all(4),
              child: Icon(Icons.close),
            ),
          ),
        ],
      ),
    );
  }
}

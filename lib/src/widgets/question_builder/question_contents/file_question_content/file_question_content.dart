part of '../../question_builder.dart';

class _FileQuestionContent extends HookConsumerWidget {
  const _FileQuestionContent({
    required this.field,
    required this.isEditModeOn,
    required this.fieldIndex,
    this.answer,
  });

  final FormTemplateField field;
  final bool isEditModeOn;
  final int fieldIndex;
  final FormSubmissionAnswer? answer;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (isEditModeOn) {
      return Row(
        spacing: 16,
        children: [
          Expanded(
            child: Align(
              alignment: Alignment.centerLeft,
              child: _BaseEditModeTextQuestionContent(
                fieldType: field.fieldType,
                placeholderText: field.getEmptyText(),
              ),
            ),
          ),
          Expanded(
            child: Align(
              alignment: Alignment.centerLeft,
              child: ANewDropDown<ValidationRuleEnum?>(
                header: 'Maximum number of files',
                width: 100,
                placeholder: '',
                // Add null to the list to allow no validation rule(1 max file)
                itemList: [null, ...field.fieldType.validationRuleOptions],
                itemBuilder: (value) {
                  final option = switch (value) {
                    null => '1',
                    ValidationRuleEnum.allowFiveItems => '5',
                    ValidationRuleEnum.allowTenItems => '10',
                    _ => '',
                  };
                  return Text(option, style: ATextStyle.text14);
                },
                selectedItems: field.validationRule != null
                    ? [field.validationRule]
                    : [null],
                onSelected: (value) {
                  ref
                      .read(
                        editFormAsyncNotifierProvider(field.formTemplate.id!)
                            .notifier,
                      )
                      .updateValidationRule(fieldIndex, value);
                },
              ),
            ),
          ),
        ],
      );
    }

    final downloadedFiles =
        answer?.answerValue.toDownloadedFileModelList() ?? [];

    return FilePickerFormField<DownloadedFileModel>(
      selectedFiles: downloadedFiles,
      initialValue: downloadedFiles,
      header: field.getEmptyText(),
      allowExtensions: const ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'png'],
      autovalidateMode: AutovalidateMode.onUserInteraction,
      allowMultiple: field.validationRule != null,
      validator: (value) {
        if (field.isRequired != null &&
            field.isRequired! &&
            (value == null || value.isEmpty)) {
          return 'This field is required'.hardcoded;
        }
        if (field.validationRule != null) {
          final maxFileCount =
              field.validationRule! == ValidationRuleEnum.allowFiveItems
                  ? 5
                  : 10;
          if (value!.length > maxFileCount) {
            return 'You can only upload up to $maxFileCount files'.hardcoded;
          }
        }
        return null;
      },
      uploadedFileListView: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: List.generate(
          downloadedFiles.length,
          (index) {
            final downloadedFile = downloadedFiles[index];
            return _FileItem(
              key: ValueKey(downloadedFile.name),
              downloadedFile: downloadedFile,
              onDelete: (downloadedFile) {
                unawaited(
                  ref.read(mediaManagerProvider).deleteFile(
                        bucket: StorageBucket.formDocuments,
                        fileName: downloadedFile.nameInBucket!,
                        additionalPath: field.formTemplate.id,
                      ),
                );
                final newList = [...downloadedFiles]..remove(downloadedFile);
                ref
                    .read(
                  editFormAsyncNotifierProvider(field.formTemplate.id!)
                      .notifier,
                )
                    .setAnswerForField(field, [newList.toJsonString()!]);
              },
            );
          },
        ),
      ),
      onSelected: (files) async {
        if (files.isNotEmpty && context.mounted) {
          final uploadedFiles = await AppDialog.show<List<DownloadedFileModel>>(
            context: context,
            showHeader: false,
            width: 400,
            barrierDismissible: false,
            child: _FileUploadDialog(
              files: files,
              formTemplateId: field.formTemplate.id!,
            ),
          );
          if (uploadedFiles != null) {
            ref
                .read(editFormAsyncNotifierProvider(field.formTemplate.id!)
                    .notifier,
                    )
                .setAnswerForField(field, [uploadedFiles.toJsonString()!]);
          }
        }
      },
    );
  }
}

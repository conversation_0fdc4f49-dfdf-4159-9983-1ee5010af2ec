part of '../../question_builder.dart';

class _FileUploadDialog extends HookWidget {
  const _FileUploadDialog({required this.files, required this.formTemplateId});

  final List<XFile> files;
  final String formTemplateId;

  @override
  Widget build(BuildContext context) {
    final uploadedFiles = useState<List<DownloadedFileModel>>(
      List.generate(files.length,
          (index) => DownloadedFileModel(name: files[index].sanitizedFileName),
          ),
    );
    final allFilesUploaded = uploadedFiles.value.every((e) => e.url != null);
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(maxHeight: 500),
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 12,
        children: [
          Text(
            'Dosyalar yüklenirken lütfen bekleyiniz...'.hardcoded,
            style: ATextStyle.text14SemiBold,
          ),
          const Divider(),
          Flexible(
            child: SingleChildScrollView(
              child: Column(
                spacing: 8,
                children: List.generate(
                  files.length,
                  (index) {
                    final file = files[index];
                    return _FileUploadItem(
                      key: ValueKey(file.path),
                      file: file,
                      index: index,
                      formTemplateId: formTemplateId,
                      onUploaded: (index, downloadUrl) {
                        final newFile = uploadedFiles.value[index]
                            .copyWith(url: downloadUrl);
                        uploadedFiles.value = [...uploadedFiles.value]
                          ..replaceAt(index, newFile);
                      },
                    );
                  },
                ),
              ),
            ),
          ),
          if (allFilesUploaded)
            Center(
              child: LoadingElevatedButton(
                text: 'Onayla'.hardcoded,
                height: 35,
                onPressed: () async {
                  await context.maybePop(uploadedFiles.value);
                },
              ),
            ),
        ],
      ),
    );
  }
}

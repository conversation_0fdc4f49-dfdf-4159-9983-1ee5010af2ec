part of '../../question_builder.dart';

class _FileUploadItem extends HookConsumerWidget {
  const _FileUploadItem({
    required this.file,
    required this.index,
    required this.formTemplateId,
    required this.onUploaded,
    super.key,
  });

  final XFile file;
  final int index;
  final String formTemplateId;
  final void Function(int index, String? downloadUrl) onUploaded;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isUpLoading = useState<bool?>(true);
    useEffect(
      () {
        Future.microtask(() async {
          final downloadUrl = await ref.read(mediaManagerProvider).uploadFile(
                bucket: StorageBucket.formDocuments,
                file: file,
                additionalPath: formTemplateId,
              );
          isUpLoading.value = downloadUrl != null ? false : null;
          onUploaded(index, downloadUrl);
        });
        return null;
      },
      [],
    );

    Widget buildUploadStatusIcon() {
      if (isUpLoading.value == null) {
        return HeartBeat(
          child: const Icon(Icons.close, color: AColor.red),
        );
      }
      if (isUpLoading.value!) {
        return const CircularProgressIndicator(strokeWidth: 1);
      }
      return HeartBeat(
        child: const Icon(
          Icons.done,
          color: AColor.green,
        ),
      );
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          file.name,
          style: ATextStyle.text14,
        ),
        const Expanded(child: AppDottedLine()),
        const Gap(4),
        SizedBox(
          width: 20,
          height: 20,
          child: buildUploadStatusIcon(),
        ),
      ],
    );
  }
}

part of '../question_builder.dart';

class _CheckboxQuestionContent extends HookConsumerWidget {
  const _CheckboxQuestionContent({
    required this.field,
    required this.isEditModeOn,
    required this.options,
    this.answer,
  });

  final FormTemplateField field;
  final bool isEditModeOn;
  final List<FormFieldOption> options;
  final FormSubmissionAnswer? answer;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (isEditModeOn) {
      return _BaseEditModeOptionsQuestionContent(
        field: field,
        options: options,
      );
    }
    final optionList = options.map((e) => e.optionValue).toList();
    final answerList = answer.answerList;
   
    return FractionallySizedBox(
      widthFactor: .4,
      child: CheckboxGroupFormField(
        initialValue: answerList,
        items: optionList,
        onSelected: (value) {
          ref.read(editFormAsyncNotifierProvider(field.formTemplate.id!).notifier).setAnswerForField(field, value);
        },
        validator: (value) {
          if (field.isRequired != null && field.isRequired! && (value == null || value.isEmpty)) {
            return context.tr.fieldRequired;
          }
          return null;
        },
      ),
    );
  }
}

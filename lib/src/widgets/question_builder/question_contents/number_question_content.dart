part of '../question_builder.dart';

class _NumberQuestionContent extends HookConsumerWidget {
  const _NumberQuestionContent({
    required this.field,
    required this.isEditModeOn,
    required this.fieldIndex,
    this.answer,
  });

  final FormTemplateField field;
  final bool isEditModeOn;
  final int fieldIndex;
  final FormSubmissionAnswer? answer;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (isEditModeOn) {
      return Row(
        spacing: 16,
        children: [
          Expanded(
            child: _BaseEditModeTextQuestionContent(fieldType: field.fieldType, placeholderText: field.getEmptyText()),
          ),
          Expanded(
            child: ANewDropDown<ValidationRuleEnum>(
              placeholder: 'Select validation type',
              itemList: field.fieldType.validationRuleOptions,
              itemBuilder: (value) {
                return Text(value?.name ?? '', style: ATextStyle.text14);
              },
              selectedItems:
                  field.validationRule != null ? [field.validationRule!] : [],
              onSelected: (value) {
                ref
                    .read(
                      editFormAsyncNotifierProvider(field.formTemplate.id!)
                          .notifier,
                    )
                    .updateValidationRule(fieldIndex, value);
              },
            ),
          ),
        ],
      );
    }
    final answerTextEditingController = useTextEditingController(text: answer?.answerValue ?? '');
    return FractionallySizedBox(
          widthFactor: .5,
          child: CustomTextFormField(
            controller: answerTextEditingController,
            style: ATextStyle.text14,
            enabled: !isEditModeOn,
            maxLines: field.fieldType == FormFieldTypeEnum.textarea ? 3 : 1,
            hintText: field.validationRule.placeholderText,
            inputFormatters: [FilteringTextInputFormatter.allow(RegExp('[0-9.,-]')),],
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            onChanged: (value) {
              ref.read(editFormAsyncNotifierProvider(field.formTemplate.id!).notifier).setAnswerForField(field, [value]);
            },
            validator: (value) {
              if (field.isRequired != null && field.isRequired! && (value == null || value.isEmpty)) {
                return context.tr.fieldRequired;
              }
              if (value == null || value.isEmpty) {
                return null;
              }
              if (field.validationRule != null && !field.validationRule!.regexPattern.hasMatch(value)) {
                return 'Invalid number format'.hardcoded;
              }
              return null;
            },
          ),
        );
  }
}

part of '../question_builder.dart';

class _DateQuestionContent extends HookConsumerWidget {
  const _DateQuestionContent({
    required this.field,
    required this.isEditModeOn,
    this.answer,
  });

  final FormTemplateField field;
  final bool isEditModeOn;
  final FormSubmissionAnswer? answer;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (isEditModeOn) {
      return _BaseEditModeTextQuestionContent(fieldType: field.fieldType, placeholderText: field.getEmptyText());
    }
  
    final mode = field.fieldType == FormFieldTypeEnum.date ? DateTimePickerMode.date : DateTimePickerMode.time;
    final headerText = field.fieldType == FormFieldTypeEnum.date ? '<PERSON><PERSON>h seçiniz'.hardcoded : 'Saat seçiniz'.hardcoded;

    return FractionallySizedBox(
      widthFactor: .2,
      child: DateTimePickerFormField(
        mode: mode,
        header: headerText,
        selectedDate: answer?.answerValue != null
            ? DateTime.parse(answer!.answerValue!)
            : null,
        onSelected: (date) {
          ref
              .read(
                editFormAsyncNotifierProvider(field.formTemplate.id!).notifier,
              )
              .setAnswerForField(field, [date.toIso8601String()]);
        },
        validator: (value) {
          if (field.isRequired != null && field.isRequired! && value == null) {
            return context.tr.fieldRequired;
          }
          return null;
        },
      ),
    );
  }
}

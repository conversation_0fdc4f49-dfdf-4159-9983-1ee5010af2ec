part of '../question_builder.dart';

class _TextQuestionContent extends HookConsumerWidget {
  const _TextQuestionContent({
    required this.field,
    required this.isEditModeOn,
    this.answer,
  });

  final FormTemplateField field;
  final FormSubmissionAnswer? answer;
  final bool isEditModeOn;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (isEditModeOn) {
      return _BaseEditModeTextQuestionContent(fieldType: field.fieldType, placeholderText: field.getEmptyText());
    }
    final answerTextEditingController = useTextEditingController(text: answer?.answerValue ?? '');
   
    return FractionallySizedBox(
      widthFactor: .5,
      child: CustomTextFormField(
        controller: answerTextEditingController,
        style: ATextStyle.text14,
        enabled: !isEditModeOn,
        maxLines: field.fieldType == FormFieldTypeEnum.textarea ? 3 : 1,
        hintText: 'Type your answer here'.hardcoded,
        onChanged: (value) {
          ref.read(editFormAsyncNotifierProvider(field.formTemplate.id!).notifier).setAnswerForField(field, [value]);
        },
        validator: (value) {
          if (field.isRequired != null && field.isRequired! && (value == null || value.isEmpty)) {
            return context.tr.fieldRequired;
          }
          return null;
        },
      ),
    );
  }
}

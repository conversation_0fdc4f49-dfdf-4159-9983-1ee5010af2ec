part of '../question_builder.dart';

class _NoQuestions extends StatelessWidget {
  const _NoQuestions({
    required this.isEditModeOn,
  });

  final bool isEditModeOn;

  @override
  Widget build(BuildContext context) {

    String getText() {
      if (isEditModeOn) {
        return 'Bu formda henüz soru bulunmamaktadır.\nEklemek için sağ üstteki + butonuna tıklayınız.';
      }
      return 'Bu formda henüz soru bulunmamaktadır.';
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(
          Icons.info_outline_rounded,
          size: 50,
          color: AColor.primaryColor,
        ),
        Text(
          getText().hardcoded,
          style: ATextStyle.text24,
        ),
      ],
    );
  }
}

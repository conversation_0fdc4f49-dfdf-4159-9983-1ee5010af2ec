part of '../question_builder.dart';

class _BaseEditModeTextQuestionContent extends StatelessWidget {
  const _BaseEditModeTextQuestionContent({
    required this.fieldType,
    required this.placeholderText,
  });

  final FormFieldTypeEnum fieldType;
  final String placeholderText;

  @override
  Widget build(BuildContext context) {

    double getBoxWidthFactor() {
     return switch (fieldType) {
      FormFieldTypeEnum.date => .2,
      FormFieldTypeEnum.time => .2,
      FormFieldTypeEnum.number => 1,
      FormFieldTypeEnum.file => .7,
      _ => .5,
    };
  }

    return FractionallySizedBox(
      widthFactor: getBoxWidthFactor(),
      child: SizedBox(
        width: double.infinity,
        height: 40,
        child: Stack(
          children: [
            const Align(
              alignment: Alignment.bottomCenter,
              child: AppDottedLine(),
            ),
            Row(
              children: [
                Expanded(
                  child: Text(
                    placeholderText.hardcoded,
                    style: ATextStyle.text14
                        .copyWith(color: AColor.loginBorderColor),
                  ),
                ),
                Icon(
                  fieldType.icon,
                  color: AColor.loginBorderColor,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

part of '../question_builder.dart';

class _BaseEditModeOptionsQuestionContent extends HookConsumerWidget {
  const _BaseEditModeOptionsQuestionContent({
    required this.field,
    required this.options,
  });

  final FormTemplateField field;
  final List<FormFieldOption> options;

  @override
  Widget build(BuildContext context, WidgetRef ref) {


    Widget buildAddButton() {
      return InkWell(
        onTap: () {
          ref.read(editFormAsyncNotifierProvider(field.formTemplate.id!).notifier).addOptionToField(field);
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          spacing: 4,
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: AColor.primaryColor),
              ),
              child: const Center(
                child: Icon(
                  Icons.add,
                  color: AColor.primaryColor,
                  size: 16,
                ),
              ),
            ),
            Text(
              'Add option'.hardcoded,
              style: ATextStyle.text14.copyWith(color: AColor.primaryColor),
            ),
          ],
        ),
      );
    }

    return FractionallySizedBox(
      widthFactor: .8,
      child: Column(
        spacing: 12,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          ...List.generate(
            options.length,
            (index) {
              final option = options[index];
              return _EditModeOptionItem(
                key: ValueKey(option.id),
                option: option,
                index: index,
                field: field,
              );
            },
          ),
          buildAddButton(),
        ],
      ),
    );
  }
}

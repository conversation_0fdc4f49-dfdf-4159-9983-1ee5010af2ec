part of '../question_builder.dart';

class _BaseQuestionCard extends HookWidget {
  const _BaseQuestionCard({
    required this.child,
    this.isSelected = false,
    this.indexForDrag,
  });

  final Widget child;
  final bool isSelected;
  final int? indexForDrag;

  @override
  Widget build(BuildContext context) {
    return HoverBuilder(
      builder: (isHovering, _) => Container(
        width: double.infinity,
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          color: AColor.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: const Color.fromRGBO(218, 220, 224, 1),
          ),
        ),
        child: Stack(
          children: [
            if (indexForDrag != null && isHovering)
              Align(
                alignment: Alignment.topCenter,
                child: MouseRegion(
                  cursor: SystemMouseCursors.move,
                  child: ReorderableDragStartListener(
                  index: indexForDrag!,
                  child: const RotatedBox(
                    quarterTurns: 1,
                    child: Icon(
                      Icons.drag_indicator,
                      color: AColor.loginBorderColor,
                    ),
                  ),
                ),
                ),
              ),
            if (isSelected)
              Positioned(
                top: 0,
                bottom: 0,
                child: Container(
                  width: 10,
                  color: AColor.primaryColor,
                ),
              ),
            Padding(
              padding: const EdgeInsets.all(24),
              child: child,
            ),
          ],
        ),
      ),
    );
  }
}

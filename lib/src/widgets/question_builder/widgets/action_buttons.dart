part of '../question_builder.dart';

class _ActionButtons extends HookConsumerWidget {
  const _ActionButtons({
    required this.formTemplateId,
    required this.isEditModeOn,
    this.onBackPressed,
    this.onPreviewPressed,
    this.onSubmitPressed,
  });

  final String formTemplateId;
  final bool isEditModeOn;
  final VoidCallback? onBackPressed;
  final VoidCallback? onPreviewPressed;
  final VoidCallback? onSubmitPressed;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      spacing: 12,
      children: [
        if (onBackPressed != null)
          _BaseQuestionBuilderButton(
            onPressed: onBackPressed!,
            backgroundColor: AColor.black,
            tooltip: '<PERSON>eri'.hardcoded,
            child: const Icon(
              Icons.arrow_back_ios,
              color: AColor.white,
            ),
          ),
        if (onPreviewPressed != null)
          _BaseQuestionBuilderButton(
            onPressed: onPreviewPressed!,
            backgroundColor: AColor.blue,
            tooltip: 'Önizleme'.hardcoded,
            child: const Icon(
              Icons.visibility_outlined,
              color: AColor.white,
            ),
          ),
        if (onSubmitPressed != null)
        _BaseQuestionBuilderButton(
          onPressed: onSubmitPressed!,
          backgroundColor: AColor.green,
          tooltip: 'Gönder'.hardcoded,
          child: const Icon(
            Icons.done,
            color: AColor.white,
          ),
        ),
        if (isEditModeOn)
          _BaseQuestionBuilderButton(
            onPressed: ref
                .read(
                  editFormAsyncNotifierProvider(formTemplateId).notifier,
                )
                .addField,
            tooltip: 'Soru Ekle'.hardcoded,
            backgroundColor: AColor.primaryColor,
            child: const Icon(
              Icons.add,
              color: AColor.white,
            ),
          ),
      ],
    );
  }
}

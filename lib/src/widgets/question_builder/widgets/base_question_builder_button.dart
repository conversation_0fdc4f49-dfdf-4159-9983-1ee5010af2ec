part of '../question_builder.dart';

class _BaseQuestionBuilderButton extends HookConsumerWidget {
  const _BaseQuestionBuilderButton({
    required this.onPressed,
    required this.child,
    required this.tooltip,
    this.backgroundColor,
  });

  final VoidCallback onPressed;
  final Color? backgroundColor;
  final Widget child;
  final String tooltip;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isVisible = useState(false);
    return PortalTarget(
      visible: isVisible.value,
      portalFollower: FadeInLeft(
        from: 10,
        duration: const Duration(milliseconds: 300),
        child: Text(
          tooltip,
          style: ATextStyle.text16.copyWith(color: backgroundColor),
        ),
      ),
      anchor: const Aligned(
        follower: Alignment.centerLeft,
        target: Alignment(1.4, 0),
      ),
      child: InkWell(
        onTap: onPressed,
        onHover: (value) {
          isVisible.value = value;
        },
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: backgroundColor ?? AColor.primaryColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: child,
          ),
        ),
      ),
    );
  }
}

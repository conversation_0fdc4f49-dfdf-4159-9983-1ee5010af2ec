part of '../question_builder.dart';

class _BaseQuestionItem extends HookConsumerWidget {
  const _BaseQuestionItem({
    required this.index,
    required this.formState,
    required this.isEditModeOn,
    super.key,
  });

  final int index;
  final FormsState formState;
  final bool isEditModeOn;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final field = formState.fields[index];
    final answerInfo = formState.getAnswerForField(field);
    final templateId = field.formTemplate.id!;
    final questionTextController = useTextEditingController(text: field.label);

    Widget buildContent() {
      return switch (field.fieldType) {
        FormFieldTypeEnum.dropdown => _DropdownQuestionContent(
            field: field,
            isEditModeOn: isEditModeOn,
            options: formState.getOptionsForField(field),
            answer: answerInfo.answer,
          ),
        FormFieldTypeEnum.checkbox => _CheckboxQuestionContent(
            field: field,
            isEditModeOn: isEditModeOn,
            options: formState.getOptionsForField(field),
            answer: answerInfo.answer,
          ),
        FormFieldTypeEnum.radio => _RadioQuestionContent(
            field: field,
            isEditModeOn: isEditModeOn,
            options: formState.getOptionsForField(field),
            answer: answerInfo.answer,
          ),
        FormFieldTypeEnum.number => _NumberQuestionContent(
            field: field,
            fieldIndex: index,
            isEditModeOn: isEditModeOn,
            answer: answerInfo.answer,
          ),
        FormFieldTypeEnum.file => _FileQuestionContent(
            field: field,
            fieldIndex: index,
            isEditModeOn: isEditModeOn,
            answer: answerInfo.answer,
          ),
        FormFieldTypeEnum.date ||
        FormFieldTypeEnum.time =>
          _DateQuestionContent(
            field: field,
            isEditModeOn: isEditModeOn,
            answer: answerInfo.answer,
          ),
        _ => _TextQuestionContent(
            field: field,
            isEditModeOn: isEditModeOn,
            answer: answerInfo.answer,
          ),
      };
    }

    Widget buildBottomSection() {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.only(top: 16),
        decoration: const BoxDecoration(
          border: Border(
            top: BorderSide(color: AColor.loginBorderColor),
          ),
        ),
        child: Row(
          spacing: 4,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            IconButton(
              onPressed: () {
                ref
                    .read(
                      editFormAsyncNotifierProvider(templateId).notifier,
                    )
                    .duplicateField(index);
              },
              icon: const Icon(Icons.content_copy),
            ),
            IconButton(
              onPressed: () {
                ref
                    .read(
                      editFormAsyncNotifierProvider(templateId).notifier,
                    )
                    .deleteField(index);
              },
              icon: const Icon(Icons.delete),
            ),
            const VerticalDivider(
              color: AColor.loginBorderColor,
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              spacing: 4,
              children: [
                Text('Zorunlu'.hardcoded, style: ATextStyle.text14),
                CustomSwitch(
                  value: field.isRequired!,
                  onChanged: (value) {
                    ref
                        .read(
                          editFormAsyncNotifierProvider(templateId).notifier,
                        )
                        .toggleFieldRequired(index);
                  },
                ),
              ],
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.only(top: 12),
      child: _BaseQuestionCard(
        indexForDrag: isEditModeOn ? index : null,
        child: Column(
          spacing: 12,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 12,
              children: [
                Expanded(
                  child: isEditModeOn
                      ? CustomTextFormField(
                          controller: questionTextController,
                          style: ATextStyle.text14,
                          maxLines: null,
                          readOnly: !isEditModeOn,
                          hintText: isEditModeOn ? 'Question'.hardcoded : null,
                          borderColor: isEditModeOn ? null : Colors.transparent,
                          onChanged: (value) {
                            ref
                                .read(
                                  editFormAsyncNotifierProvider(templateId)
                                      .notifier,
                                )
                                .updateFieldLabel(index, value);
                          },
                        )
                      : RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: field.label,
                                style: ATextStyle.text14,
                              ),
                              if (field.isRequired!)
                                TextSpan(
                                  text: ' *',
                                  style: ATextStyle.text14
                                      .copyWith(color: AColor.red),
                                ),
                            ],
                          ),
                        ),
                ),
                if (isEditModeOn)
                  SizedBox(
                    width: 200,
                    child: ANewDropDown<FormFieldTypeEnum>(
                      placeholder: 'Select field type'.hardcoded,
                      itemList: FormFieldTypeEnum.values,
                      selectedItems: [field.fieldType],
                      itemBuilder: (item) {
                        return Row(
                          spacing: 8,
                          children: [
                            Icon(item!.icon),
                            Text(
                              item.name.capitalizeFirst ?? '',
                              style: ATextStyle.text14,
                            ),
                          ],
                        );
                      },
                      onSelected: (value) {
                        ref
                            .read(
                              editFormAsyncNotifierProvider(templateId)
                                  .notifier,
                            )
                            .updateFieldType(index, value);
                      },
                    ),
                  ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: buildContent(),
            ),
            if (isEditModeOn) buildBottomSection(),
          ],
        ),
      ),
    );
  }
}

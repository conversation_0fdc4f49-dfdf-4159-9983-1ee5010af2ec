part of '../question_builder.dart';

class _EditModeOptionItem extends HookConsumerWidget {
  const _EditModeOptionItem({
    required this.field,
    required this.option,
    required this.index,
    super.key,
  });

  final FormTemplateField field;
  final FormFieldOption option;
  final int index;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final optionTextController = useTextEditingController(text: option.optionValue);


    Widget buildPrefix() {
      return switch (field.fieldType) {
        FormFieldTypeEnum.checkbox => const Checkbox(value: false, onChanged: null),
        FormFieldTypeEnum.radio => const Radio(value: false, groupValue: true, onChanged: null),
        _ => Text(
          '${index + 1}.',
          style: ATextStyle.text14,
        ),
      };
    }

    return Row(
      spacing: 4,
      children: [
        buildPrefix(),
        Expanded(
          child: CustomTextFormField(
            controller: optionTextController,
            hintText: 'Option ${index + 1}'.hardcoded,
            onChanged: (value) {
              ref
                  .read(
                    editFormAsyncNotifierProvider(option.field.formTemplate.id!).notifier,
                  )
                  .setValueForOption(option.id!, value);
            },
          ),
        ),
        IconButton(
          onPressed: () {
            ref.read(editFormAsyncNotifierProvider(option.field.formTemplate.id!).notifier).removeOption(option.id!);
          },
          icon: const Icon(
            Icons.close,
            color: AColor.primaryColor,
          ),
        ),
      ],
    );
  }
}

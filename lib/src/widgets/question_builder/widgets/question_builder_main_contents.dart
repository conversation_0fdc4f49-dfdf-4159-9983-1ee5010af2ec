part of '../question_builder.dart';

class _QuestionBuilderMainContents extends HookConsumerWidget {
  const _QuestionBuilderMainContents({
    required this.formState,
    required this.isEditModeOn,
  });

  final FormsState formState;
  final bool isEditModeOn;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final scrollController = useScrollController();
    const contentPadding = EdgeInsets.only(right: 12);

    ref.listen(editFormAsyncNotifierProvider(formState.formTemplate.id!),
        (previous, next) {
      if (previous?.value == null) return;
      // Scroll to bottom when new field is added
      if (next.value != null &&
          next.value!.fields.length > previous!.value!.fields.length) {
        // Post frame callback to let the scrollcontroller update it's max extent
        WidgetsBinding.instance.addPostFrameCallback((_) {
          scrollController.animateTo(
            scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        });
      }
    });
    return CustomScrollView(
      controller: scrollController,
      slivers: [
        SliverPadding(
          padding: contentPadding,
          sliver: SliverToBoxAdapter(
            child: _FormsHeader(formState: formState),
          ),
        ),
        const SliverGap(12),
        if (formState.fields.isEmpty)
          SliverPadding(
            padding: contentPadding,
            sliver: SliverToBoxAdapter(
              child: _NoQuestions(isEditModeOn: isEditModeOn),
            ),
          )
        else
          SliverPadding(
            padding: contentPadding,
            sliver: SliverReorderableList(
              itemCount: formState.fields.length,
              onReorder: ref
                  .read(
                    editFormAsyncNotifierProvider(formState.formTemplate.id!)
                        .notifier,
                  )
                  .reorderField,
              itemBuilder: (context, index) {
                final field = formState.fields
                    .firstWhere((element) => element.displayOrder == index);
                return _BaseQuestionItem(
                  key: ValueKey(field.id),
                  formState: formState,
                  index: index,
                  isEditModeOn: isEditModeOn,
                );
              },
            ),
          ),
      ],
    );
  }
}

part of '../question_builder.dart';

class _FormsHeader extends HookConsumerWidget {
  const _FormsHeader({
    required this.formState,
  });

  final FormsState formState;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userNameList =
        formState.assignedUsers.map((e) => e.user.name).toList().join(', ');

    return _BaseQuestionCard(
      isSelected: true,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 12,
        children: [
          Text(
            formState.formTemplate.title,
            style: ATextStyle.text24ExtraBold,
          ),
          if (formState.formTemplate.description != null)
            Text(
              formState.formTemplate.description!,
              style: ATextStyle.text18,
            ),
          if (userNameList.isNotEmpty)
            RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: '<PERSON><PERSON><PERSON>: '.hardcoded,
                    style: ATextStyle.text18SemiBold,
                  ),
                  TextSpan(
                    text: userNameList,
                    style: ATextStyle.text18,
                  ),
                ],
              ),
            ),
          Container(
            height: 40,
            width: double.infinity,
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(color: AColor.loginBorderColor),
              ),
            ),
            child: Align(
              alignment: Alignment.bottomLeft,
              child: Text(
                '* Indicates a required question'.hardcoded,
                style: ATextStyle.text14.copyWith(color: AColor.red),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

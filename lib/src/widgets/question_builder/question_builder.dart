import 'dart:async';

import 'package:animate_do/animate_do.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:gap/gap.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/media_manager/media_manager.dart';
import 'package:smart_team_web/core/router/auto_router.gr.dart';
import 'package:smart_team_web/src/edit_form/application/edit_form_async_notifier.dart';
import 'package:smart_team_web/src/edit_form/application/forms_state.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/list_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/riverpod_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/shared/managers/loading_overlay_manager/loading_overlay_manager.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/toast/toast_manager.dart';
import 'package:smart_team_web/src/widgets/app_dialog/app_dialog.dart';
import 'package:smart_team_web/src/widgets/app_dotted_line.dart';
import 'package:smart_team_web/src/widgets/base_async_provider_widget.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/date_time_picker/date_time_picker.dart';
import 'package:smart_team_web/src/widgets/dropdown/a_new_dropdown.dart';
import 'package:smart_team_web/src/widgets/form_fields/checkbox_group_formfield.dart';
import 'package:smart_team_web/src/widgets/form_fields/date_time_picker_formfield.dart';
import 'package:smart_team_web/src/widgets/form_fields/dropdown_formfield.dart';
import 'package:smart_team_web/src/widgets/form_fields/file_picker_formfield.dart';
import 'package:smart_team_web/src/widgets/form_fields/radio_group_formfield.dart';
import 'package:smart_team_web/src/widgets/hover_builder.dart';
import 'package:smart_team_web/src/widgets/question_builder/model/downloaded_file_model.dart';
import 'package:smart_team_web/src/widgets/switch/custom_switch.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';

part 'question_contents/checkbox_question_content.dart';
part 'question_contents/dropdown_question_content.dart';
part 'question_contents/file_question_content/file_question_content.dart';
part 'question_contents/file_question_content/file_item.dart';
part 'question_contents/file_question_content/file_upload_dialog.dart';
part 'question_contents/file_question_content/file_upload_item.dart';
part 'question_contents/number_question_content.dart';
part 'question_contents/radio_question_content.dart';
part 'question_contents/text_question_content.dart';
part 'question_contents/date_question_content.dart';

part 'widgets/question_builder_main_contents.dart';
part 'widgets/base_edit_mode_options_question_content.dart';
part 'widgets/base_edit_mode_text_question_content.dart';
part 'widgets/base_question_builder_button.dart';
part 'widgets/base_question_card.dart';
part 'widgets/base_question_item.dart';
part 'widgets/edit_mode_option_item.dart';
part 'widgets/forms_header.dart';
part 'widgets/no_questions.dart';
part 'widgets/action_buttons.dart';

class QuestionBuilder extends StatefulHookConsumerWidget {
  const QuestionBuilder({
    required this.formTemplateId,
    this.isEditModeOn = true,
    this.onBackPressed,
    this.onPreviewPressed,
    this.onlyAllowPublic = false,
    this.showSubmitButton = true,
    super.key,
  });

  final String formTemplateId;
  final bool isEditModeOn;
  final bool onlyAllowPublic;
  final bool showSubmitButton;
  final VoidCallback? onBackPressed;
  final VoidCallback? onPreviewPressed;

  @override
  ConsumerState<QuestionBuilder> createState() => _QuestionBuilderState();
}

class _QuestionBuilderState extends ConsumerState<QuestionBuilder> {
  final _formKey = GlobalKey<FormState>(debugLabel: 'QuestionBuilder');
  late final LoadingOverlayManager loadingOverlayManager;

  @override
  void initState() {
    super.initState();
    loadingOverlayManager = ref.read(loadingOverlayManagerProvider.notifier);
  }

  @override
  Widget build(BuildContext context) {
    final editFormAsyncNotifier =
        ref.watch(editFormAsyncNotifierProvider(widget.formTemplateId));
    final showAnswersReceivedText = useState<bool>(false);

    Future<void> onSubmitPressed(FormsState formState) async {
      final isPosted = await ref.executeWithLoading<bool>(() async {
        if (widget.isEditModeOn) {
          if (formState.fields.isEmpty) {
            ref.read(toastManagerProvider).showToastErrorWithMessage(
                  'Lütfen en az bir soru ekleyiniz.'.hardcoded,
                );
            return false;
          }
          return ref
              .read(
                editFormAsyncNotifierProvider(widget.formTemplateId).notifier,
              )
              .createTemplate();
        }
        if (_formKey.currentState!.validate()) {
          return ref
              .read(
                  editFormAsyncNotifierProvider(widget.formTemplateId).notifier)
              .postFormAnswers();
        }
        return false;
      });
      if (isPosted && context.mounted) {
        await context.navigateTo(const FormsRoute());
      }
    }

    if (showAnswersReceivedText.value) {
      return Center(
        child: Text(
          'Cevaplarınız başarıyla kaydedildi.\nTeşekkürler!'.hardcoded,
          style: ATextStyle.text16.copyWith(color: AColor.primaryColor),
        ),
      );
    }

    return BaseAsyncProviderWidget(
      value: editFormAsyncNotifier,
      builder: (formState) {
        if (widget.onlyAllowPublic && !formState.formTemplate.isPublic) {
          return Center(
            child: Text(
              'Bu form herkese açık değildir.'.hardcoded,
              style: ATextStyle.text16.copyWith(color: AColor.primaryColor),
            ),
          );
        }
        return Form(
          key: _formKey,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: _QuestionBuilderMainContents(
                  formState: formState,
                  isEditModeOn: widget.isEditModeOn,
                ),
              ),
              _ActionButtons(
                formTemplateId: widget.formTemplateId,
                isEditModeOn: widget.isEditModeOn,
                onBackPressed: widget.onBackPressed,
                onPreviewPressed: widget.onPreviewPressed,
                onSubmitPressed: widget.showSubmitButton
                    ? () => onSubmitPressed(formState)
                    : null,
              ),
            ],
          ),
        );
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// A shimmer effect widget that can be used to show loading states.
///
/// The shimmer effect is achieved using flutter_animate's shimmer animation.
///
/// Example usage:
/// ```dart
/// AppShimmer(
///   width: 100,
///   height: 20,
///   backgroundColor: Colors.grey[200],
///   shimmerColor: Colors.grey[400],
///   borderRadius: BorderRadius.circular(4),
/// )
/// ```
///
/// Parameters:
/// - [width] - Optional width of the shimmer container
/// - [height] - Optional height of the shimmer container
/// - [backgroundColor] - Background color of the shimmer container (defaults to grey[200])
/// - [shimmerColor] - Color of the shimmer effect (defaults to grey[400])
/// - [borderRadius] - Border radius of the shimmer container (defaults to AppRadiuses.xs)
/// - [child] - Optional child widget to display inside the shimmer container

class AppShimmer extends StatelessWidget {
  const AppShimmer({
    super.key,
    this.backgroundColor,
    this.shimmerColor,
    this.width,
    this.height,
    this.borderRadius,
    this.child,
  });

  final double? width;
  final double? height;
  final Color? backgroundColor;
  final Color? shimmerColor;
  final BorderRadius? borderRadius;
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.grey[200],
        borderRadius: borderRadius ?? BorderRadius.circular(4),
      ),
      child: child,
    )
        .animate(onPlay: (controller) => controller.repeat())
        .shimmer(duration: 1200.ms, color: shimmerColor ?? Colors.grey[400], size: .8);
  }
}

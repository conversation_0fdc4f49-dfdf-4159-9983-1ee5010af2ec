import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/enums/regex_type.dart';
import 'package:smart_team_web/src/theme/colors.dart';

class CustomTextFormField extends StatefulHookConsumerWidget {
  const CustomTextFormField({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.errorText,
    this.onChanged,
    this.validator,
    this.obscureText = false,
    this.enabled = true,
    this.prefixIcon,
    this.suffixIcon,
    this.keyboardType,
    this.textInputAction,
    this.maxLines = 1,
    this.minLines,
    this.debounceDuration,
    this.fillcolor = AColor.white,
    this.readOnly = false,
    this.onTap,
    this.style,
    this.contentPadding,
    this.headerText,
    this.headerTextStyle,
    this.borderColor,
    this.inputFormatters,
    this.regexType,
    this.focusNode,
    this.autovalidateMode,
  });

  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final String? errorText;
  final ValueChanged<String>? onChanged;
  final String? Function(String?)? validator;
  final bool obscureText;
  final bool enabled;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final int? maxLines;
  final int? minLines;
  final Duration? debounceDuration;
  final Color? fillcolor;
  final bool readOnly;
  final VoidCallback? onTap;
  final TextStyle? style;
  final EdgeInsets? contentPadding;
  final String? headerText;
  final TextStyle? headerTextStyle;
  final FocusNode? focusNode;
  final Color? borderColor;
  final List<TextInputFormatter>? inputFormatters;
  final RegexType? regexType;
  final AutovalidateMode? autovalidateMode;

  @override
  ConsumerState<CustomTextFormField> createState() => _CustomTextFormFieldState();
}

class _CustomTextFormFieldState extends ConsumerState<CustomTextFormField> {
  Timer? _debounceTimer;

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final style = ref.watch(appStyleProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 8,
      children: [
        if (widget.headerText != null)
          Text(
            widget.headerText!,
            style: widget.headerTextStyle ?? style.text.bodyXXSmall,
          ),
        TextFormField(
          focusNode: widget.focusNode,
          controller: widget.controller,
          readOnly: widget.readOnly,
          onTap: widget.onTap,
          onChanged: (value) {
            if (widget.debounceDuration != null) {
              _debounceTimer?.cancel();
              _debounceTimer = Timer(widget.debounceDuration!, () {
                widget.onChanged?.call(value);
              });
            } else {
              widget.onChanged?.call(value);
            }
          },
          validator: widget.validator,
          obscureText: widget.obscureText,
          enabled: widget.enabled,
          keyboardType: widget.keyboardType,
          textInputAction: widget.textInputAction,
          maxLines: widget.maxLines,
          minLines: widget.minLines,
          style: widget.style ?? style.text.bodyXSmall,
          cursorColor: AColor.black,
          inputFormatters: _inputFormatters,
          autovalidateMode: widget.autovalidateMode,
          decoration: InputDecoration(
            isDense: true, // Makes the field more compact
            contentPadding: widget.contentPadding ?? const EdgeInsets.all(12),
            labelText: widget.labelText,
            hintText: widget.hintText,
            hintStyle: style.text.bodyXSmall.copyWith(color: AColor.loginBorderColor),
            errorText: widget.errorText,
            errorStyle: ATextStyle.text12.copyWith(
              color: AColor.red,
            ),
            prefixIcon: widget.prefixIcon,
            enabled: widget.enabled,
            prefixIconConstraints: const BoxConstraints(
              minWidth: 40,
              minHeight: 20,
            ),
            suffixIcon: widget.suffixIcon,
            suffixIconConstraints: const BoxConstraints(
              minWidth: 40,
              minHeight: 20,
            ),
            floatingLabelStyle: const TextStyle(color: Colors.black),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: BorderSide(
                color: widget.borderColor ?? AColor.loginBorderColor,
              ),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: BorderSide(
                color: widget.borderColor ?? AColor.loginBorderColor,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: BorderSide(
                color: widget.borderColor ?? AColor.black,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: const BorderSide(
                color: AColor.red,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: const BorderSide(
                color: AColor.red,
              ),
            ),
            filled: true,
            fillColor: widget.fillcolor,
          ),
        ),
      ],
    );
  }

  List<TextInputFormatter> get _inputFormatters {
    // Eğer kullanıcı tarafından inputFormatters verilmişse onu kullan
    if (widget.inputFormatters != null) {
      return widget.inputFormatters!;
    }
    // regexType'a göre varsayılan inputFormatters üret
    if (widget.regexType != null) {
      switch (widget.regexType!) {
        case RegexType.phone:
          // Telefon: rakam, artı, boşluk ve tire
          return [FilteringTextInputFormatter.allow(RegExp(r'[0-9+\s-]'))];

        case RegexType.id:
          // Sadece rakam
          return [FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))];
        // Diğer regexType'lar için gerekiyorsa eklemeler yapabilirsiniz.
        default:
          return [];
      }
    }
    return [];
  }
}

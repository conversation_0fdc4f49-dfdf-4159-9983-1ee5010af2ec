import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/calendar/presentation/calendar_view.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/widgets/app_overlay.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

part 'widgets/calendar_expandable.dart';

class ACalendarDropDown extends HookConsumerWidget {
  const ACalendarDropDown({
    required this.initialDate,
    required this.onDateSelected,
    this.displayText,
    super.key,
  });

  final DateTime initialDate;
  final void Function(DateTime) onDateSelected;
  final String? displayText;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isOpen = useState(false);
    final currentDate = useState<DateTime>(initialDate);

    void toggleDropdown() => isOpen.value = !isOpen.value;
    void closeDropdown() => isOpen.value = false;

    return AppOverlay(
      isOpen: isOpen.value,
      onOutsideClick: closeDropdown,
      followerMatchTargetSize: false,
      buildOverlay: (overlayContext) {
        return UnconstrainedBox(
          alignment: Alignment.topLeft,
          child: _CalendarExpandable(
            initialDate: currentDate.value,
            onDateSelected: (selected) {
              onDateSelected(selected);
              currentDate.value = selected;
              closeDropdown();
            },
          ),
        );
      },
      buildTrigger: (ctx) => InkWell(
        onTap: toggleDropdown,
        child: Row(
          spacing: 8,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.calendar_today,
              size: 16,
              color: Colors.grey.shade700,
            ),
            TextWidget(
              displayText ??
                  '${currentDate.value.day}.${currentDate.value.month}.${currentDate.value.year}',
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}

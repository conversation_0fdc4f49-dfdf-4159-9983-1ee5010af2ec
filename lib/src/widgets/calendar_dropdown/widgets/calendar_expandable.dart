part of '../a_calendar_dropdown.dart';

class _CalendarExpandable extends HookConsumerWidget {
  const _CalendarExpandable({
    required this.initialDate,
    required this.onDateSelected,
  });

  final DateTime initialDate;
  final void Function(DateTime) onDateSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final displayedMonth = useState<DateTime>(
      DateTime(initialDate.year, initialDate.month),
    );
    final selectedDate = useState<DateTime>(initialDate);
    final today = DateTime.now();

    return Material(
      elevation: 4,
      borderRadius: BorderRadius.circular(4),
      child: SizedBox(
        width: 300,
        height: 300,
        child: Column(
          children: [
            _buildHeader(context, displayedMonth),
            const SizedBox(height: 8),
            _buildDaysOfWeekRow(),
            Expanded(
              child: SingleChildScrollView(
                child: _buildDaysGrid(context, displayedMonth, selectedDate),
              ),
            ),
            _buildBottomLabel(context, today),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(
    BuildContext context,
    ValueNotifier<DateTime> displayedMonth,
  ) {
    final monthLabel = monthName(context, displayedMonth.value.month);
    final year = displayedMonth.value.year;

    return Row(
      children: [
        SizedBox(
          width: 40,
          child: IconButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              final newMonth = DateTime(
                displayedMonth.value.year,
                displayedMonth.value.month - 1,
              );
              displayedMonth.value = newMonth;
              onDateSelected(newMonth);
            },
            icon: const Icon(Icons.chevron_left, size: 20),
          ),
        ),
        Expanded(
          child: Center(
            child: TextWidget(
              '$monthLabel $year',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ),
        SizedBox(
          width: 40,
          child: IconButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              displayedMonth.value = DateTime(
                displayedMonth.value.year,
                displayedMonth.value.month + 1,
              );
            },
            icon: const Icon(Icons.chevron_right, size: 20),
          ),
        ),
      ],
    );
  }

  Widget _buildDaysOfWeekRow() {
    final daysOfWeek = [
      'PT'.hardcoded,
      'SA'.hardcoded,
      'ÇA'.hardcoded,
      'PE'.hardcoded,
      'CU'.hardcoded,
      'CT'.hardcoded,
      'PA'.hardcoded,
    ];
    return Row(
      children: daysOfWeek.map((d) {
        return Expanded(
          child: Center(
            child: TextWidget(
              d,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDaysGrid(
    BuildContext context,
    ValueNotifier<DateTime> displayedMonth,
    ValueNotifier<DateTime> selectedDate,
  ) {
    final firstDayOfMonth =
        DateTime(displayedMonth.value.year, displayedMonth.value.month);
    final lastDayOfMonth =
        DateTime(displayedMonth.value.year, displayedMonth.value.month + 1, 0);
    final daysInMonth = lastDayOfMonth.day;
    final leadingDays = firstDayOfMonth.weekday - 1;
    const totalCells = 42;
    final days = List<DateTime?>.filled(totalCells, null);

    for (var i = 0; i < daysInMonth; i++) {
      days[leadingDays + i] = DateTime(
        displayedMonth.value.year,
        displayedMonth.value.month,
        i + 1,
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Wrap(
        spacing: 2,
        runSpacing: 2,
        children: List.generate(totalCells, (index) {
          final day = days[index];
          if (day == null) {
            return _buildEmptyCell();
          } else {
            return _buildDayCell(context, day, displayedMonth, selectedDate);
          }
        }),
      ),
    );
  }

  Widget _buildEmptyCell() {
    return const SizedBox(width: 36, height: 36);
  }

  Widget _buildDayCell(
    BuildContext context,
    DateTime day,
    ValueNotifier<DateTime> displayedMonth,
    ValueNotifier<DateTime> selectedDate,
  ) {
    final now = DateTime.now();
    final isSelected = _isSameDay(day, selectedDate.value);
    final isToday = _isSameDay(day, now);
    final isCurrentMonth = (day.month == displayedMonth.value.month);

    Color? bgColor;
    var textColor = Colors.black;

    if (isSelected) {
      bgColor = Colors.red.shade400;
      textColor = Colors.white;
    } else if (!isCurrentMonth) {
      textColor = Colors.grey;
    }

    return InkWell(
      onTap: () {
        selectedDate.value = day;
        onDateSelected(day);
      },
      child: Container(
        width: 36,
        height: 36,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(4),
        ),
        child: TextWidget(
          '${day.day}',
          style: TextStyle(
            color: textColor,
            fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildBottomLabel(BuildContext context, DateTime today) {
    final day = today.day;
    final monthLabel = monthName(context, today.month);
    final year = today.year;
    final weekdayLabel = weekName(context, today.weekday);
    return Padding(
      padding: const EdgeInsets.only(top: 8, bottom: 8),
      child: TextWidget(
        '$day $monthLabel $year $weekdayLabel',
        style: TextStyle(color: Colors.red.shade400),
      ),
    );
  }

  bool _isSameDay(DateTime a, DateTime b) =>
      a.year == b.year && a.month == b.month && a.day == b.day;
}

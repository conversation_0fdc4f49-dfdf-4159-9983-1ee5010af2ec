import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

/// Widget that allows for conditional rendering based on hover
/// events and taps, with support for disabling interactions.
class HoverBuilder extends HookWidget {
  const HoverBuilder({required this.builder, this.onTap, this.isDisabled = false, super.key});

  final Widget Function(bool isHovering, bool isPressed) builder;
  final VoidCallback? onTap;
  final bool isDisabled;

  @override
  Widget build(BuildContext context) {
    final isHovering = useState(false);
    final isPressed = useState(false);

    useEffect(
      () {
        Future.microtask(() {
          if (isDisabled) {
            isPressed.value = false;
            isHovering.value = false;
          }
        });
        return null;
      },
      [isDisabled],
    );

    return onTap == null
        ? MouseRegion(
            onEnter: (_) => isHovering.value = true,
            onExit: (_) => isHovering.value = false,
            child: GestureDetector(
              onTapDown: (_) => isPressed.value = true,
              onTapUp: (_) => isPressed.value = false,
              onTapCancel: () => isPressed.value = false,
              child: builder(
                isHovering.value,
                isPressed.value,
              ),
            ),
          )
        : InkWell(
            onTap: isDisabled ? null : onTap,
            onTapDown: isDisabled ? null : (_) => isPressed.value = true,
            onTapUp: isDisabled ? null : (_) => isPressed.value = false,
            onTapCancel: isDisabled ? null : () => isPressed.value = false,
            onHover: isDisabled ? null : (hovering) => isHovering.value = hovering,
            child: builder(isHovering.value, isPressed.value),
          );
  }
}

import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/text_widget/default_text_widget_tags.dart';
import 'package:styled_text/styled_text.dart';

class TextWidget extends StatelessWidget {
  const TextWidget(
    this.styledText, {
    super.key,
    this.customTag = const {},
    this.color = AColor.textColor,
    this.fontFamily,
    this.textAlign = TextAlign.start,
    this.maxLines = 999,
    this.fontWeight = FontWeight.normal,
    this.overflow = TextOverflow.ellipsis,
    this.size,
    this.style,
  });
  final String styledText;
  final Map<String, StyledTextTagBase> customTag;
  final Color? color;
  final String? fontFamily;
  final TextAlign textAlign;
  final int? maxLines;
  final FontWeight? fontWeight;
  final TextOverflow overflow;
  final double? size;
  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    final formFactor = context.responsive<double>(desktop: 1.2, mobile: .8, tablet: 1);
    final fontSize = size != null ? size! * formFactor : null;
    return StyledText(
      text: styledText,
      tags: {...defaultTextWidgetTags, ...customTag},
      overflow: overflow,
      textAlign: textAlign,
      maxLines: maxLines,
      // textScaleFactor: 1,
      style: style?.copyWith(
            fontSize: style?.fontSize != null ? style!.fontSize! * formFactor : null,
          ) ??
          TextStyle(
            color: color,
            fontWeight: fontWeight,
            fontFamily: fontFamily,
            fontSize: fontSize,
          ),
    );
  }
}

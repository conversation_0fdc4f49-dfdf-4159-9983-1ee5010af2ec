import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class BaseAsyncProviderWidgetData<T> extends HookConsumerWidget {
  const BaseAsyncProviderWidgetData({
    required this.value,
    required this.builder,
    this.loadingBuilder,
    this.errorBuilder,
    super.key,
  });

  final AsyncValue<T> value;
  final Widget Function(T) builder;
  final Widget Function(AsyncError<T> error)? errorBuilder;
  final Widget Function(AsyncLoading<T> loading)? loadingBuilder;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return value.map<Widget>(
      data: (data) {
        return builder(data.value);
      },
      error: (asyncError) {
        debugPrint(asyncError.error.toString());
        if (errorBuilder != null) return errorBuilder!(asyncError);
        return const SizedBox();
      },
      loading: (asyncLoading) {
        if (loadingBuilder != null) return loadingBuilder!(asyncLoading);
        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );
  }
}

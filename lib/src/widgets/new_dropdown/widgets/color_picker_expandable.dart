part of '../new_dropdown.dart';

class _ColorPickerExpandable extends HookConsumerWidget {
  const _ColorPickerExpandable({
    required this.onColorSelected,
    this.initialColor = AColor.primaryColor,
  });

  final void Function(Color) onColorSelected;
  final Color initialColor;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedColor = useState<Color>(initialColor);

    useState<String>('RGB');
    final rController = useTextEditingController(
      text: selectedColor.value.r.toInt().toString(),
    );
    final gController = useTextEditingController(
      text: selectedColor.value.g.toInt().toString(),
    );
    final bController = useTextEditingController(
      text: selectedColor.value.b.toInt().toString(),
    );
    final hexController = useTextEditingController(
      text: _colorToHex(selectedColor.value),
    );

    useEffect(
      () {
        final color = selectedColor.value;
        rController.text = color.r.toInt().toString();
        gController.text = color.g.toInt().toString();
        bController.text = color.b.toInt().toString();
        hexController.text = _colorToHex(color);
        return null;
      },
      [selectedColor.value],
    );

    return ConstrainedBox(
      constraints: const BoxConstraints(
        maxHeight: 350,
        maxWidth: 350,
      ),
      child: Container(
        margin: const EdgeInsets.only(top: 4),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
          boxShadow: const [
            BoxShadow(
              color: Color(0x66210A38),
              blurRadius: 40,
              offset: Offset(8, 20),
              spreadRadius: -4,
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.only(left: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: ColorPicker(
                  pickerColor: selectedColor.value,
                  onColorChanged: (color) {
                    selectedColor.value = color;
                  },
                  enableAlpha: false,
                  displayThumbColor: true,
                  paletteType: PaletteType.hsv,
                  pickerAreaHeightPercent: 0.8,
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(8),
                child: Row(
                  children: [
                    const Spacer(),
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                          side: BorderSide(
                            color: Colors.grey.shade300,
                          ),
                        ),
                        backgroundColor: Colors.grey.shade50,
                      ),
                      onPressed: () {
                        onColorSelected(selectedColor.value);
                      },
                      child: TextWidget(context.tr.ok),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _colorToHex(Color color) {
    final rgb =
        (color.r.toInt() << 16) | (color.g.toInt() << 8) | color.b.toInt();
    return '#${rgb.toRadixString(16).padLeft(6, '0').toUpperCase()}';
  }
}

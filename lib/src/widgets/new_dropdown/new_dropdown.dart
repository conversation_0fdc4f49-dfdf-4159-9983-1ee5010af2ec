import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:gap/gap.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/app_infinite_scroll.dart';
import 'package:smart_team_web/src/widgets/hover_builder.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

part 'widgets/color_picker_expandable.dart';
part 'widgets/new_dropdown_expandable.dart';
part 'widgets/search_field.dart';

class NewDropDown<T> extends StatefulHookConsumerWidget {
  const NewDropDown({
    required this.placeholder,
    required this.itemList,
    required this.onSelected,
    this.width,
    this.height,
    this.selectedItems,
    this.header,
    this.searchHint,
    this.itemBuilder,
    this.onLoadNewItems,
    this.decoration,
    this.enableColorPicker = false,
    this.enableSearch = false,
    this.initialColor,
    this.borderColor,
    this.onColorSelected,
    this.onLoadMore,
    this.allowMultipleSelection = false,
    this.isDisabled = false,
    this.prefix,
    super.key,
  });

  final double? width;
  final double? height;
  final String placeholder;
  final String? header;
  final String? searchHint;
  final List<T> itemList;
  final List<T>? selectedItems;
  final void Function(T) onSelected;
  final Color? initialColor;
  final void Function(Color)? onColorSelected;
  final Widget Function(T?)? itemBuilder;
  final Widget? prefix;
  final Future<void> Function()? onLoadNewItems;
  final Decoration? decoration;
  final Color? borderColor;
  final bool enableColorPicker;
  final bool enableSearch;
  final bool allowMultipleSelection;
  final bool isDisabled;
  final Future<void> Function()? onLoadMore;

  @override
  ConsumerState<NewDropDown<T>> createState() => _NewDropDownState<T>();
}

class _NewDropDownState<T> extends ConsumerState<NewDropDown<T>> {
  final MenuController _controller = MenuController();

  @override
  Widget build(BuildContext context) {
    final style = ref.watch(appStyleProvider);
    final selectedColor = useState<Color>(widget.initialColor ?? AColor.primaryColor);

    void toggleDropdown() {
      if (_controller.isOpen) {
        _controller.close();
      } else {
        _controller.open();
      }
    }

    void closeDropdown() {
      _controller.close();
    }

    Widget buildSelectedItems() {
      if (widget.selectedItems == null || widget.selectedItems!.isEmpty) {
        return Text(
          widget.placeholder,
          style: style.text.bodyXSmall.copyWith(color: AColor.textColor.withValues(alpha: .4)),
        );
      }
      final itemsToShow = widget.selectedItems!.take(3).toList();
      final remainingCount = widget.selectedItems!.length - 3;
      return Row(
        children: [
          ...List.generate(itemsToShow.length, (index) {
            final item = itemsToShow[index];
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                widget.itemBuilder?.call(item) ??
                    Text(
                      item.toString(),
                      style: style.text.bodyXSmall.copyWith(color: AColor.textColor),
                    ),
                if (index < itemsToShow.length - 1 || remainingCount > 0)
                  Text(
                    ', ',
                    style: style.text.bodyXSmall.copyWith(color: AColor.textColor),
                  ),
              ],
            );
          }),
          if (remainingCount > 0)
            Text(
              '+ $remainingCount daha'.hardcoded,
              style: style.text.bodyXSmall.copyWith(color: AColor.textColor),
            ),
        ],
      );
    }

    return IgnorePointer(
      ignoring: widget.isDisabled || (widget.itemList.isEmpty && !widget.enableColorPicker),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        spacing: 8,
        children: [
          if (widget.header != null && widget.header!.isNotEmpty)
            Text(widget.header!, style: style.text.bodyXXSmall),
          RawMenuAnchor(
            controller: _controller,
            overlayBuilder: (_, info) {
              return Positioned(
                    top: info.anchorRect.bottom + 6,
                    left: info.anchorRect.left,
                    child: PointerInterceptor(
                      child: TapRegion(
                        onTapOutside: (_) => closeDropdown(),
                        groupId: info.tapRegionGroupId,
                        child: SizedBox(
                          width: info.anchorRect.size.width,
                          child: _NewDropdownExpandable<T>(
                            itemList: widget.itemList,
                            selectedItemList: widget.selectedItems ?? [],
                            enableSearch: widget.enableSearch,
                            searchHint: widget.searchHint,
                            itemBuilder: widget.itemBuilder,
                            onLoadMore: widget.onLoadMore,
                            allowMultipleSelection: widget.allowMultipleSelection,
                            onSelected: (item) {
                              widget.onSelected(item);
                              if (!widget.allowMultipleSelection) {
                                closeDropdown();
                              }
                            },
                            onLoadNewItems: widget.onLoadNewItems == null
                                ? null
                                : () async {
                                    closeDropdown();
                                    await widget.onLoadNewItems!();
                                  },
                          ),
                        ),
                      ),
                    ),
                  );
            },
            child: Opacity(
              opacity: widget.isDisabled ? .4 : 1,
              child: InkWell(
                onTap: toggleDropdown,
                child: Container(
                  width: widget.width,
                  height: widget.height ?? 37,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  decoration: widget.decoration ??
                      CommonDecorations.containerDecoration(
                        borderColor: widget.borderColor ?? AColor.loginBorderColor,
                      ).copyWith(
                        color: widget.enableColorPicker ? selectedColor.value : Colors.white,
                      ),
                  child: Row(
                    spacing: 4,
                    children: [
                      if (widget.prefix != null) widget.prefix!,
                      Expanded(
                        child: widget.enableColorPicker ? const SizedBox.shrink() : buildSelectedItems(),
                      ),
                      Icon(
                        Icons.keyboard_arrow_down,
                        color: widget.enableColorPicker ? AColor.white : AColor.textColor,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

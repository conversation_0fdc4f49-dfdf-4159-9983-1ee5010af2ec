import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:smart_team_web/src/shared/constants/constants.dart';

class AppMap extends StatefulHookWidget {
  const AppMap({
    this.location,
    this.onLocationClicked,
    super.key,
  });

  final LatLng? location;
  final void Function(LatLng)? onLocationClicked;

  @override
  State<AppMap> createState() => _AppMapState();
}

class _AppMapState extends State<AppMap> {
  final _controller = Completer<GoogleMapController>();

  Future<void> _moveToLocation(LatLng newLocation) async {
    final controller = await _controller.future;
    final zoomLevel = await controller.getZoomLevel();
    await controller.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(target: newLocation, zoom: zoomLevel),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final location = widget.location ?? kDefaultLocation;

    useEffect(
      () {
        if (context.mounted && widget.location != null) {
          _moveToLocation(widget.location!);
        }
        return null;
      },
      [widget.location],
    );

    return GoogleMap(
      initialCameraPosition: CameraPosition(
        target: location,
        zoom: 6,
      ),
      onTap: widget.onLocationClicked,
      markers: {
        Marker(
          markerId: const MarkerId('selected_location'),
          position: location,
          anchor: const Offset(.5, .5),
        ),
      },
      onMapCreated: _controller.complete,
    );
  }
}

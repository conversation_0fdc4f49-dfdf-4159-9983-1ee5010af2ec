part of '../team_select_dropdown_widget.dart';

class TeamSelectDropdownExpandable<T> extends HookConsumerWidget {
  const TeamSelectDropdownExpandable({
    required this.widgetId,
    required this.itemList,
    required this.selectedItemList,
    required this.onItemToggle,
    required this.onSelectionChange,
    required this.onSave,
    required this.isMultiSelect,
    required this.itemBuilder,
    required this.filter,
    super.key,
  });

  final String widgetId;
  final List<T> itemList;
  final List<T> selectedItemList;
  final void Function(T) onItemToggle;
  final void Function(List<T>) onSelectionChange;
  final VoidCallback onSave;
  final bool isMultiSelect;
  final Widget Function(T)? itemBuilder;
  final String Function(T) filter;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchText = useState('');
    final isSearching = useState(false);
    final tempSelectedItems = useState<List<T>>(List.from(selectedItemList));

    final currentHeight = useState<double>(300);
    const minHeight = 200.0;
    const maxHeight = 600.0;

    // Calculate height constraints based on current height
    final heightConstraints = useMemoized(
      () {
        return BoxConstraints(
          maxHeight: currentHeight.value,
          minHeight: minHeight,
        );
      },
      [currentHeight.value],
    );

    final filteredItems = useMemoized(
      () {
        if (searchText.value.isEmpty) return itemList;
        final q = searchText.value.toLowerCase();
        return itemList.where((item) {
          return filter(item).toLowerCase().contains(q);
        }).toList();
      },
      [searchText.value, itemList],
    );

    final hasNoResults = filteredItems.isEmpty && searchText.value.isNotEmpty;

    void handleSearchChange(String value) {
      searchText.value = value;
      isSearching.value = false;
    }

    void handleSearchStart() {
      isSearching.value = true;
    }

    void handleItemToggle(T item) {
      final currentSelection = List<T>.from(tempSelectedItems.value);
      if (currentSelection.contains(item)) {
        currentSelection.remove(item);
      } else {
        currentSelection.add(item);
      }
      tempSelectedItems.value = currentSelection;
    }

    void handleSelectionChange(List<T> newSelection) {
      tempSelectedItems.value = newSelection;
    }

    void handleSave() {
      onSelectionChange(tempSelectedItems.value);
      onSave();
    }

    // New handler for resizing via drag
    void handleDrag(DragUpdateDetails details) {
      // Düzeltilmiş mantık: Aşağı sürüklerken (pozitif dy) yüksekliği artır,
      // yukarı sürüklerken (negatif dy) yüksekliği azalt
      final newHeight = currentHeight.value + details.delta.dy;

      // Enforce min/max constraints
      if (newHeight >= minHeight && newHeight <= maxHeight) {
        currentHeight.value = newHeight;
      }
    }

    return RepaintBoundary(
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 100),
        curve: Curves.easeInOut,
        constraints: heightConstraints,
        margin: const EdgeInsets.only(top: 4),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
          boxShadow: const [
            BoxShadow(
              color: Color(0x66210A38),
              blurRadius: 40,
              offset: Offset(8, 20),
              spreadRadius: -4,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _OptimizedDropdownSearchField(
              searchText: searchText.value,
              onChanged: handleSearchChange,
              onSearchStart: handleSearchStart,
              onClear: () {
                searchText.value = '';
                isSearching.value = false;
              },
            ),
            if (isMultiSelect && !isSearching.value)
              _SelectionHeaderControls<T>(
                itemList: itemList,
                selectedItemList: tempSelectedItems.value,
                onSelectionChange: handleSelectionChange,
              ),
            Expanded(
              child: hasNoResults
                  ? Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          'Sonuç bulunamadı.'.hardcoded,
                          style: ATextStyle.text14.copyWith(
                            color: AColor.textColor,
                          ),
                        ),
                      ),
                    )
                  : _OptimizedItemList<T>(
                      filteredItems: filteredItems,
                      selectedItemList: tempSelectedItems.value,
                      onItemToggle: handleItemToggle,
                      itemBuilder: itemBuilder,
                    ),
            ),
            if (isMultiSelect && !isSearching.value)
              _SelectionFooterControl(
                onSave: handleSave,
                onDrag: handleDrag,
              ),
          ],
        ),
      ),
    );
  }
}

class _OptimizedDropdownSearchField extends HookWidget {
  const _OptimizedDropdownSearchField({
    required this.searchText,
    required this.onChanged,
    required this.onClear,
    required this.onSearchStart,
  });

  final String searchText;
  final Function(String) onChanged;
  final VoidCallback onClear;
  final VoidCallback onSearchStart;

  @override
  Widget build(BuildContext context) {
    final controller = useTextEditingController(text: searchText);
    final debounceTimer = useRef<Timer?>(null);
    final isFocused = useState(false);
    final focusNode = useFocusNode();

    useEffect(
      () {
        focusNode.addListener(() {
          isFocused.value = focusNode.hasFocus;
        });
        return () {
          debounceTimer.value?.cancel();
        };
      },
      [],
    );

    useEffect(
      () {
        if (controller.text != searchText) {
          controller.text = searchText;
        }
        return null;
      },
      [searchText],
    );

    void handleTextChange(String value) {
      onSearchStart();
      debounceTimer.value?.cancel();

      debounceTimer.value = Timer(const Duration(milliseconds: 500), () {
        onChanged(value);
      });
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Container(
        decoration: CommonDecorations.containerDecoration().copyWith(
          border: Border.all(
            color: isFocused.value ? AColor.primaryColor : Colors.grey.shade300,
          ),
        ),
        child: TextField(
          controller: controller,
          focusNode: focusNode,
          style: ATextStyle.mediumRegular,
          decoration: InputDecoration(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            filled: true,
            isDense: true,
            fillColor: AColor.white,
            hintText: 'Kişi ara...'.hardcoded,
            prefixIcon: const Padding(
              padding: EdgeInsets.all(8),
              child: Icon(Icons.search),
            ),
            prefixIconConstraints: const BoxConstraints(
              minWidth: 24,
              minHeight: 24,
            ),
            suffixIcon: searchText.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear, size: 18),
                    onPressed: onClear,
                  )
                : null,
            border: const OutlineInputBorder(
              borderSide: BorderSide.none,
            ),
          ),
          onChanged: handleTextChange,
        ),
      ),
    );
  }
}

class _OptimizedItemList<T> extends HookWidget {
  const _OptimizedItemList({
    required this.filteredItems,
    required this.selectedItemList,
    required this.onItemToggle,
    required this.itemBuilder,
    super.key,
  });

  final List<T> filteredItems;
  final List<T> selectedItemList;
  final void Function(T) onItemToggle;
  final Widget Function(T)? itemBuilder;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: filteredItems.length,
      itemExtent: 40, // Sabit yükseklik
      cacheExtent: 1000, // Daha fazla öğeyi önbellekte tut
      itemBuilder: (_, index) {
        final item = filteredItems[index];
        final isSelected = selectedItemList.contains(item);

        return _OptimizedItemTile<T>(
          item: item,
          isSelected: isSelected,
          onToggle: onItemToggle,
          itemBuilder: itemBuilder,
        );
      },
    );
  }
}

class _OptimizedItemTile<T> extends HookWidget {
  const _OptimizedItemTile({
    required this.item,
    required this.isSelected,
    required this.onToggle,
    required this.itemBuilder,
    super.key,
  });

  final T item;
  final bool isSelected;
  final void Function(T) onToggle;
  final Widget Function(T)? itemBuilder;

  @override
  Widget build(BuildContext context) {
    return HoverBuilder(
      onTap: () => onToggle(item),
      builder: (isHovering, _) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: double.infinity,
          padding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 8,
          ),
          color: isHovering ? AColor.lightGreyBackground : Colors.white,
          child: Row(
            spacing: 8,
            children: [
              Checkbox(
                value: isSelected,
                onChanged: (_) => onToggle(item),
                activeColor: AColor.primaryColor,
                side: const BorderSide(color: AColor.grayMedium),
              ),
              Expanded(
                child: itemBuilder != null
                    ? itemBuilder!(item)
                    : Text(
                        item.toString(),
                        style: TextStyle(
                          color: Colors.black87,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class _SelectionHeaderControls<T> extends StatelessWidget {
  const _SelectionHeaderControls({
    required this.itemList,
    required this.selectedItemList,
    required this.onSelectionChange,
    super.key,
  });

  final List<T> itemList;
  final List<T> selectedItemList;
  final void Function(List<T>) onSelectionChange;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: () {
              onSelectionChange(List<T>.from(itemList));
            },
            child: TextWidget(
              'Tümünü Seç'.hardcoded,
              style: ATextStyle.text12.copyWith(
                color: AColor.primaryColor,
                decoration: TextDecoration.underline,
                decorationColor: AColor.primaryColor,
              ),
            ),
          ),
          InkWell(
            onTap: () {
              onSelectionChange([]);
            },
            child: TextWidget(
              'Tümünü Temizle'.hardcoded,
              style: ATextStyle.text12.copyWith(
                color: AColor.inactiveColor,
                decoration: TextDecoration.underline,
                decorationColor: AColor.inactiveColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _SelectionFooterControl extends StatelessWidget {
  const _SelectionFooterControl({
    required this.onSave,
    required this.onDrag,
  });

  final VoidCallback onSave;
  final void Function(DragUpdateDetails) onDrag;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Center(
              child: InkWell(
                onTap: onSave,
                child: Container(
                  width: 270,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 6),
                  decoration: BoxDecoration(
                    color: AColor.primaryColor,
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Center(
                    child: Text(
                      context.tr.save,
                      style: ATextStyle.text14.copyWith(color: AColor.white),
                    ),
                  ),
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(right: 12),
            child: MouseRegion(
              cursor: SystemMouseCursors.resizeUpDown,
              child: GestureDetector(
                onVerticalDragUpdate: onDrag,
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: AColor.lightGreyBackground,
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: const Icon(
                    Icons.drag_indicator,
                    size: 18,
                    color: AColor.black,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/theme/colors.dart';

class AppDialog extends StatelessWidget {
  const AppDialog({
    required this.child,
    this.title,
    this.padding,
    this.backgroundColor,
    this.width,
    this.height,
    this.borderRadius,
    this.barrierDismissible = true,
    this.showCloseButton = true,
    this.showHeader = true,
    super.key,
  });

  final Widget child;
  final String? title;
  final EdgeInsets? padding;
  final Color? backgroundColor;
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final bool barrierDismissible;
  final bool showCloseButton;
  final bool showHeader;

  static Future<T?> show<T>({
    required BuildContext context,
    required Widget child,
    String? title,
    EdgeInsets? padding,
    Color? backgroundColor,
    double? width,
    double? height,
    BorderRadius? borderRadius,
    bool showCloseButton = true,
    bool showHeader = true,
    bool barrierDismissible = true,
  }) =>
      _showInternal(
        context: context,
        child: child,
        title: title,
        padding: padding,
        backgroundColor: backgroundColor,
        width: width,
        height: height,
        borderRadius: borderRadius,
        showHeader: showHeader,
        showCloseButton: showCloseButton,
        barrierDismissible: barrierDismissible,
      );

  static Future<T?> withoutHeader<T>({
    required BuildContext context,
    required Widget child,
    EdgeInsets? padding,
    Color? backgroundColor,
    double? width,
    double? height,
    BorderRadius? borderRadius,
    bool barrierDismissible = true,
  }) =>
      _showInternal(
        context: context,
        child: child,
        padding: padding,
        backgroundColor: backgroundColor,
        width: width,
        height: height,
        borderRadius: borderRadius,
        showHeader: false,
        showCloseButton: false,
        barrierDismissible: barrierDismissible,
      );

  static Future<T?> _showInternal<T>({
    required BuildContext context,
    required Widget child,
    required bool showHeader,
    required bool showCloseButton,
    String? title,
    EdgeInsets? padding,
    Color? backgroundColor,
    double? width,
    double? height,
    BorderRadius? borderRadius,
    bool barrierDismissible = true,
  }) {
    return showGeneralDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierColor: Colors.black54,
      transitionDuration: const Duration(milliseconds: 400),
      pageBuilder: (context, animation1, animation2) {
        return AppDialog(
          title: title,
          padding: padding,
          backgroundColor: backgroundColor,
          width: width,
          height: height,
          borderRadius: borderRadius ?? BorderRadius.zero,
          barrierDismissible: barrierDismissible,
          showHeader: showHeader,
          showCloseButton: showCloseButton,
          child: PointerInterceptor(child: child),
        );
      },
      transitionBuilder: (ctx, animation, secondaryAnimation, child) {
        final curvedAnimation = CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutBack,
          reverseCurve: Curves.easeInBack,
        );
        return FadeTransition(
          opacity: Tween<double>(begin: 0, end: 1).animate(curvedAnimation),
          child: ScaleTransition(
            scale: Tween<double>(begin: 0.8, end: 1).animate(curvedAnimation),
            child: child,
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: backgroundColor,
      shape: RoundedRectangleBorder(borderRadius: borderRadius!),
      child: SizedBox(
        width: width,
        height: height,
        child: Padding(
          padding: padding ?? EdgeInsets.zero,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (showHeader)
                Container(
                  width: double.infinity,
                  height: 45,
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    color: AColor.primaryColor,
                    borderRadius: BorderRadius.vertical(
                      top: borderRadius?.topLeft ?? Radius.zero,
                    ),
                  ),
                  child: Row(
                    children: [
                      Text(
                        title ?? '',
                        style: ATextStyle.text18.copyWith(color: AColor.white),
                      ),
                      const Spacer(),
                      if (showCloseButton)
                        IconButton(
                          onPressed: context.maybePop,
                          icon: const Icon(Icons.close, color: AColor.white),
                        ),
                    ],
                  ),
                ),
              Flexible(child: child),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_column_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_row_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/st_data_table.dart';

class TableDialog extends StatelessWidget {
  const TableDialog({
    required this.columns,
    required this.data,
    super.key,
    this.showExportButtons = false,
  });

  final List<String> columns;
  final List<List<String>> data;
  final bool showExportButtons;

  @override
  Widget build(BuildContext context) {
    return SmartTeamDataTable(
      showExportButtons: showExportButtons,
      columns: columns.map((col) => TableColumnModel(columnName: col)).toList(),
      rowData: data.map((row) {
        return row.asMap().entries.map((entry) {
          final idx = entry.key;
          final value = entry.value;
          return RowDataModel<String>(
            columnName: columns[idx],
            value: value,
            cellBuilder: () => Text(value, style: ATextStyle.small),
          );
        }).toList();
      }).toList(),
    );
  }
}

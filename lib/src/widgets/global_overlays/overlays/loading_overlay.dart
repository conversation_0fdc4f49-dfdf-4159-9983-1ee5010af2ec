part of '../global_overlays_wrapper.dart';

class _LoadingOverlay extends ConsumerWidget {
  const _LoadingOverlay();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoaderVisible = ref.watch(loadingOverlayManagerProvider);

    if (!isLoaderVisible) return const SizedBox.shrink();

    return ColoredBox(
      color: Colors.black.withValues(alpha: .4),
      child: Colum<PERSON>(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SmartTeamAssets.images.kirmizTek.image(
            width: 300,
          ),
          Transform.translate(
            offset: const Offset(0, -60),
            child: const CircularProgressIndicator(
              color: AColor.primaryColor,
            ),
          ),
        ],
      ),
    );
  }
}

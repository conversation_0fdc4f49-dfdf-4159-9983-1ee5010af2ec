import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/shared/managers/loading_overlay_manager/loading_overlay_manager.dart';
import 'package:smart_team_web/src/theme/colors.dart';

part './overlays/loading_overlay.dart';

class GlobalOverlaysWrapper extends StatelessWidget {
  const GlobalOverlaysWrapper({required this.child, super.key});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: AColor.white,
      child: Stack(
        children: [
          child,
          const Positioned.fill(child: _LoadingOverlay()),
        ],
      ),
    );
  }
}

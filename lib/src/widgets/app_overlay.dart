import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_portal/flutter_portal.dart';

enum AppOverlayPosition {
  under,
  top,
  right,
  left,
  topRight,
  topLeft,
  bottomLeft,
}

class AppOverlay extends HookWidget {
  const AppOverlay({
    required this.isOpen,
    required this.buildOverlay,
    required this.buildTrigger,
    this.onOutsideClick,
    this.position = AppOverlayPosition.under,
    this.followerMatchTargetSize = true,
    this.backgroundColor = Colors.transparent,
    this.additionalOffset = Offset.zero,
    this.expandWithAnimation = true,
    super.key,
  });

  final bool isOpen;
  final VoidCallback? onOutsideClick;
  final AppOverlayPosition position;
  final bool followerMatchTargetSize;
  final Color backgroundColor;
  final Offset additionalOffset;
  final bool expandWithAnimation;
  final Widget Function(BuildContext context) buildOverlay;
  final Widget Function(BuildContext context) buildTrigger;

  Aligned _getAlignedConfig() {
    switch (position) {
      case AppOverlayPosition.under:
        return Aligned(
          follower: Alignment.topCenter,
          target: Alignment.bottomCenter,
          widthFactor: followerMatchTargetSize ? 1 : null,
          shiftToWithinBound: const AxisFlag(y: true),
          offset: additionalOffset,
        );
      case AppOverlayPosition.top:
        return Aligned(
          follower: Alignment.bottomCenter,
          target: Alignment.topCenter,
          widthFactor: followerMatchTargetSize ? 1 : null,
          shiftToWithinBound: const AxisFlag(y: true),
          offset: additionalOffset,
        );
      case AppOverlayPosition.right:
        return Aligned(
          follower: Alignment.centerLeft,
          target: Alignment.centerRight,
          widthFactor: followerMatchTargetSize ? 1 : null,
          shiftToWithinBound: const AxisFlag(x: true),
          offset: additionalOffset,
        );
      case AppOverlayPosition.left:
        return Aligned(
          follower: Alignment.centerRight,
          target: Alignment.centerLeft,
          widthFactor: followerMatchTargetSize ? 1 : null,
          shiftToWithinBound: const AxisFlag(x: true),
          offset: additionalOffset,
        );
      case AppOverlayPosition.topRight:
        return Aligned(
          follower: Alignment.topLeft,
          target: Alignment.topRight,
          widthFactor: followerMatchTargetSize ? 1 : null,
          shiftToWithinBound: const AxisFlag(x: true, y: true),
          offset: additionalOffset,
        );
      case AppOverlayPosition.topLeft:
        return Aligned(
          follower: Alignment.topRight,
          target: Alignment.topLeft,
          widthFactor: followerMatchTargetSize ? 1 : null,
          shiftToWithinBound: const AxisFlag(x: true, y: true),
          offset: additionalOffset,
        );
      case AppOverlayPosition.bottomLeft:
        return Aligned(
          follower: Alignment.topLeft,
          target: Alignment.bottomLeft,
          widthFactor: followerMatchTargetSize ? 1 : null,
          shiftToWithinBound: const AxisFlag(x: true, y: true),
          offset: additionalOffset,
        );
    }
  }

  Alignment _getAnimationAlignment() {
    switch (position) {
      case AppOverlayPosition.under:
      case AppOverlayPosition.bottomLeft:
        return Alignment.topCenter;
      case AppOverlayPosition.top:
        return Alignment.bottomCenter;
      case AppOverlayPosition.right:
      case AppOverlayPosition.topRight:
        return Alignment.centerLeft;
      case AppOverlayPosition.left:
      case AppOverlayPosition.topLeft:
        return Alignment.centerRight;
    }
  }

  Widget _getAnimatedWidget(Widget widget, AnimationController controller) {
    final animate = widget.animate(
      controller: controller,
      autoPlay: false,
    );

    switch (position) {
      case AppOverlayPosition.under:
      case AppOverlayPosition.top:
      case AppOverlayPosition.bottomLeft:
        return animate.scaleY(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          alignment: _getAnimationAlignment(),
        );
      case AppOverlayPosition.right:
      case AppOverlayPosition.left:
      case AppOverlayPosition.topRight:
      case AppOverlayPosition.topLeft:
        return animate.scaleX(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          alignment: _getAnimationAlignment(),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    final animationController = useAnimationController(duration: const Duration(milliseconds: 200));
    final isOverlayVisible = useState(isOpen);

    void handleOverlayVisibility() {
      if (isOpen) {
        isOverlayVisible.value = true;
      } else {
        isOverlayVisible.value = false;
      }
    }

    Future<void> handleOverlayVisibilityWithAnimation() async {
      if (isOpen) {
        isOverlayVisible.value = true;
        await animationController.forward();
      } else {
        await animationController.reverse();
        isOverlayVisible.value = false;
      }
    }

    useEffect(
      () {
        if (!context.mounted) return;
        if (expandWithAnimation) {
          handleOverlayVisibilityWithAnimation();
        } else {
          handleOverlayVisibility();
        }
        return null;
      },
      [isOpen],
    );

    return PortalTarget(
      visible: isOverlayVisible.value,
      portalFollower: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          onOutsideClick?.call();
        },
        child: ColoredBox(
          color: backgroundColor,
        ),
      ),
      child: PortalTarget(
        visible: isOverlayVisible.value,
        anchor: _getAlignedConfig(),
        portalFollower: _getAnimatedWidget(buildOverlay(context), animationController),
        child: buildTrigger(context),
      ),
    );
  }
}

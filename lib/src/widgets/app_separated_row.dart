import 'package:flutter/material.dart';

class AppSeparatedRow extends StatelessWidget {
  const AppSeparatedRow({
    required this.separatorBuilder,
    this.children = const <Widget>[],
    this.mainAxisSize = MainAxisSize.max,
    this.verticalDirection = VerticalDirection.down,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.textBaseline,
    this.textDirection,
    this.includeOuterSeparators = false,
    super.key,
  });
  final bool includeOuterSeparators;
  final IndexedWidgetBuilder separatorBuilder;
  final List<Widget> children;
  final TextBaseline? textBaseline;
  final TextDirection? textDirection;
  final MainAxisSize mainAxisSize;
  final VerticalDirection verticalDirection;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;

  @override
  Widget build(BuildContext context) {
    if (children.isEmpty) {
      // Return an empty Row or SizedBox if there are no children
      return Row(
        key: key,
        mainAxisAlignment: mainAxisAlignment,
        mainAxisSize: mainAxisSize,
        crossAxisAlignment: crossAxisAlignment,
        textDirection: textDirection,
        verticalDirection: verticalDirection,
        textBaseline: textBaseline,
      );
    }

    final widgetsWithSeparators = <Widget>[];
    var separatorIndex = 0;

    // Add leading separator if needed
    if (includeOuterSeparators) {
      widgetsWithSeparators.add(separatorBuilder(context, separatorIndex++));
    }

    // Add first child
    widgetsWithSeparators.add(children[0]);

    // Add remaining children with separators in between
    for (var i = 1; i < children.length; i++) {
      widgetsWithSeparators
        ..add(separatorBuilder(context, separatorIndex++))
        ..add(children[i]);
    }

    // Add trailing separator if needed
    if (includeOuterSeparators) {
      widgetsWithSeparators.add(separatorBuilder(context, separatorIndex++));
    }

    return Row(
      key: key,
      mainAxisAlignment: mainAxisAlignment,
      mainAxisSize: mainAxisSize,
      crossAxisAlignment: crossAxisAlignment,
      textDirection: textDirection,
      verticalDirection: verticalDirection,
      textBaseline: textBaseline,
      children: widgetsWithSeparators,
    );
  }
}

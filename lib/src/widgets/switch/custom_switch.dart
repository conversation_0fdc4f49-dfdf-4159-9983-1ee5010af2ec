import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/theme/colors.dart';

class CustomSwitch extends HookConsumerWidget {
  const CustomSwitch({
    required this.value,
    required this.onChanged,
    this.header,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    super.key,
  });

  final bool value;
  final ValueChanged<bool> onChanged;
  final String? header;
  final CrossAxisAlignment crossAxisAlignment;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SizedBox(
      width: 60,
      height: 30,
      child: FittedBox(
        child: Switch(
          value: value,
          onChanged: onChanged,
          activeTrackColor: AColor.primaryColor,
          activeColor: AColor.white,
          inactiveThumbColor: AColor.white,
          inactiveTrackColor: AColor.grey,
          trackOutlineColor: WidgetStateProperty.resolveWith<Color>(
            (states) {
              return states.contains(WidgetState.selected)
                  ? AColor.primaryColor
                  : Colors.grey;
            },
          ),
        ),
      ),
    );
  }
}

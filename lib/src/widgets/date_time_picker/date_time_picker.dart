import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/date_time_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/hover_builder.dart';

enum DateTimePickerMode {
  date(icons: [Icons.calendar_month_rounded], placeholderText: 'Select Date'),
  time(icons: [Icons.watch_later_outlined], placeholderText: 'Select Time'),
  dateAndTime(
      icons: [Icons.calendar_month_rounded, Icons.watch_later_outlined],
      placeholderText: 'Select Date and Time');

  const DateTimePickerMode({required this.icons, required this.placeholderText});

  final List<IconData> icons;
  final String placeholderText;
}

class DateTimePicker extends HookConsumerWidget {
  const DateTimePicker({
    this.header,
    this.width,
    this.decoration,
    this.selectedDate,
    this.locale = const Locale('en', 'EN'),
    this.startDate,
    this.endDate,
    this.mode = DateTimePickerMode.date,
    this.onSelected,
    this.isDisabled = false,
    this.borderColor,
    super.key,
  });

  final String? header;
  final double? width;
  final Decoration? decoration;
  final DateTime? selectedDate;
  final DateTime? startDate;
  final DateTime? endDate;
  final Locale locale;
  final Color? borderColor;
  final DateTimePickerMode mode;
  final bool isDisabled;
  final void Function(DateTime)? onSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final style = ref.watch(appStyleProvider);
    const defaultColor = AColor.loginBorderColor;
    const hoverColor = AColor.black;

    void setSelectedDate(DateTime date) {
      if (date != selectedDate) {
        onSelected?.call(date);
      }
    }

    Future<void> pickDate() async {
      final now = DateTime.now();
      final pickedDate = await showDatePicker(
        context: context,
        locale: locale,
        initialDate: selectedDate,
        firstDate: startDate ?? DateTime(2020),
        lastDate: endDate ?? DateTime(now.year + 5),
      );
      if (pickedDate != null) {
        setSelectedDate(pickedDate.copyWith(hour: selectedDate?.hour ?? 0, minute: selectedDate?.minute ?? 0));
      }
    }

    Future<void> pickTime() async {
      final pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(selectedDate ?? DateTime.now()),
      );
      if (pickedTime != null) {
        setSelectedDate(pickedTime.toDateTime(date: selectedDate));
      }
    }

    Future<void> Function()? onPickerClicked() {
      switch (mode) {
        case DateTimePickerMode.date:
          return pickDate;
        case DateTimePickerMode.time:
          return pickTime;
        case DateTimePickerMode.dateAndTime:
          return null;
      }
    }

    return IgnorePointer(
      ignoring: isDisabled,
      child: Opacity(
        opacity: isDisabled ? .6 : 1,
        child: HoverBuilder(
          onTap: onPickerClicked,
          isDisabled: mode == DateTimePickerMode.dateAndTime || isDisabled,
          builder: (isHovered, _) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 8,
              children: [
                if (header != null && header!.isNotEmpty) Text(header!, style: ATextStyle.text12),
                Container(
                  width: width,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  height: 37,
                  decoration: decoration ??
                      CommonDecorations.containerDecoration(
                        borderColor: borderColor ?? (isHovered ? hoverColor : defaultColor),
                      ),
                  child: Row(
                    spacing: 8,
                    children: [
                      Expanded(
                        child: Text(
                          selectedDate?.getFormValue(mode: mode) ?? mode.placeholderText.hardcoded,
                          style: style.text.bodyXSmall.copyWith(
                            color: selectedDate == null ? AColor.loginBorderColor : null,
                          ),
                        ),
                      ),
                      ...mode.icons.map(
                        (icon) => InkWell(
                          onTap: mode != DateTimePickerMode.dateAndTime 
                          ? null : () {
                            if (icon == Icons.calendar_month_rounded) {
                              pickDate();
                            } else {
                              pickTime();
                            }
                          },
                          child: Icon(
                            icon,
                            size: style.insets.sm,
                            color: isHovered || mode == DateTimePickerMode.dateAndTime ? hoverColor : defaultColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

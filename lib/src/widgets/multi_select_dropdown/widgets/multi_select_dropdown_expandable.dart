part of '../multi_select_dropdown_widget.dart';

class MultiSelectDropdownExpandable<T> extends HookConsumerWidget {
  const MultiSelectDropdownExpandable({
    required this.itemList,
    required this.onSelected,
    this.selectedItemList,
    super.key,
  });

  final List<T> itemList;
  final List<T>? selectedItemList;
  final void Function(T) onSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Widget buildDropdownItem(T item) {
      final isSelected = selectedItemList?.contains(item) ?? false;
      return HoverBuilder(
        onTap: () => onSelected(item),
        builder: (isHovering, _) {
          return AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            color: isHovering ? AColor.lightGreyBackground : Colors.white,
            child: Row(
              spacing: 8,
              children: [
                Checkbox(
                  value: isSelected,
                  onChanged: (_) => onSelected(item),
                  activeColor: AColor.primaryColor,
                  side: WidgetStateBorderSide.resolveWith(
                    (states) => const BorderSide(color: AColor.grayMedium),
                  ),
                ),
                Expanded(
                  child: Text(
                    item.toString(),
                    style: TextStyle(
                      color: Colors.black87,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    }

    return ConstrainedBox(
      constraints: const BoxConstraints(maxHeight: 300),
      child: Container(
        margin: const EdgeInsets.only(top: 4),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
          boxShadow: const [
            BoxShadow(
              color: Color(0x66210A38),
              blurRadius: 40,
              offset: Offset(8, 20),
              spreadRadius: -4,
            ),
          ],
        ),
        child: ListView.builder(
          shrinkWrap: true,
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: itemList.length,
          itemBuilder: (_, index) {
            return buildDropdownItem(itemList[index]);
          },
        ),
      ),
    );
  }
}

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/app_overlay.dart';
import 'package:smart_team_web/src/widgets/hover_builder.dart';

part 'widgets/multi_select_dropdown_expandable.dart';

class MultiSelectDropDown<T> extends HookConsumerWidget {
  const MultiSelectDropDown({
    required this.placeholder,
    required this.itemList,
    required this.onSelected,
    this.width,
    this.height,
    this.selectedItems,
    this.header,
    this.itemBuilder,
    super.key,
    this.decoration,
    this.headerText,
    this.headerTextStyle,
  });

  final double? width;
  final double? height;
  final String placeholder;
  final String? header;
  final List<T> itemList;
  final List<T>? selectedItems;
  final void Function(List<T>) onSelected;
  final Widget Function(T?)? itemBuilder;
  final Decoration? decoration;
  final String? headerText;
  final TextStyle? headerTextStyle;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isOpen = useState(false);
    final selectedItemsState = useState<List<T>>(selectedItems ?? []);

    void toggleDropdown() {
      Future.microtask(() {
        isOpen.value = !isOpen.value;
      });
    }

    void closeDropdown() {
      Future.microtask(() {
        isOpen.value = false;
      });
    }

    void handleItemSelection(T item) {
      final currentSelectedItems = List<T>.from(selectedItemsState.value);

      if (currentSelectedItems.contains(item)) {
        currentSelectedItems.remove(item);
      } else {
        currentSelectedItems.add(item);
      }

      selectedItemsState.value = currentSelectedItems;
      onSelected(currentSelectedItems);
    }

    void removeItem(T item) {
      final currentSelectedItems = List<T>.from(selectedItemsState.value)
        ..remove(item);
      selectedItemsState.value = currentSelectedItems;
      onSelected(currentSelectedItems);
    }

    return IgnorePointer(
      ignoring: itemList.isEmpty,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (header != null) Text(header!, style: ATextStyle.text13),
          AppOverlay(
            isOpen: isOpen.value,
            onOutsideClick: closeDropdown,
            buildOverlay: (_) => MultiSelectDropdownExpandable<T>(
              itemList: itemList,
              selectedItemList: selectedItemsState.value,
              onSelected: handleItemSelection,
            ),
            buildTrigger: (context) => InkWell(
              onTap: toggleDropdown,
              child: Container(
                width: width,
                height: height ?? 37,
                padding: const EdgeInsets.symmetric(horizontal: 8),
                decoration: decoration ??
                    CommonDecorations.containerDecoration().copyWith(
                      border: Border.all(color: Colors.grey.shade300),
                      color: Colors.white,
                    ),
                child: Row(
                  children: [
                    Expanded(
                      child: selectedItemsState.value.isEmpty
                          ? Text(
                              placeholder,
                              style: ATextStyle.text14.copyWith(
                                color: AColor.textColor,
                              ),
                            )
                          : SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                children: selectedItemsState.value.map((item) {
                                  return Container(
                                    margin: const EdgeInsets.symmetric(
                                      horizontal: 2,
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.grey[300],
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Row(
                                      spacing: 4,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          item.toString(),
                                          style: ATextStyle.text12,
                                        ),
                                        GestureDetector(
                                          onTap: () => removeItem(item),
                                          child: const Icon(
                                            Icons.close,
                                            size: 16,
                                            color: AColor.textColor,
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                }).toList(),
                              ),
                            ),
                    ),
                    const Icon(
                      Icons.keyboard_arrow_down,
                      color: AColor.textColor,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

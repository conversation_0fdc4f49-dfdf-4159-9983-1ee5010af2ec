import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/app_infinite_scroll.dart';
import 'package:smart_team_web/src/widgets/app_overlay.dart';
import 'package:smart_team_web/src/widgets/hover_builder.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

part 'widgets/new_dropdown_expandable.dart';
part 'widgets/search_field.dart';
part 'widgets/color_picker_expandable.dart';

class ANewDropDown<T> extends HookWidget {
  const ANewDropDown({
    required this.placeholder,
    required this.itemList,
    required this.onSelected,
    this.width,
    this.height,
    this.selectedItems,
    this.header,
    this.searchHint,
    this.itemBuilder,
    this.onLoadNewItems,
    super.key,
    this.decoration,
    this.enableColorPicker = false,
    this.enableSearch = false,
    this.initialColor,
    this.onColorSelected,
    this.onLoadMore,
    this.allowMultipleSelection = false,
    this.isDisabled = false,
  });

  final double? width;
  final double? height;
  final String placeholder;
  final String? header;
  final String? searchHint;
  final List<T> itemList;
  final List<T>? selectedItems;
  final void Function(T) onSelected;
  final Color? initialColor;
  final void Function(Color)? onColorSelected;
  final Widget Function(T?)? itemBuilder;
  final Future<void> Function()? onLoadNewItems;
  final Decoration? decoration;
  final bool enableColorPicker;
  final bool enableSearch;
  final bool allowMultipleSelection;
  final bool isDisabled;
  final Future<void> Function()? onLoadMore;

  @override
  Widget build(BuildContext context) {
    final isOpen = useState(false);
    final selectedColor = useState<Color>(initialColor ?? AColor.primaryColor);

    void toggleDropdown() {
      Future.microtask(() {
        isOpen.value = !isOpen.value;
      });
    }

    void closeDropdown() {
      Future.microtask(() {
        isOpen.value = false;
      });
    }

    Widget buildSelectedItems() {
      if (selectedItems == null || selectedItems!.isEmpty) {
        return Text(
          placeholder,
          style: ATextStyle.text14
              .copyWith(color: AColor.textColor.withValues(alpha: .4)),
        );
      }
      final itemsToShow = selectedItems!.take(3).toList();
      final remainingCount = selectedItems!.length - 3;
      return Row(
        children: [
          ...List.generate(itemsToShow.length, (index) {
            final item = itemsToShow[index];
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                itemBuilder?.call(item) ??
                    Text(
                      item.toString(),
                      style:
                          ATextStyle.text14.copyWith(color: AColor.textColor),
                    ),
                if (index < itemsToShow.length - 1 || remainingCount > 0)
                  Text(
                    ', ',
                    style: ATextStyle.text14.copyWith(color: AColor.textColor),
                  ),
              ],
            );
          }),
          if (remainingCount > 0)
            Text(
              '+ $remainingCount daha'.hardcoded,
              style: ATextStyle.text14.copyWith(color: AColor.textColor),
            ),
        ],
      );
    }

    return IgnorePointer(
      ignoring: isDisabled || (itemList.isEmpty && !enableColorPicker),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        spacing: 8,
        children: [
          if (header != null && header!.isNotEmpty)
            Text(header!, style: ATextStyle.text13),
          AppOverlay(
            isOpen: isOpen.value,
            onOutsideClick: closeDropdown,
            buildOverlay: (_) => enableColorPicker
                ? _ColorPickerExpandable(
                    initialColor: selectedColor.value,
                    onColorSelected: (color) {
                      Future.microtask(() {
                        selectedColor.value = color;
                        onColorSelected?.call(color);
                        isOpen.value = false;
                      });
                    },
                  )
                : _NewDropdownExpandable<T>(
                    itemList: itemList,
                    selectedItemList: selectedItems ?? [],
                    enableSearch: enableSearch,
                    searchHint: searchHint,
                    itemBuilder: itemBuilder,
                    onLoadMore: onLoadMore,
                    allowMultipleSelection: allowMultipleSelection,
                    onSelected: (item) {
                      onSelected(item);
                      if (!allowMultipleSelection) {
                        closeDropdown();
                      }
                    },
                    onLoadNewItems: onLoadNewItems == null
                        ? null
                        : () async {
                            closeDropdown();
                            await onLoadNewItems!();
                          },
                  ),
            buildTrigger: (context) => Opacity(
              opacity: isDisabled ? .4 : 1,
              child: InkWell(
                onTap: toggleDropdown,
                child: Container(
                  width: width,
                  height: height ?? 37,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  decoration: decoration ??
                      CommonDecorations.containerDecoration(
                        borderColor: AColor.loginBorderColor,
                      ).copyWith(
                        color: enableColorPicker
                            ? selectedColor.value
                            : Colors.white,
                      ),
                  child: Row(
                    children: [
                      Expanded(
                        child: enableColorPicker
                            ? const SizedBox.shrink()
                            : buildSelectedItems(),
                      ),
                      Icon(
                        Icons.keyboard_arrow_down,
                        color:
                            enableColorPicker ? AColor.white : AColor.textColor,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

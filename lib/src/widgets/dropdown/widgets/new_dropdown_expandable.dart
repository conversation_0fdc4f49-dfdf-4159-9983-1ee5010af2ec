part of '../a_new_dropdown.dart';

class _NewDropdownExpandable<T> extends HookConsumerWidget {
  const _NewDropdownExpandable({
    required this.itemList,
    required this.selectedItemList,
    required this.onSelected,
    this.onLoadNewItems,
    this.enableSearch = false,
    this.searchHint,
    this.itemBuilder,
    this.onLoadMore,
    this.allowMultipleSelection = false,
    super.key,
  });

  final List<T> itemList;
  final List<T> selectedItemList;
  final void Function(T) onSelected;
  final Future<void> Function()? onLoadNewItems;
  final bool enableSearch;
  final String? searchHint;
  final Widget Function(T)? itemBuilder;
  final Future<void> Function()? onLoadMore;
  final bool allowMultipleSelection;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final scrollController = useScrollController();
    final searchText = useState('');
    final listToShow = searchText.value.isEmpty
        ? itemList
        : itemList
            .where(
              (element) => element
                  .toString()
                  .toLowerCase()
                  .contains(searchText.value.toLowerCase()),
            )
            .toList();

    Widget buildDropdownItem(T item) {
      final isSelected = (selectedItemList.contains(item) ?? false) && !allowMultipleSelection;
      Widget buildItem({bool isHovering = false}) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          color: isSelected
              ? Colors.blue[100]
              : isHovering
                  ? Colors.blue[50]
                  : Colors.white,
          child: itemBuilder?.call(item) ??
              Text(
                item.toString(),
                style: ATextStyle.text14,
              ),
        );
      }

      if (allowMultipleSelection) {
        return buildItem();
      }
      return HoverBuilder(
        onTap: () => onSelected(item),
        builder: (isHovering, _) => buildItem(isHovering: isHovering),
      );
    }

    return Container(
      margin: const EdgeInsets.only(top: 4),
      constraints: const BoxConstraints(maxHeight: 300),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        boxShadow: const [
          BoxShadow(
            color: Color(0x66210A38),
            blurRadius: 40,
            offset: Offset(8, 20),
            spreadRadius: -4,
          ),
        ],
      ),
      child: Column(
        spacing: 4,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (enableSearch)
            Padding(
              padding: const EdgeInsets.all(8),
              child: _SearchField(
                searchHint: searchHint,
                onChanged: (value) => searchText.value = value,
              ),
            ),
          Flexible(
            child: AppInfiniteScroll(
              scrollController: scrollController,
              onLoad: onLoadMore,
              child: ListView.separated(
                controller: scrollController,
                shrinkWrap: listToShow.length < 15,
                padding: const EdgeInsets.all(8),
                itemCount: listToShow.length,
                separatorBuilder: (_, __) => const Gap(4),
                itemBuilder: (_, index) {
                  final item = listToShow[index];
                  if (allowMultipleSelection) {
                    return CheckboxListTile(
                      key: ValueKey(item.toString()),
                      value: selectedItemList.contains(item),
                      controlAffinity: ListTileControlAffinity.leading,
                      onChanged: (value) => onSelected(item),
                      shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(2),
                    ),
                      title: buildDropdownItem(item),
                    );
                  }
                  return buildDropdownItem(item);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

part of '../a_new_dropdown.dart';

class _SearchField extends StatelessWidget {
  const _SearchField({required this.onChanged, this.searchHint});

  final String? searchHint;
  final void Function(String) onChanged;

  @override
  Widget build(BuildContext context) {
    return TextField(
      onChanged: onChanged,
      decoration: InputDecoration(
        hintText: searchHint ?? ''.hardcoded,
        isDense: true,
        hintStyle: ATextStyle.text14.copyWith(
          color: AColor.inactiveColor,
        ),
        prefixIcon: const Icon(Icons.search),
        border: const OutlineInputBorder(
          borderSide: BorderSide(color: AColor.loginBorderColor),
        ),
      ),
    );
  }
}

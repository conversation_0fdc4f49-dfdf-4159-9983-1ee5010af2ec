import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/app_overlay.dart';

class ColorPickerDropDown extends StatefulHookConsumerWidget {
  const ColorPickerDropDown({
    required this.onColorSelected,
    this.selectedColor = AColor.primaryColor,
    this.width,
    this.height,
    this.header,
    super.key,
    this.decoration,
    this.isDisabled = false,
  });

  final double? width;
  final double? height;
  final Color selectedColor;
  final String? header;
  final void Function(Color) onColorSelected;
  final Decoration? decoration;
  final bool isDisabled;

  @override
  ConsumerState<ColorPickerDropDown> createState() => _ColorPickerDropDownState();
}

class _ColorPickerDropDownState extends ConsumerState<ColorPickerDropDown> {
  final MenuController _controller = MenuController();
  @override
  Widget build(BuildContext context) {
    final style = ref.watch(appStyleProvider);
    void toggleDropdown() {
      if (_controller.isOpen) {
        _controller.close();
      } else {
        _controller.open();
      }
    }

    void closeDropdown() {
      _controller.close();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      spacing: 8,
      children: [
        if (widget.header != null && widget.header!.isNotEmpty)
          Text(widget.header!, style: ATextStyle.text12),
        RawMenuAnchor(
          controller: _controller,
          overlayBuilder: (_, info) => Positioned(
            top: info.anchorRect.bottom + 6,
            left: info.anchorRect.left,
            child: Container(
              width: 400,
              height: 280,
              color: AColor.white,
              padding: EdgeInsets.all(style.insets.xs),
              child: TapRegion(
                onTapOutside: (_) => closeDropdown(),
                groupId: info.tapRegionGroupId,
                child: SizedBox(
                  width: 400,
                  child: ColorPicker(
                    pickerColor: widget.selectedColor,
                    displayThumbColor: true,
                    pickerAreaHeightPercent: .6,
                    enableAlpha: false,
                    portraitOnly: true,
                    onColorChanged: widget.onColorSelected,
                    labelTypes: const [],
                  ),
                ),
              ),
            ),
          ),
          child: InkWell(
            onTap: toggleDropdown,
            child: Container(
              width: widget.width,
              height: widget.height ?? 37,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              decoration: BoxDecoration(
                color: widget.selectedColor,
              ),
              child: const Row(
                children: [
                  Spacer(),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: AColor.white,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

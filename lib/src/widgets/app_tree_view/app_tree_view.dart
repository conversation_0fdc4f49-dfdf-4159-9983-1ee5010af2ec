import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/effects/effects.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/theme/colors.dart';

part 'widgets/main_tree_header.dart';
part 'widgets/tree_children.dart';

class AppTreeView<T> extends HookWidget {
  const AppTreeView({
    required this.subListHeaders,
    required this.subListData,
    required this.itemBuilder,
    required this.onHeaderPressed,
    this.isHeaderSelected = false,
    this.expandHeader = true,
    super.key,
  });

  final List<String> subListHeaders;
  final List<List<T>> subListData;
  final VoidCallback onHeaderPressed;
  final Widget Function(T) itemBuilder;
  final bool expandHeader;
  final bool isHeaderSelected;

  @override
  Widget build(BuildContext context) {
    final isExpanded = useState(expandHeader);

    useEffect(
      () {
        if (!context.mounted) return;
        isExpanded.value = expandHeader;
        return null;
      },
      [expandHeader],
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _MainTreeHeader(
          headerText: 'Tüm Ekip'.hardcoded,
          totalCount: 20,
          onIconPresed: () {
            isExpanded.value = !isExpanded.value;
          },
          onHeaderPressed: onHeaderPressed,
          isExpanded: isExpanded.value,
          isHeaderSelected: isHeaderSelected,
        ),
        _TreeChildren<T>(
          isExpanded: isExpanded.value,
          subListHeaders: subListHeaders,
          subListData: subListData,
          itemBuilder: itemBuilder,
        ),
      ],
    );
  }
}

part of '../app_tree_view.dart';

class _TreeChildren<T> extends HookWidget {
  const _TreeChildren({
    required this.isExpanded,
    required this.subListHeaders,
    required this.subListData,
    required this.itemBuilder,
  });

  final bool isExpanded;
  final List<String> subListHeaders;
  final List<List<T>> subListData;
  final Widget Function(T) itemBuilder;

  @override
  Widget build(BuildContext context) {
    final animationController = useAnimationController(duration: 200.ms);

    useEffect(() {
      if (isExpanded) {
        animationController.forward();
      } else {
        animationController.reverse();
      }
      return null;
    }, [isExpanded]);

    return Animate(
      autoPlay: false,
      controller: animationController,
      effects: [SizeEffect(duration: 200.ms)],
      child: Container(
        width: 200,
        margin: const EdgeInsets.only(left: 40),
        child: ListView.builder(
          itemCount: subListHeaders.length,
          shrinkWrap: true,
          itemBuilder: (context, index) {
            return ExpansionTile(
              title: Text(subListHeaders[index], style: ATextStyle.mediumRegular),
              tilePadding: EdgeInsets.zero,
              initiallyExpanded: true,
              childrenPadding: const EdgeInsets.only(left: 20),
              children: List.generate(
                subListData[index].length,
                (subIndex) => Align(
                  alignment: Alignment.centerLeft,
                  child: itemBuilder(subListData[index][subIndex]),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

part of '../app_tree_view.dart';

class _MainTreeHeader extends StatelessWidget {
  const _MainTreeHeader({
    required this.headerText,
    required this.totalCount,
    required this.onIconPresed,
    required this.onHeaderPressed,
    required this.isExpanded,
    required this.isHeaderSelected,
  });

  final String headerText;
  final int totalCount;
  final VoidCallback onIconPresed;
  final VoidCallback onHeaderPressed;
  final bool isExpanded;
  final bool isHeaderSelected;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        IconButton(
          onPressed: onIconPresed,
          icon: AnimatedRotation(
            duration: 200.ms,
            turns: isExpanded ? -0.25 : 0, // -0.25 turns = -90 degrees
            child: const Icon(
              Icons.arrow_drop_down,
            ),
          ),
        ),
        InkWell(
          onTap: onHeaderPressed,
          child: AnimatedContainer(
            duration: 200.ms,
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
            decoration: BoxDecoration(
              color: isHeaderSelected ? AColor.grayMedium : AColor.lightGreyBackground,
            ),
            child: Row(
              spacing: 4,
              children: [
                Text(
                  headerText,
                  style: ATextStyle.mediumRegular.copyWith(
                    color: isHeaderSelected ? AColor.white : null,
                  ),
                ),
                Container(
                  height: 17,
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    color: isHeaderSelected ? AColor.secondaryColor : AColor.textColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Center(
                    child: Text(
                      '$totalCount',
                      style: ATextStyle.semiSmall.copyWith(color: AColor.white, height: 1.2),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

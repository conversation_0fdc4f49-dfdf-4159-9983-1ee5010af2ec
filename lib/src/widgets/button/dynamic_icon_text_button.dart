import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/theme/colors.dart';

class DynamicIconTextButton extends HookWidget {
  const DynamicIconTextButton({
    required this.title,
    required this.onTap,
    this.prefixIcon,
    this.suffixIcon,
    this.color,
    this.textStyle,
    this.height,
    super.key,
  });

  final VoidCallback onTap;
  final String title;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final Color? color;
  final TextStyle? textStyle;
  final double? height;

  @override
  Widget build(BuildContext context) {
    final isLoading = useState(false);

    return InkWell(
      onTap: isLoading.value
          ? null
          : () async {
              isLoading.value = true;
              try {
                onTap();
              } finally {
                if (!context.mounted) return;
                isLoading.value = false;
              }
            },
      child: Container(
        constraints: BoxConstraints(minHeight: height!),
        padding: EdgeInsets.symmetric(
          horizontal: context.width * 0.006,
          vertical: context.width * 0.003,
        ),
        decoration: BoxDecoration(
          color: AColor.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: color ?? AColor.primaryColor),
        ),
        alignment: Alignment.center,
        child: isLoading.value
            ? SizedBox.square(
                dimension: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: color ?? AColor.primaryColor,
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (prefixIcon != null) ...[
                    Icon(
                      prefixIcon,
                      size: 14,
                      color: color ?? AColor.primaryColor,
                    ),
                    const SizedBox(width: 4),
                  ],
                  Flexible(
                    child: Text(
                      title,
                      style: textStyle ??
                          ATextStyle.small.copyWith(
                            color: color ?? AColor.primaryColor,
                          ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      softWrap: false,
                    ),
                  ),
                  if (suffixIcon != null) ...[
                    const SizedBox(width: 4),
                    Icon(
                      suffixIcon,
                      size: 14,
                      color: color ?? AColor.primaryColor,
                    ),
                  ],
                ],
              ),
      ),
    );
  }
}

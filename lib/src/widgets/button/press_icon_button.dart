import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class PressIconButton extends HookWidget {
  const PressIconButton({
    required this.icon,
    required this.onTap,
    this.interval = const Duration(milliseconds: 100),
    super.key,
  });
  final Widget icon;
  final VoidCallback onTap;
  final Duration interval;

  @override
  Widget build(BuildContext context) {
    final timer = useRef<Timer?>(null);

    void start() {
      onTap();
      timer.value = Timer.periodic(interval, (_) {
        onTap();
      });
    }

    void stop() {
      timer.value?.cancel();
      timer.value = null;
    }

    return InkWell(
      onTapDown: (_) => start(),
      onTapUp: (_) => stop(),
      onTapCancel: stop,
      child: icon,
    );
  }
}

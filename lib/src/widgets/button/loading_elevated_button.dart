import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/theme/colors.dart';

class LoadingElevatedButton extends HookWidget {
  const LoadingElevatedButton({
    required this.onPressed,
    required this.text,
    this.isLoading = false,
    this.width,
    this.height = 48,
    this.isDisabled = false,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
    this.textStyle,
    this.prefix,
    this.suffix,
    this.isLoadingEnabled = true,
    this.borderRadius,
    super.key,
  });

  final FutureOr<void> Function()? onPressed;
  final String text;
  final bool isLoading;
  final double? width;
  final double height;
  final bool isDisabled;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? borderColor;
  final TextStyle? textStyle;
  final Widget? prefix;
  final Widget? suffix;
  final BorderRadius? borderRadius;
  final bool isLoadingEnabled;


  @override
  Widget build(BuildContext context) {
    final isProcessing = useState(false);

    Future<void> handlePress() async {
      isProcessing.value = true;
      try {
        await onPressed?.call();
      } finally {
        if (context.mounted) isProcessing.value = false;
      }
    }

    final isButtonDisabled =
        isDisabled || isProcessing.value || onPressed == null;
    final effectiveBackgroundColor =
        backgroundColor ?? (isButtonDisabled ? AColor.grey : AColor.red);

    return SizedBox(
      width: width,
      height: height,
      child: ElevatedButton(
        onPressed: isButtonDisabled ? null : handlePress,
        style: OutlinedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius ?? BorderRadius.circular(height / 2),
            side: borderColor != null
                ? BorderSide(color: borderColor!)
                : BorderSide.none,
          ),
          foregroundColor: textColor ?? AColor.white,
          backgroundColor:
              effectiveBackgroundColor, // backgroundColor ?? AColor.primaryColor,
        ),
        child: isLoadingEnabled && (isLoading || isProcessing.value)
            ? SizedBox.square(
                dimension: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: textColor ?? AColor.white,
                ),
              )
            : SelectionContainer.disabled(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (prefix != null) prefix!,
                    Text(
                      text,
                      style: textStyle ?? ATextStyle.mediumRegular,
                    ),
                    if (suffix != null) suffix!,
                  ],
                ),
              ),
      ),
    );
  }
}

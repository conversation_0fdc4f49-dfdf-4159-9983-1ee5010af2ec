import 'package:country_phone_validator/country_phone_validator.dart';
import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/phone_code_picker/phone_code_picker.dart';

class PhonePickerFormField extends FormField<({String countryCode, String phoneNumber})> {
  PhonePickerFormField({
    required ({String countryCode, String phoneNumber}) phoneNumberInfo,
    String? headerText,
    TextStyle? headerTextStyle,
    void Function(({String countryCode, String phoneNumber}))? onPhoneNumberChanged,
    AutovalidateMode autovalidateMode = AutovalidateMode.disabled,
    bool isRequired = true,
    super.key,
  }) : super(
          initialValue: phoneNumberInfo,
          autovalidateMode: autovalidateMode,
          validator: (value) {
            if (isRequired) {
              if (value == null || value.phoneNumber.isEmpty) return 'Please enter a valid phone number';
            }
            final isValid = CountryUtils.validatePhoneNumber(value!.phoneNumber, '+${value.countryCode}');
            if (!isValid) return 'Please enter a valid phone number';
            return null;
          },
          builder: (FormFieldState<({String countryCode, String phoneNumber})> state) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              spacing: 4,
              children: [
                PhoneCodePicker(
                  phoneNumberInfo: phoneNumberInfo,
                  headerText: headerText,
                  headerTextStyle: headerTextStyle,
                  onPhoneNumberChanged: (value) {
                    state.didChange(value);
                    onPhoneNumberChanged?.call(value);
                  },
                ),
                if (state.hasError)
                  Padding(
                    padding: const EdgeInsets.only(top: 5, left: 16),
                    child: Text(
                      state.errorText ?? '',
                      style: ATextStyle.text12.copyWith(
                        color: AColor.red,
                      ),
                    ),
                  ),
              ],
            );
          },
        );
}

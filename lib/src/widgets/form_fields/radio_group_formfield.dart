import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/theme/colors.dart';


class RadioGroupFormfield<T> extends FormField<T> {
  RadioGroupFormfield({
    required List<T> items,
    AutovalidateMode autovalidateMode = AutovalidateMode.disabled,
    void Function(T)? onSelected,
    super.validator,
    super.initialValue,
    super.key,
  }) : super(
          autovalidateMode: autovalidateMode,
          builder: (FormFieldState<T> state) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              spacing: 4,
              children: [
                ...List.generate(
                  items.length,
                  (index) => RadioListTile<T>(
                    key: ValueKey(items[index].toString()),
                    groupValue: state.value,
                    value: items[index],
                    controlAffinity: ListTileControlAffinity.leading,
                    onChanged: (value) {
                      if (value == null) return;
                      state.didChange(value);
                      onSelected?.call(value);
                    },
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(2),
                    ),
                    title: Text(
                      items[index].toString(),
                      style: ATextStyle.text14,
                    ),
                  ),
                ),
                if (state.hasError)
                  Padding(
                    padding: const EdgeInsets.only(top: 5, left: 16),
                    child: Text(
                      state.errorText ?? '',
                      style: ATextStyle.text12.copyWith(
                        color: AColor.red,
                      ),
                    ),
                  ),
              ],
            );
          },
        );
}

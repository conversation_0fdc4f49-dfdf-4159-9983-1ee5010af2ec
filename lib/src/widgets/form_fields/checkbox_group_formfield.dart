import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/theme/colors.dart';


class CheckboxGroupFormField<T> extends FormField<List<T>> {
  CheckboxGroupFormField({
    required List<T> items,
    AutovalidateMode autovalidateMode = AutovalidateMode.disabled,
    void Function(List<T>)? onSelected,
    super.validator,
    super.initialValue,
    super.key,
  }) : super(
          autovalidateMode: autovalidateMode,
          builder: (FormFieldState<List<T>> state) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              spacing: 4,
              children: [
                ...List.generate(
                  items.length,
                  (index) => CheckboxListTile(
                    key: ValueKey(items[index].toString()),
                    value: state.value?.indexWhere(
                          (e) => e.toString() == items[index].toString(),
                        ) !=
                        -1,
                    controlAffinity: ListTileControlAffinity.leading,
                    onChanged: (value) {
                      if (value == null) return;
                      final newList = [...state.value ?? []];
                      if (value) {
                        newList.add(items[index]);
                      } else {
                        newList.remove(items[index]);
                      }
                      final updatedList = List<T>.from(newList);
                      state.didChange(updatedList);
                      onSelected?.call(updatedList);
                    },
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(2),
                    ),
                    title: Text(
                      items[index].toString(),
                      style: ATextStyle.text14,
                    ),
                  ),
                ),
                if (state.hasError)
                  Padding(
                    padding: const EdgeInsets.only(top: 5, left: 16),
                    child: Text(
                      state.errorText ?? '',
                      style: ATextStyle.text12.copyWith(
                        color: AColor.red,
                      ),
                    ),
                  ),
              ],
            );
          },
        );
}

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/date_time_picker/date_time_picker.dart';

class DateTimePickerFormField extends FormField<DateTime> {
  DateTimePickerFormField({
    String? header,
    double? width,
    Decoration? decoration,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? selectedDate,
    Locale locale = const Locale('en', 'EN'),
    DateTimePickerMode mode = DateTimePickerMode.date,
    AutovalidateMode autovalidateMode = AutovalidateMode.disabled,
    void Function(DateTime)? onSelected,
    super.validator,
    super.key,
  }) : super(
          autovalidateMode: autovalidateMode,
          initialValue: selectedDate,
          builder: (FormFieldState<DateTime> state) {
            return HookBuilder(
              builder: (context) {
                useEffect(
                  () {
                    if (!context.mounted) return;
                    Future.microtask(() {
                      state.didChange(selectedDate);
                    });
                    return null;
                  },
                  [selectedDate],
                );

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    DateTimePicker(
                      header: header,
                      width: width,
                      decoration: decoration,
                      startDate: startDate,
                      selectedDate: selectedDate,
                      endDate: endDate,
                      locale: locale,
                      mode: mode,
                      borderColor: state.hasError ? AColor.red : null,
                      onSelected: (date) {
                        state.didChange(date);
                        onSelected?.call(date);
                      },
                    ),
                    if (state.hasError)
                      Padding(
                        padding: const EdgeInsets.only(top: 5, left: 16),
                        child: Text(
                          state.errorText ?? '',
                          style: ATextStyle.text12.copyWith(
                            color: AColor.red,
                          ),
                        ),
                      ),
                  ],
                );
              },
            );
          },
        );
}

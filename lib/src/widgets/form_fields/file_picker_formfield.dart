import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/media_manager/media_manager.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';

class FilePickerFormField<T> extends FormField<List<T>> {
  FilePickerFormField({
    required List<T> selectedFiles,
    String? header,
    Widget? uploadedFileListView,
    bool allowMultiple = false,
    int? maxFileSize,
    List<String>? allowExtensions,
    AutovalidateMode autovalidateMode = AutovalidateMode.disabled,
    void Function(List<XFile>)? onSelected,
    super.validator,
    super.initialValue,
    super.key,
  }) : super(
          autovalidateMode: autovalidateMode,
          builder: (FormFieldState<List<T>> state) {
            return HookConsumer(
              builder: (context, ref, child) {
                useEffect(() {
                  Future.microtask(() {
                    state.didChange(selectedFiles);
                  });
                  return null;
                }, [selectedFiles],
                );

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  spacing: 4,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 12,
                      children: [
                        if (header != null)
                          Text(header, style: ATextStyle.text12),
                        if (state.value != null &&
                            state.value!.isNotEmpty &&
                            uploadedFileListView != null)
                          uploadedFileListView,
                        LoadingElevatedButton(
                          height: 35,
                          text: 'Dosya ekle'.hardcoded,
                          borderRadius: BorderRadius.circular(4),
                          backgroundColor: AColor.white,
                          borderColor: AColor.blue,
                          textColor: AColor.blue,
                          prefix: const Icon(
                            Icons.upload,
                            color: AColor.blue,
                          ),
                          onPressed: () async {
                            final pickedFiles =
                                await ref.read(mediaManagerProvider).pickFile(
                                      allowMultiple: allowMultiple,
                                      allowExtensions: allowExtensions,
                                      maxFileSize: maxFileSize,
                                    );
                            if (pickedFiles != null) {
                              onSelected?.call(pickedFiles);
                            }
                          },
                        ),
                      ],
                    ),
                    if (state.hasError)
                      Padding(
                        padding: const EdgeInsets.only(top: 5, left: 16),
                        child: Text(
                          state.errorText ?? '',
                          style: ATextStyle.text12.copyWith(
                            color: AColor.red,
                          ),
                        ),
                      ),
                  ],
                );
              },
            );
          },
        );
}

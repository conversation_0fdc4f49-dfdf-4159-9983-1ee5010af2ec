import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/new_dropdown/new_dropdown.dart';

class ANewDropdownFormField<T> extends FormField<List<T>> {
  ANewDropdownFormField({
    required String placeholder,
    required List<T> itemList,
    required void Function(T) onSelected,
    super.key,
    double? width,
    double? height,
    String? header,
    String? searchHint,
    List<T>? selectedItems,
    Widget Function(T?)? itemBuilder,
    Future<void> Function()? onLoadNewItems,
    Decoration? decoration,
    bool enableColorPicker = false,
    bool enableSearch = false,
    Color? initialColor,
    void Function(Color)? onColorSelected,
    Future<void> Function()? onLoadMore,
    super.onSaved,
    String? Function(List<T>?)? validator,
    bool allowMultipleSelection = false,
    bool isDisabled = false,
    AutovalidateMode autovalidateMode = AutovalidateMode.disabled,
  }) : super(
          autovalidateMode: autovalidateMode,
          initialValue: (selectedItems != null && selectedItems.isNotEmpty)
              ? selectedItems
              : null,
          validator: validator ??
              (value) {
                return value == null || value.isEmpty
                    ? 'Please select an option'.hardcoded
                    : null;
              },
          builder: (FormFieldState<List<T>> state) {
            return HookBuilder(
              builder: (context) {
                useEffect(
                  () {
                    if (state.context.mounted) {
                      Future.microtask(() {
                        state.didChange(selectedItems);
                      });
                    }
                    return null;
                  },
                  [selectedItems],
                );

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    NewDropDown<T>(
                      isDisabled: isDisabled,
                      placeholder: placeholder,
                      header: header,
                      searchHint: searchHint,
                      width: width,
                      height: height,
                      itemList: itemList,
                      selectedItems: state.value,
                      itemBuilder: itemBuilder,
                      onLoadNewItems: onLoadNewItems,
                      decoration: decoration,
                      enableColorPicker: enableColorPicker,
                      enableSearch: enableSearch,
                      initialColor: initialColor,
                      onColorSelected: onColorSelected,
                      onLoadMore: onLoadMore,
                      allowMultipleSelection: allowMultipleSelection,
                      borderColor: state.hasError ? AColor.red : null,
                      onSelected: (selectedItem) {
                        Future.microtask(() {
                          onSelected(selectedItem);
                        });
                      },
                    ),
                    if (state.hasError)
                      Padding(
                        padding: const EdgeInsets.only(top: 5, left: 16),
                        child: Text(
                          state.errorText ?? '',
                          style: ATextStyle.text12.copyWith(
                            color: AColor.red,
                          ),
                        ),
                      ),
                  ],
                );
              },
            );
          },
        );
}

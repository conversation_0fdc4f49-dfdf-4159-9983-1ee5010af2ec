import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/utils/dt_format.dart';
import 'package:smart_team_web/src/shared/utils/dt_util.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

class AppDateTimeRangePicker extends StatefulWidget {
  const AppDateTimeRangePicker({
    super.key,
    this.onDateRangeSelected,
    this.initialDateRange,
  });

  final void Function(DateTimeRange)? onDateRangeSelected;
  final DateTimeRange? initialDateRange;

  @override
  State<AppDateTimeRangePicker> createState() => _AppDateTimeRangePickerState();
}

class _AppDateTimeRangePickerState extends State<AppDateTimeRangePicker> {
  late DateTimeRange _selectedDateRange;

  @override
  void initState() {
    super.initState();
    _selectedDateRange = widget.initialDateRange ??
        DateTimeRange(
          start: DateTime(2024, 1, 1),
          end: DateTime(2025, 12, 31),
        );
  }

  Future<void> _showDateRangePicker() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime(2025, 12, 31),
      initialDateRange: _selectedDateRange,
      saveText: context.tr.pick,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            appBarTheme: const AppBarTheme(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
          child: Center(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: SizedBox(
                width: 400,
                height: 600,
                child: child,
              ),
            ),
          ),
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
      widget.onDateRangeSelected?.call(picked);
    }
  }

  String _formatDateRange() {
    return '${DTUtil.dtToString(
      _selectedDateRange.start,
      format: DTFormat.dayMonthYear,
    )} - ${DTUtil.dtToString(
      _selectedDateRange.end,
      format: DTFormat.dayMonthYear,
    )}';
  }

  @override
  Widget build(BuildContext context) {
    return SelectionContainer.disabled(
      child: InkWell(
        onTap: _showDateRangePicker,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            spacing: 8,
            children: [
              const Icon(Icons.calendar_today, size: 16),
              Text(
                _formatDateRange(),
                style: ATextStyle.text14,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

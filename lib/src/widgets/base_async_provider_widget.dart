import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/error/domain/alert_model.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/toast/toast_manager.dart';

/// A widget that handles the different states of an [AsyncValue].
///
/// This widget provides a convenient way to handle loading, error and data states
/// of an async operation, with customizable behavior for each state.
class BaseAsyncProviderWidget<T> extends HookConsumerWidget {
  const BaseAsyncProviderWidget({
    required this.value,
    required this.builder,
    this.loadingWidget,
    this.errorBuilder,
    this.skipError = true,
    this.showToastOnError = true,
    this.skipLoadingOnRefresh = true,
    super.key,
  });

  /// The [AsyncValue] to be handled by this widget
  final AsyncValue<T> value;

  /// Custom widget to show during loading state. If null, shows a centered [CircularProgressIndicator]
  final Widget? loadingWidget;

  /// Builder function that creates the widget when data is available
  final Widget Function(T) builder;

  /// Optional builder for custom error widget. If null, returns an empty [SizedBox]
  final Widget Function(Object error, StackTrace stackTrace)? errorBuilder;

  /// Whether to show toast notifications on error. Defaults to true
  final bool showToastOnError;

  /// Whether to skip error state and keep showing previous data. Defaults to true
  final bool skipError;

  /// Whether to skip loading state during refresh/reload. Defaults to true
  final bool skipLoadingOnRefresh;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return value.when(
      skipError: skipError,
      skipLoadingOnRefresh: skipLoadingOnRefresh,
      skipLoadingOnReload: skipLoadingOnRefresh,
      error: (error, stacktrace) {
        debugPrint('<debug> error: $error');
        debugPrint('<debug> stacktrace: $stacktrace');
        // Show error toast if enabled
        if (showToastOnError) {
          final alertModel = AlertModel.fromExceptionObject(exception: error);
          ref.read(toastManagerProvider).showToastError(alertModel);
        }
        // Use custom error builder if provided, otherwise show empty widget
        if (errorBuilder != null) {
          return errorBuilder!(error, stacktrace);
        }
        return const SizedBox();
      },
      loading: () =>
          loadingWidget ??
          const Center(
            child: CircularProgressIndicator(
              color: AColor.primaryColor,
            ),
          ),
      data: builder,
    );
  }
}

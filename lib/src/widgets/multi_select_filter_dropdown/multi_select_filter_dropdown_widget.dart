import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/home/<USER>/team_member.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/shared/utils/dt_format.dart';
import 'package:smart_team_web/src/shared/utils/dt_util.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/app_overlay.dart';
import 'package:smart_team_web/src/widgets/hover_builder.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

part 'widgets/multi_select_filter_dropdown_expandable.dart';
part 'widgets/calendar_filter_dropdown_expandable.dart';

class MultiSelectFilterDropDown<T> extends HookConsumerWidget {
  const MultiSelectFilterDropDown({
    required this.placeholder,
    required this.itemList,
    required this.onSelected,
    this.width,
    this.height,
    this.selectedItems,
    this.header,
    this.itemBuilder,
    this.isMultiSelect = true,
    super.key,
    this.decoration,
  });

  final double? width;
  final double? height;
  final String placeholder;
  final String? header;
  final List<T> itemList;
  final List<T>? selectedItems;
  final void Function(List<T>) onSelected;
  final Widget Function(T?)? itemBuilder;
  final bool isMultiSelect;
  final Decoration? decoration;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final widgetId = useMemoized(() => UniqueKey().toString(), []);
    final layerLink = useMemoized(LayerLink.new, []);

    final isOpen = useState(false);
    final selectedItemsState = useState<List<T>>(selectedItems ?? []);

    useEffect(
      () {
        if (selectedItems != null) {
          selectedItemsState.value = selectedItems!;
        }
        return null;
      },
      [selectedItems],
    );

    void toggleDropdown() {
      isOpen.value = !isOpen.value;
    }

    void closeDropdown() {
      isOpen.value = false;
    }

    void handleItemSelection(T item) {
      if (isMultiSelect) {
        final currentSelectedItems = List<T>.from(selectedItemsState.value);
        if (currentSelectedItems.contains(item)) {
          currentSelectedItems.remove(item);
        } else {
          currentSelectedItems.add(item);
        }
        selectedItemsState.value = currentSelectedItems;
        onSelected(currentSelectedItems);
      } else {
        selectedItemsState.value = [item];
        onSelected([item]);
        closeDropdown();
      }
    }

    void removeItem(T item) {
      final currentSelectedItems = List<T>.from(selectedItemsState.value)
        ..remove(item);
      selectedItemsState.value = currentSelectedItems;
      onSelected(currentSelectedItems);
    }

    return IgnorePointer(
      ignoring: itemList.isEmpty,
      child: CompositedTransformTarget(
        link: layerLink,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (header != null) Text(header!, style: ATextStyle.text12),
            AppOverlay(
              isOpen: isOpen.value,
              onOutsideClick: closeDropdown,
              buildOverlay: (_) => CompositedTransformFollower(
                link: layerLink,
                showWhenUnlinked: false,
                offset: Offset(0, height ?? 37),
                child: MultiSelectFilterDropdownExpandable<T>(
                  widgetId: widgetId,
                  itemList: itemList,
                  selectedItemList: selectedItemsState.value,
                  onItemToggle: handleItemSelection,
                  onSelectionChange: (newSelection) {
                    selectedItemsState.value = newSelection;
                    onSelected(newSelection);
                  },
                  onSave: closeDropdown,
                ),
              ),
              buildTrigger: (context) => InkWell(
                onTap: toggleDropdown,
                child: Container(
                  width: width,
                  height: height ?? 37,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: decoration ??
                      CommonDecorations.containerDecoration().copyWith(
                        border: Border.all(color: Colors.grey.shade300),
                        color: Colors.white,
                      ),
                  child: Row(
                    children: [
                      Expanded(
                        child: selectedItemsState.value.isEmpty
                            ? Text(
                                placeholder,
                                style: ATextStyle.text14.copyWith(
                                  color: AColor.textColor,
                                ),
                              )
                            : SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  children:
                                      selectedItemsState.value.map((item) {
                                    return Container(
                                      margin: const EdgeInsets.symmetric(
                                        horizontal: 2,
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.grey[300],
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Flexible(
                                            child: Text(
                                              item.toString(),
                                              style: ATextStyle.text12,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                          GestureDetector(
                                            onTap: () => removeItem(item),
                                            child: const Icon(
                                              Icons.close,
                                              size: 16,
                                              color: AColor.textColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ),
                      ),
                      const Icon(
                        Icons.keyboard_arrow_down,
                        color: AColor.textColor,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

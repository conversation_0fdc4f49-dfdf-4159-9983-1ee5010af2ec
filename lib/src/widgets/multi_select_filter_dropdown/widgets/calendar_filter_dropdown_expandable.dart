part of '../multi_select_filter_dropdown_widget.dart';

class CalendarFilterDropdownExpandable extends HookConsumerWidget {
  const CalendarFilterDropdownExpandable({
    required this.onSelectionChange,
    required this.onSave,
    super.key,
  });

  final void Function(List<DateTime>) onSelectionChange;
  final VoidCallback onSave;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final startDate = useState<DateTime?>(null);
    final endDate = useState<DateTime?>(null);

    void updateSelection() {
      final dates = <DateTime>[];
      if (startDate.value != null) dates.add(startDate.value!);
      if (endDate.value != null) dates.add(endDate.value!);
      onSelectionChange(dates);
    }

    Future<void> pickDate(
      DateTime? initial,
      void Function(DateTime?) setter,
    ) async {
      final now = DateTime.now();
      final picked = await showDatePicker(
        context: context,
        initialDate: initial ?? now,
        firstDate: DateTime(2000),
        lastDate: DateTime(2100),
      );
      setter(picked);
      updateSelection();
    }

    void handleClear() {
      startDate.value = null;
      endDate.value = null;
      onSelectionChange([]);
    }

    void handleSave() {
      updateSelection();
      onSave();
    }

    final fields = [
      {
        'label': 'Başlangıç Tarihi'.hardcoded,
        'value': startDate.value,
        'setter': (DateTime? d) => startDate.value = d,
      },
      {
        'label': 'Bitiş Tarihi'.hardcoded,
        'value': endDate.value,
        'setter': (DateTime? d) => endDate.value = d,
      },
    ];

    return RepaintBoundary(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxHeight: 300),
        child: Container(
          margin: const EdgeInsets.only(top: 4),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                child: Column(
                  spacing: 16,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: fields.map((field) {
                    final label = field['label']! as String;
                    final dateVal = field['value'] as DateTime?;
                    final setter = field['setter']! as void Function(DateTime?);

                    return Column(
                      spacing: 4,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextWidget(
                          label.hardcoded,
                          style: ATextStyle.text12,
                        ),
                        Container(
                          decoration: CommonDecorations.textFieldDecoration(
                            borderColor: Colors.grey.shade300,
                          ),
                          child: TextField(
                            readOnly: true,
                            controller: TextEditingController(
                              text: dateVal == null
                                  ? ''
                                  : DTUtil.dtToString(
                                      dateVal,
                                      format: DTFormat.dayMonthYear,
                                    ),
                            ),
                            decoration: InputDecoration(
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                              filled: true,
                              fillColor: AColor.white,
                              isDense: true,
                              hintText: context.tr.select,
                              prefixIcon: const Padding(
                                padding: EdgeInsets.all(8),
                                child: Icon(Icons.calendar_today),
                              ),
                              prefixIconConstraints: const BoxConstraints(
                                minWidth: 24,
                                minHeight: 24,
                              ),
                              border: const OutlineInputBorder(
                                borderSide: BorderSide.none,
                              ),
                            ),
                            onTap: () => pickDate(dateVal, setter),
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Row(
                  spacing: 16,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: handleClear,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: AColor.white,
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: AColor.primaryColor),
                        ),
                        child: Text(
                          'Temizle'.hardcoded,
                          style: ATextStyle.text14
                              .copyWith(color: AColor.primaryColor),
                        ),
                      ),
                    ),
                    InkWell(
                      onTap: handleSave,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: AColor.primaryColor,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          context.tr.save,
                          style:
                              ATextStyle.text14.copyWith(color: AColor.white),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/calendar/presentation/calendar_view.dart';

import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/shared/utils/dt_format.dart';
import 'package:smart_team_web/src/shared/utils/dt_util.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/app_overlay.dart';
import 'package:smart_team_web/src/widgets/multi_select_filter_dropdown/multi_select_filter_dropdown_widget.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

part 'widgets/date_range_dropdown_expandable.dart';

class DateRangeDropDown extends HookConsumerWidget {
  const DateRangeDropDown({
    required this.placeholder,
    required this.onSelected,
    this.width,
    this.height,
    this.selectedDates = const [],
    this.header,
    this.decoration,
    this.singleDate = false,
    super.key,
  });

  final double? width;
  final double? height;
  final String placeholder;
  final String? header;
  final List<DateTime> selectedDates;
  final void Function(List<DateTime>) onSelected;
  final Decoration? decoration;

  final bool singleDate;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final layerLink = useMemoized(LayerLink.new, []);
    final isOpen = useState(false);
    final triggerWidth = useState<double?>(null);

    void toggleDropdown() => isOpen.value = !isOpen.value;
    void closeDropdown() => isOpen.value = false;

    final selectedDatesState = useState<List<DateTime>>(selectedDates);

    final singleDateState = useState<DateTime>(
      selectedDates.isNotEmpty ? selectedDates.first : DateTime.now(),
    );
    String getDisplayText() {
      if (singleDate) {
        return DTUtil.dtToString(
          singleDateState.value,
          format: DTFormat.dayMonthYear,
        );
      }
      if (selectedDatesState.value.isEmpty) {
        return placeholder;
      }
      final texts = selectedDatesState.value
          .map((d) => DTUtil.dtToString(d, format: DTFormat.dayMonthYear))
          .toList();
      if (texts.length == 1) return texts[0];
      return '${texts[0]} – ${texts[1]}';
    }

    return CompositedTransformTarget(
      link: layerLink,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (header != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(header!, style: ATextStyle.text13),
            ),
          LayoutBuilder(
            builder: (context, constraints) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (triggerWidth.value == null) {
                  triggerWidth.value = constraints.maxWidth;
                }
              });

              return AppOverlay(
                isOpen: isOpen.value,
                onOutsideClick: closeDropdown,
                buildTrigger: (_) => InkWell(
                  onTap: toggleDropdown,
                  child: Container(
                    width: width ?? constraints.maxWidth,
                    height: height ?? 37,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: decoration ??
                        CommonDecorations.containerDecoration().copyWith(
                          border: Border.all(color: Colors.grey.shade300),
                          color: Colors.white,
                        ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            getDisplayText(),
                            style: ATextStyle.text14.copyWith(
                              color: AColor.textColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const Icon(
                          Icons.keyboard_arrow_down,
                          color: AColor.textColor,
                        ),
                      ],
                    ),
                  ),
                ),
                buildOverlay: (_) {
                  if (singleDate) {
                    return CompositedTransformFollower(
                      link: layerLink,
                      showWhenUnlinked: false,
                      offset: Offset(0, height ?? 37),
                      child: _SingleDateCalendar(
                        width: triggerWidth.value ?? 300,
                        height: height ?? 300,
                        initialDate: singleDateState.value,
                        onDateSelected: (d) {
                          singleDateState.value = d;
                          onSelected([d]);
                          closeDropdown();
                        },
                      ),
                    );
                  }
                  return CompositedTransformFollower(
                    link: layerLink,
                    showWhenUnlinked: false,
                    offset: Offset(0, height ?? 37),
                    child: CalendarFilterDropdownExpandable(
                      onSelectionChange: (newRange) {
                        selectedDatesState.value = newRange;
                        onSelected(newRange);
                      },
                      onSave: closeDropdown,
                    ),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }
}

class _SingleDateCalendar extends HookConsumerWidget {
  const _SingleDateCalendar({
    required this.width,
    required this.height,
    required this.initialDate,
    required this.onDateSelected,
  });

  final double width;
  final double height;
  final DateTime initialDate;
  final void Function(DateTime) onDateSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final maxW = constraints.maxWidth;
        final popupW = min(width, maxW);

        return Material(
          elevation: 4,
          borderRadius: BorderRadius.circular(4),
          child: SizedBox(
            width: popupW,
            height: height,
            child: _DateRangeDropdownExpandable(
              initialDate: initialDate,
              onDateSelected: onDateSelected,
            ),
          ),
        );
      },
    );
  }
}

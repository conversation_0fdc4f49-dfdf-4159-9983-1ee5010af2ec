import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:smart_team_common/smart_team_common.dart';

import 'package:smart_team_web/src/home/<USER>/filtered_team_provider.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/app_overlay.dart';
import 'package:smart_team_web/src/widgets/hover_builder.dart';

part 'widgets/three_dot_menu.dart';
part 'widgets/search_tree.dart';
part 'widgets/tree_header.dart';
part 'widgets/team_member.dart';
part 'widgets/member_tree.dart';

class EmployeeTreeView extends HookConsumerWidget {
  const EmployeeTreeView({
    required this.selectedMembers,
    this.onTeamMemberSelected,
    this.allowMultipleSelection = false,
    super.key,
  });

  final List<TeamMemberModel> selectedMembers;
  final void Function(List<TeamMemberModel>)? onTeamMemberSelected;
  final bool allowMultipleSelection;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final teamsAsync = ref.watch(filteredTeamsProvider);

    final controller = useTreeSliverController();
    final showNames = useState(true);

    return SizedBox(
      width: double.infinity,
      child: Column(
        spacing: 16,
        children: [
          Row(
            spacing: 4,
            children: [
              _ThreeDotMenu(
                showMemberNames: showNames.value,
                onShowMemberNamesChanged: ({showMemberNames = true}) =>
                    showNames.value = showMemberNames,
                onExpandAll: controller.expandAll,
                onCollapseAll: controller.collapseAll,
              ),
              Expanded(
                child: _SearchTree(
                  onChanged: (v) {
                    ref.read(queryProvider.notifier).state = v;
                  },
                ),
              ),
            ],
          ),
          Expanded(
            child: teamsAsync.when(
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (e, st) => const SizedBox.shrink(),
              data: (teams) => _MemberTree(
                teams: teams,
                showMemberNames: showNames.value,
                controller: controller,
                onTeamMemberSelected: onTeamMemberSelected,
                allowMultipleSelection: allowMultipleSelection,
                selectedMembers: selectedMembers,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

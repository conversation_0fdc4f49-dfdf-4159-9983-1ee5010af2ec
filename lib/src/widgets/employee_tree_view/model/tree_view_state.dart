import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:smart_team_web/src/home/<USER>/team_member.dart';

part 'tree_view_state.freezed.dart';

@freezed
abstract class TreeViewState with _$TreeViewState {
  const factory TreeViewState({
    required List<WebTeamMemberModel> members,
    required List<WebTeamMemberModel> filteredMembers,
    required WebTeamMemberModel? selectedMember,
    @Default(false) bool showMemberNames,
    @Default('') String searchText,
  }) = _TreeViewState;
}

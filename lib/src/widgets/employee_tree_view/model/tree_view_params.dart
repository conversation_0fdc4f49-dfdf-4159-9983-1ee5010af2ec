import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:smart_team_web/src/home/<USER>/team_member.dart';

part 'tree_view_params.freezed.dart';

@immutable
@freezed
abstract class TreeViewParams with _$TreeViewParams {
  const factory TreeViewParams({
    required String routeName,
    required List<WebTeamMemberModel> members,
  }) = _TreeViewParams;

  const TreeViewParams._();

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TreeViewParams && other.routeName == routeName;
  }

  @override
  int get hashCode => routeName.hashCode;
}

part of '../employee_tree_view.dart';

class _SearchTree extends HookConsumerWidget {
  const _SearchTree({required this.onChanged});
  final void Function(String) onChanged;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return TextField(
      style: ATextStyle.mediumRegular,
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        filled: true,
        isDense: true,
        fillColor: AColor.white,
        hintText: 'Search members...'.hardcoded,
        prefixIcon: const Padding(
          padding: EdgeInsets.all(8),
          child: Icon(Icons.search),
        ),
        prefixIconConstraints: const BoxConstraints(
          minWidth: 24,
          minHeight: 24,
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide.none,
        ),
      ),
      onChanged: onChanged,
    );
  }
}

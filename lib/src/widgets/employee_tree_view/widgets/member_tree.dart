part of '../employee_tree_view.dart';

class _MemberTree extends StatefulHookWidget {
  const _MemberTree({
    required this.teams,
    required this.showMemberNames,
    required this.controller,
    this.onTeamMemberSelected,
    this.allowMultipleSelection = false,
    this.selectedMembers = const [],
  });

  final List<TeamsWithMembersModel> teams;
  final bool showMemberNames;
  final TreeSliverController controller;
  final void Function(List<TeamMemberModel>)? onTeamMemberSelected;
  final bool allowMultipleSelection;
  final List<TeamMemberModel> selectedMembers;

  @override
  State<_MemberTree> createState() => __MemberTreeState();
}

class __MemberTreeState extends State<_MemberTree> {
  late TreeSliverNode<Object?> _rootNode;
  late List<TreeSliverNode<Object?>> _tree;

  @override
  void initState() {
    super.initState();
    _rebuildTree();
  }

  @override
  void didUpdateWidget(covariant _MemberTree old) {
    super.didUpdateWidget(old);
    if (old.teams != widget.teams ||
        old.selectedMembers != widget.selectedMembers ||
        old.showMemberNames != widget.showMemberNames) {
      _rebuildTree();
    }
  }

  void _rebuildTree() {
    final branchNodes = widget.teams.map((team) {
      final memberNodes = team.userList
          .map<TreeSliverNode<Object?>>(
            TreeSliverNode<Object?>.new,
          )
          .toList();

      return TreeSliverNode<Object?>(
        team.teamName,
        expanded: true,
        children: memberNodes,
      );
    }).toList();

    _rootNode = TreeSliverNode<Object?>(
      'Tüm Ekip'.hardcoded,
      expanded: true,
      children: branchNodes,
    );

    _tree = <TreeSliverNode<Object?>>[_rootNode];
    setState(() {}); // trigger rebuild
  }

  void _handleSelect(TeamMemberModel m) {
    if (widget.allowMultipleSelection) {
      final next = widget.selectedMembers.contains(m)
          ? widget.selectedMembers.where((x) => x != m).toList()
          : [...widget.selectedMembers, m];
      widget.onTeamMemberSelected?.call(next);
    } else {
      widget.onTeamMemberSelected?.call([m]);
    }
  }

  bool get _allSelected =>
      widget.allowMultipleSelection &&
      widget.selectedMembers.length ==
          widget.teams.expand((t) => t.userList).length;

  void _toggleAll() {
    if (!widget.allowMultipleSelection) return;
    if (_allSelected) {
      widget.onTeamMemberSelected?.call([]);
    } else {
      widget.onTeamMemberSelected?.call(
        widget.teams.expand((t) => t.userList).toList(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: [
        TreeSliver<Object?>(
          tree: _tree,
          controller: widget.controller,
          indentation: TreeSliverIndentationType.custom(30),
          treeNodeBuilder: (ctx, node, animation) {
            // Root
            if (node == _rootNode) {
              return _TreeHeader(
                controller: widget.controller,
                node: node,
                totalItemCount: widget.teams.expand((t) => t.userList).length,
                isSelected: widget.selectedMembers.isEmpty,
                onTap: () => widget.onTeamMemberSelected?.call([]),
                allowMultipleSelection: widget.allowMultipleSelection,
                isAllSelected: _allSelected,
                onToggleAllSelection: _toggleAll,
              );
            }

            // Team branch
            if (node.content is String) {
              return GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () => widget.controller.toggleNode(node),
                child: TreeSliver.defaultTreeNodeBuilder(ctx, node, animation),
              );
            }

            // Leaf: TeamMemberModel
            final member = node.content! as TeamMemberModel;
            final isSel = widget.selectedMembers.contains(member);
            return _TeamMember(
              member: member,
              isSelected: isSel,
              showMemberNames: widget.showMemberNames,
              allowMultipleSelection: widget.allowMultipleSelection,
              onTap: () => _handleSelect(member),
              onToggleSelection: () => _handleSelect(member),
            );
          },
        ),
      ],
    );
  }
}

part of '../employee_tree_view.dart';

class _TeamMember extends StatelessWidget {
  const _TeamMember({
    required this.member,
    required this.isSelected,
    required this.onTap,
    required this.showMemberNames,
    this.allowMultipleSelection = false,
    this.onToggleSelection,
  });

  final TeamMemberModel member;
  final bool isSelected;
  final VoidCallback onTap;
  final bool showMemberNames;
  final bool allowMultipleSelection;
  final VoidCallback? onToggleSelection;

  @override
  Widget build(BuildContext context) {
    final isMemberActive = member.isActive ?? true;
    final iconColor = isMemberActive ? AColor.green : AColor.red;

    return HoverBuilder(
      onTap: allowMultipleSelection ? onToggleSelection ?? () {} : onTap,
      builder: (hovered, _) {
        final bg = hovered || isSelected;
        return Align(
          alignment: Alignment.centerLeft,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (allowMultipleSelection)
                Checkbox(
                  value: isSelected,
                  onChanged: (_) => onToggleSelection?.call(),
                  activeColor: AColor.primaryColor,
                  checkColor: AColor.white,
                  side: WidgetStateBorderSide.resolveWith(
                    (_) => const BorderSide(color: AColor.grayMedium),
                  ),
                ),
              Flexible(
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  color: bg ? AColor.grayMedium : AColor.lightGreyBackground,
                  padding: const EdgeInsets.all(4),
                  child: Row(
                    spacing: 4,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.wifi,
                        color: iconColor,
                        size: 20,
                      ),
                      Flexible(
                        child: AnimatedDefaultTextStyle(
                          duration: const Duration(milliseconds: 150),
                          style: bg
                              ? ATextStyle.mediumRegular
                                  .copyWith(color: AColor.white)
                              : ATextStyle.mediumRegular,
                          child: Text(
                            showMemberNames ? _displayName() : _displayPhone(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String _displayName() {
    final name = member.userName?.trim() ?? '';
    return name.isNotEmpty ? name : 'Unknown User'.hardcoded;
  }

  String _displayPhone() {
    final phone = member.phone;
    if (phone?.isNotEmpty ?? false) {
      return phone!;
    }
    return 'No Phone'.hardcoded;
  }
}

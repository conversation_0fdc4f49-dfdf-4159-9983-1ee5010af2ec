part of '../employee_tree_view.dart';

class _TreeHeader extends StatelessWidget {
  const _TreeHeader({
    required this.controller,
    required this.node,
    required this.totalItemCount,
    required this.isSelected,
    required this.onTap,
    this.allowMultipleSelection = false,
    this.isAllSelected = false,
    this.onToggleAllSelection,
  });

  final TreeSliverController controller;
  final TreeSliverNode<dynamic> node;
  final int totalItemCount;
  final bool isSelected;
  final VoidCallback onTap;
  final bool allowMultipleSelection;
  final bool isAllSelected;
  final VoidCallback? onToggleAllSelection;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        IconButton(
          onPressed: () => controller.toggleNode(node),
          icon: AnimatedRotation(
            duration: 200.ms,
            turns: node.isExpanded ? -0.25 : 0, // -0.25 turns = -90 degrees
            child: const Icon(
              Icons.arrow_drop_down,
            ),
          ),
        ),
        if (allowMultipleSelection)
          Checkbox(
            value: isAllSelected,
            onChanged: (_) {
              onToggleAllSelection?.call();
            },
            activeColor: AColor.primaryColor,
            checkColor: AColor.white,
            side: WidgetStateBorderSide.resolveWith(
              (states) => const BorderSide(color: AColor.grayMedium),
            ),
          ),
        InkWell(
          onTap: onTap,
          child: AnimatedContainer(
            duration: 200.ms,
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
            decoration: BoxDecoration(
              color:
                  isSelected ? AColor.grayMedium : AColor.lightGreyBackground,
            ),
            child: Row(
              spacing: 4,
              children: [
                Text(
                  node.content.toString(),
                  style: ATextStyle.mediumRegular.copyWith(
                    color: isSelected ? AColor.white : null,
                  ),
                ),
                Container(
                  height: 17,
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    color:
                        isSelected ? AColor.secondaryColor : AColor.textColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Center(
                    child: Text(
                      '$totalItemCount',
                      style: ATextStyle.semiSmall
                          .copyWith(color: AColor.white, height: 1.2),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

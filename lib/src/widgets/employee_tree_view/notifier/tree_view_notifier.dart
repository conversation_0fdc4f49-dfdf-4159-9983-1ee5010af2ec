import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/widgets/employee_tree_view/model/tree_view_params.dart';
import 'package:smart_team_web/src/widgets/employee_tree_view/model/tree_view_state.dart';

final treeViewProvider = AutoDisposeFamilyNotifierProvider<TreeViewNotifier, TreeViewState, TreeViewParams>(
  TreeViewNotifier.new,
);

class TreeViewNotifier extends AutoDisposeFamilyNotifier<TreeViewState, TreeViewParams> {
  @override
  TreeViewState build(TreeViewParams params) {
    return TreeViewState(
      members: params.members,
      filteredMembers: params.members,
      selectedMember: null,
    );
  }
}

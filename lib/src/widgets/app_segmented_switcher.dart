import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/theme/colors.dart';

class AppSegmentedSwitcher extends HookConsumerWidget {
  const AppSegmentedSwitcher({
    required this.tabs,
    this.tabStyle,
    this.onTabChange,
    this.currentIndex,
    this.width,
    this.prefixWidgets,
    this.suffixWidgets,
    super.key,
  }) : assert(tabs.length > 1, 'AppSegmentedSwitcher requires at least 2 tabs');

  final List<String> tabs;
  final TextStyle? tabStyle;
  final void Function(int)? onTabChange;
  final int? currentIndex;
  final double? width;
  final List<Widget?>? prefixWidgets;
  final List<Widget?>? suffixWidgets;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = useState(currentIndex ?? 0);
    final style = ref.watch(appStyleProvider);

    useEffect(
      () {
        if (currentIndex == null) return;
        selectedIndex.value = currentIndex!;
        return null;
      },
      [currentIndex],
    );

    Widget buildText(int index) {
      final currentTab = tabs[index];
      final isSelected = selectedIndex.value == index;
      return InkWell(
        onTap: () {
          selectedIndex.value = index;
          onTabChange?.call(selectedIndex.value);
        },
        child: Row(
          children: [
            if (prefixWidgets != null && prefixWidgets![index] != null)
              prefixWidgets![index]!
            else
              const SizedBox.shrink(),
            Expanded(
              child: Center(
                child: AnimatedDefaultTextStyle(
                  style: (tabStyle ?? style.text.bodySmall).copyWith(
                    fontWeight: isSelected ? FontWeight.w700 : FontWeight.w400,
                  ),
                  duration: style.times.fast,
                  child: Text(
                    currentTab,
                    style: style.text.bodySmall,
                  ),
                ),
              ),
            ),
            if (suffixWidgets != null && suffixWidgets![index] != null)
              suffixWidgets![index]!
            else
              const SizedBox.shrink(),
          ],
        ),
      );
    }

    return Container(
      width: width ?? double.infinity,
      height: 50,
      decoration: BoxDecoration(
        color: AColor.pinBorderColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: LayoutBuilder(
        builder: (_, constraints) {
          final width = constraints.maxWidth / tabs.length;
          return Stack(
            children: [
              AnimatedPadding(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeIn,
                padding: EdgeInsets.only(left: width * selectedIndex.value),
                child: Container(
                  width: width,
                  margin: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: AColor.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
              Positioned.fill(
                child: Row(
                  children: List.generate(
                    tabs.length,
                    (index) => Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        child: buildText(index),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

part of '../dynamic_map_view.dart';

class AreaListDialog extends HookConsumerWidget {
  const AreaListDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final showOnMap = ref.watch(showAreasOnMapProvider);
    final savedAreas = ref.watch(areaListProvider);

    final columns = [
      TableColumnModel(
        columnName: 'Alan Adı'.hardcoded,
        sortable: false,
        filterable: false,
        minimumWidth: 150,
      ),
      TableColumnModel(
        columnName: 'Alan Tipi'.hardcoded,
        sortable: false,
        filterable: false,
        minimumWidth: 120,
      ),
      const TableColumnModel(
        columnName: '',
        sortable: false,
        filterable: false,
        minimumWidth: 200,
      ),
    ];

    List<List<RowDataModel>> buildRows() => savedAreas.map((area) {
          return [
            RowDataModel(columnName: 'Alan Adı', value: area.name),
            RowDataModel(columnName: '<PERSON>', value: area.type ?? '-'),
            RowDataModel(
              columnName: '',
              cellBuilder: () => Row(
                spacing: 4,
                children: [
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(2),
                        side: BorderSide(color: Colors.grey.shade300),
                      ),
                      backgroundColor: Colors.grey.shade50,
                    ),
                    onPressed: () {
                      AppDialog.show<void>(
                        context: context,
                        title: 'Alanı Düzenle'.hardcoded,
                        width: context.width * .95,
                        height: context.height * .95,
                        child: AddNewAreaWidget(area: area),
                      );
                    },
                    child: TextWidget('Düzenle'.hardcoded),
                  ),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(2),
                        side: BorderSide(color: Colors.grey.shade300),
                      ),
                      backgroundColor: Colors.grey.shade50,
                    ),
                    onPressed: () async {
                      final shouldDelete = await showDialog<bool>(
                        context: context,
                        builder: (ctx) => AlertDialog(
                          title: Text('Silme Onayı'.hardcoded),
                          content: Text(
                              'Bu alanı silmek istediğinize emin misiniz?'
                                  .hardcoded),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(ctx).pop(false),
                              child: Text('Hayır'.hardcoded),
                            ),
                            TextButton(
                              onPressed: () => Navigator.of(ctx).pop(true),
                              child: Text('Evet'.hardcoded),
                            ),
                          ],
                        ),
                      );

                      if (shouldDelete ?? false) {
                        ref.read(areaListProvider.notifier).removeArea(area.id);
                      }
                    },
                    child: TextWidget('Sil'.hardcoded),
                  ),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(2),
                        side: BorderSide(color: Colors.grey.shade300),
                      ),
                      backgroundColor: Colors.grey.shade50,
                    ),
                    onPressed: () {
                      ref.read(areaListProvider.notifier).removeArea(area.id);
                    },
                    child: TextWidget('Konuma git'.hardcoded),
                  ),
                ],
              ),
            ),
          ];
        }).toList();

    return Container(
      decoration: CommonDecorations.containerDecoration(),
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 16,
        children: [
          Row(
            spacing: 8,
            children: [
              TextWidget(
                'Haritada Göster'.hardcoded,
                style: ATextStyle.small,
              ),
              Checkbox(
                value: showOnMap,
                onChanged: (b) {
                  ref.read(showAreasOnMapProvider.notifier).state = b ?? false;
                },
                checkColor: AColor.white,
                activeColor: AColor.primaryColor,
                side: const BorderSide(width: 1.5, color: AColor.grey),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 600,
            width: double.infinity,
            child: SmartTeamDataTable(
              columns: columns,
              rowData: buildRows(),
              showExportButtons: false,
              availableRowsPerPage: const [5],
              initialRowsPerPage: 5,
              gridColumnWidthMode: ColumnWidthMode.auto,
            ),
          ),
        ],
      ),
    );
  }
}

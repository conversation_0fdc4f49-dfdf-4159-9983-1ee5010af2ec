part of '../../dynamic_map_view.dart';

class CustomInfoWindow extends StatelessWidget {
  const CustomInfoWindow({
    required this.userName,
    required this.dateTime,
    required this.speed,
    required this.address,
    required this.state,
    super.key,
  });

  final String userName;
  final String dateTime;
  final double speed;
  final String address;
  final String state;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: getStateColor(state).withAlpha(100),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        children: [
          TextWidget(
            userName,
            textAlign: TextAlign.right,
            style: ATextStyle.text12.copyWith(
              color: AColor.black,
              fontWeight: FontWeight.bold,
            ),
          ),
          TextWidget(
            dateTime,
            textAlign: TextAlign.right,
            style: ATextStyle.small,
          ),
          TextWidget(
            '${speed.toStringAsFixed(1)} ${context.tr.kmh}',
            textAlign: TextAlign.right,
            style: ATextStyle.small,
          ),
          TextWidget(
            address,
            textAlign: TextAlign.right,
            style: ATextStyle.small,
          ),
        ],
      ),
    );
  }

  static Color getStateColor(String state) {
    switch (state) {
      case 'sürüş':
        return AColor.green;
      case 'durma':
        return AColor.primaryColor;
      default:
        return AColor.grey;
    }
  }
}

part of '../../dynamic_map_view.dart';

class MapTimeRangeSelectionDialog extends HookConsumerWidget {
  const MapTimeRangeSelectionDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isTimeRangeSelected = useState(false);
    final isButtonHovered = useState(false);

    final displayTimeRanges = DisplayTimeRangeOption.getList(context);

    Widget buildLabel(String text) {
      return TextWidget(
        text,
        style: ATextStyle.mediumRegular.copyWith(
          color: AColor.tertiary,
        ),
      );
    }

    final selectedRange = useState<DisplayTimeRangeOption>(
      DisplayTimeRangeOption(
        option: TimeRangeOption.lastHour1,
        context: context,
      ),
    );

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: 1000,
          maxHeight: context.height * 0.9,
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: double.infinity,
                height: 45,
                padding:
                    const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                color: AColor.primaryColor,
                child: Align(
                  alignment: Alignment.centerRight,
                  child: IconButton(
                    onPressed: context.maybePop,
                    icon: const Icon(
                      Icons.close,
                      color: AColor.white,
                      size: 20,
                    ),
                  ),
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 40, horizontal: 24),
                child: Column(
                  spacing: 16,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: const BoxDecoration(
                        color: AColor.primaryColor,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(4),
                          topRight: Radius.circular(4),
                        ),
                      ),
                      child: Row(
                        spacing: 8,
                        children: [
                          Container(
                            width: 60,
                            height: 60,
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                              color: AColor.white,
                            ),
                            child: Center(
                              child: TextWidget(
                                'TK'.hardcoded,
                                style: ATextStyle.headerRegular
                                    .copyWith(color: AColor.primaryColor),
                              ),
                            ),
                          ),
                          Column(
                            spacing: 8,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextWidget(
                                'Secilen kullanici adi'.hardcoded,
                                style: ATextStyle.medium.copyWith(
                                  color: AColor.white,
                                ),
                              ),
                              TextWidget(
                                'secilen kullanici telefon no'.hardcoded,
                                style: ATextStyle.semiSmall.copyWith(
                                  color: AColor.white,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildTwoColumnRow(
                          labelLeft: context.tr.lastDataTime,
                          valueLeft: '07/15/2024 10:19',
                          labelRight: context.tr.lastAddress,
                          valueRight:
                              'Fatih Sultan Mehmet Mah. Poligon Cad. ...',
                        ),
                        _buildTwoColumnRow(
                          labelLeft: context.tr.dayStartAddress,
                          valueLeft: '0 km',
                          labelRight: context.tr.dailyDowntime,
                          valueRight: '00:00:00',
                        ),
                        _buildTwoColumnRow(
                          labelLeft: context.tr.dailyWalkingTime,
                          valueLeft: '00:00:00',
                          labelRight: context.tr.dailyWalkingDistance,
                          valueRight: '',
                        ),
                        _buildTwoColumnRow(
                          labelLeft: context.tr.dailyDrivingTime,
                          valueLeft: '0.00',
                          labelRight: context.tr.dailyDrivingDistance,
                          valueRight: '0.00',
                        ),
                        _buildTwoColumnRow(
                          labelLeft: context.tr.group,
                          valueLeft: '',
                          labelRight: context.tr.vehicleType,
                          valueRight: '',
                        ),
                        _buildTwoColumnRow(
                          labelLeft: context.tr.vehicleTotalDistance,
                          valueLeft: '1234.56',
                          labelRight: '',
                          valueRight: '',
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        const Spacer(),
                        Expanded(
                          child: ANewDropDown<DisplayTimeRangeOption>(
                            placeholder: context.tr.select,
                            itemList: displayTimeRanges,
                            selectedItems: [selectedRange.value],
                            onSelected: (value) {
                              selectedRange.value = value;
                              isTimeRangeSelected.value =
                                  value.option.isCustomRange;
                            },
                            itemBuilder: (item) => Text(
                              item?.label ?? context.tr.select,
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (isTimeRangeSelected.value)
                      Row(
                        spacing: 8,
                        children: [
                          const Spacer(
                            flex: 2,
                          ),
                          Expanded(
                            child: Container(
                              decoration:
                                  CommonDecorations.containerDecoration(),
                              child: DateTimePickerField(
                                header: context.tr.startTime,
                              ),
                            ),
                          ),
                          Expanded(
                            child: Container(
                              decoration:
                                  CommonDecorations.containerDecoration(),
                              child: DateTimePickerField(
                                header: context.tr.endTime,
                              ),
                            ),
                          ),
                        ],
                      ),
                    MouseRegion(
                      onEnter: (_) => isButtonHovered.value = true,
                      onExit: (_) => isButtonHovered.value = false,
                      child: LoadingElevatedButton(
                        onPressed: () async {
                          final member = ref.read(teamMemberLocationProvider)!;
                          final range = ref.read(selectedTimeRangeProvider)!;

                          final now = DateTime.now();
                          final routeModels = List.generate(5, (i) {
                            final offset = (i + 1) * 0.001 * range;
                            final speed = i.isOdd
                                ? (member.speed > 0 ? member.speed : 10.0)
                                : 0.0;
                            return member.copyWith(
                              lat: member.lat + offset,
                              lng: member.lng + offset,
                              speed: speed,
                              updatedAt: now.subtract(Duration(hours: i + 1)),
                            );
                          });

                          ref.read(routeLocationsProvider.notifier).state =
                              routeModels;

                          Navigator.of(context).pop();
                          // await context.router.push(const MapScreenRoute());
                          await context.router
                              .push(DynamicMapRoute(isHistoricalView: true));
                        },
                        height: 44,
                        text: context.tr.animation,
                        backgroundColor: isButtonHovered.value
                            ? AColor.primaryColor
                            : AColor.tertiary,
                      ),
                    ),
                    SizedBox(height: context.height * 0.1),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTwoColumnRow({
    required String labelLeft,
    required String valueLeft,
    required String labelRight,
    required String valueRight,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextWidget(
                      labelLeft,
                      style:
                          ATextStyle.semiSmall.copyWith(color: AColor.tertiary),
                    ),
                    if (valueLeft.isNotEmpty) ...[
                      const SizedBox(height: 2),
                      TextWidget(
                        valueLeft,
                        style: ATextStyle.semiSmall,
                      ),
                    ],
                  ],
                ),
              ),
              // const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (labelRight.isNotEmpty)
                      TextWidget(
                        labelRight,
                        style: ATextStyle.semiSmall
                            .copyWith(color: AColor.tertiary),
                      ),
                    if (valueRight.isNotEmpty) ...[
                      const SizedBox(height: 2),
                      TextWidget(
                        valueRight,
                        style: ATextStyle.semiSmall,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
          const Divider(
            thickness: 0.5,
            color: AColor.grey,
          ),
        ],
      ),
    );
  }
}

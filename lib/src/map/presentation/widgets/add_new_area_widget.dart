part of '../dynamic_map_view.dart';

class AddNewAreaWidget extends HookConsumerWidget {
  const AddNewAreaWidget({
    super.key,
    this.area,
    this.center,
    this.radius,
  });

  final AreaModel? area;
  final LatLng? center;
  final double? radius;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = GlobalKey<FormState>();
    final nameCtrl = useTextEditingController(text: area?.name ?? '');

    final idValue = area?.id ?? const Uuid().v4();
    final centerValue = area?.center ?? center!;
    final radiusValue = area?.radius ?? radius!;

    return Form(
      key: formKey,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 16,
          children: [
            Container(
              decoration: CommonDecorations.containerDecoration(),
              child: CustomText<PERSON>orm<PERSON>ield(
                headerText: '<PERSON>'.hardcoded,
                controller: nameCtrl,
                keyboardType: TextInputType.text,
                textInputAction: TextInputAction.done,
                validator: (value) => (value?.trim().isNotEmpty ?? false)
                    ? null
                    : 'Zorunlu alan'.hardcoded,
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                LoadingElevatedButton(
                  height: 45,
                  text: context.tr.save,
                  onPressed: () async {
                    if (!formKey.currentState!.validate()) {
                      ref
                          .read(toastManagerProvider)
                          .showToast('Tüm alanları doldurunuz.'.hardcoded);
                      return;
                    }

                    final updatedArea = AreaModel(
                      id: idValue,
                      name: nameCtrl.text.trim(),
                      center: centerValue,
                      radius: radiusValue,
                      type: area?.type,
                    );

                    if (area == null) {
                      ref.read(areaListProvider.notifier).addArea(updatedArea);
                    } else {
                      /// edit
                      ref
                          .read(areaListProvider.notifier)
                          .updateArea(updatedArea);
                    }
                    context.pop();
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

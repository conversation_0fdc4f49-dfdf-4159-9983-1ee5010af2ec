import 'dart:async';
import 'dart:math';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/router/auto_router.gr.dart';
import 'package:smart_team_web/src/home/<USER>/models/all_team_list.dart';
import 'package:smart_team_web/src/map/application/area_provider.dart';
import 'package:smart_team_web/src/map/application/map_provider.dart';
import 'package:smart_team_web/src/map/domain/area_model.dart';
import 'package:smart_team_web/src/map/domain/team_member_location_model.dart';
import 'package:smart_team_web/src/map/enum/time_range_option.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/shared/utils/dt_format.dart';
import 'package:smart_team_web/src/shared/utils/dt_util.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/toast/toast_manager.dart';
import 'package:smart_team_web/src/widgets/app_dialog/app_dialog.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/dropdown/a_new_dropdown.dart';
import 'package:smart_team_web/src/widgets/image/asset_image.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_column_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_row_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/st_data_table.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';
import 'package:smart_team_web/src/widgets/text_form_field/date_time_picker_field.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

part 'widgets/add_new_area_widget.dart';
part 'widgets/area_list_dialog.dart';
part 'widgets/map/custom_info_window.dart';
part 'widgets/map/map_utils.dart';
part 'widgets/map/ping_image.dart';
part 'widgets/overlays/map_time_range_selection_dialog.dart';

@RoutePage(name: 'DynamicMapRoute')
class DynamicMapScreenView extends ConsumerStatefulWidget {
  const DynamicMapScreenView({
    this.isHistoricalView = false,
    this.allTeamLocations = const [],
    super.key,
  });

  final bool isHistoricalView;
  final List<AllTeamList> allTeamLocations;

  @override
  ConsumerState<DynamicMapScreenView> createState() =>
      _DynamicMapScreenViewState();
}

class _DynamicMapScreenViewState extends ConsumerState<DynamicMapScreenView>
    with SingleTickerProviderStateMixin {
  GoogleMapController? _mapController;
  AnimationController? _animationController;
  Animation<double>? _widthAnimation;
  TeamMemberLocationModel? _hoveredModel;
  TeamMemberLocationModel? _selectedModel;

  // Daire çizimi için değişkenler
  bool _isDraggingCircle = false;
  bool _isDrawingCircle = false;
  LatLng? _circleCenter;
  double _circleRadius = 100.0;
  Set<Circle> _circles = {};
  bool _isCircleToolActive = false;
  Offset? _dragStartPosition;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _widthAnimation = Tween<double>(begin: 40, end: 220).animate(
      CurvedAnimation(parent: _animationController!, curve: Curves.easeOut),
    );
  }

  @override
  void dispose() {
    _animationController?.dispose();
    _mapController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final showAreasOnMap = ref.watch(showAreasOnMapProvider);
    final savedAreas = ref.watch(areaListProvider);

    final rawLocations = widget.isHistoricalView
        ? ref.watch(routeLocationsProvider)
        : widget.allTeamLocations
            .map(
              (e) => TeamMemberLocationModel(
                id: e.id.toString(),
                userName: e.name,
                lat: e.lat,
                lng: e.lng,
                speed: e.speed,
                address: e.stAddress,
                updatedAt: e.dataDateTime,
              ),
            )
            .toList();

    final polylines = widget.isHistoricalView
        ? {
            Polyline(
              polylineId: const PolylineId('route'),
              points: rawLocations.map((m) => LatLng(m.lat, m.lng)).toList(),
              color: Colors.blue,
              width: 4,
            ),
          }
        : <Polyline>{};

    return Scaffold(
      body: Stack(
        children: [
          // Base map
          GoogleMap(
            scrollGesturesEnabled: !(_isCircleToolActive && _isDraggingCircle),
            zoomGesturesEnabled: !(_isCircleToolActive && _isDraggingCircle),
            rotateGesturesEnabled: !(_isCircleToolActive && _isDraggingCircle),
            tiltGesturesEnabled: !(_isCircleToolActive && _isDraggingCircle),
            initialCameraPosition: const CameraPosition(
              target: LatLng(41.015137, 28.979530),
              zoom: 6,
            ),
            polylines: polylines,
            circles: {
              // çizim esnasındaki daire
              if (_isDrawingCircle) ..._circles,
              // checkbox işaretliyse kaydedilmiş alanlar
              if (showAreasOnMap)
                for (final a in savedAreas)
                  Circle(
                    circleId: CircleId(a.id),
                    center: a.center,
                    radius: a.radius,
                    fillColor: AColor.primaryColor.withValues(alpha: 0.2),
                    strokeColor: AColor.primaryColor,
                    strokeWidth: 2,
                  ),
            },
            onMapCreated: (controller) async {
              _mapController = controller;
              if (rawLocations.isNotEmpty) {
                final bounds = MapUtils.boundsFromLatLngList(
                  rawLocations.map((m) => LatLng(m.lat, m.lng)).toList(),
                );
                await controller.moveCamera(
                  CameraUpdate.newLatLngBounds(bounds, 50),
                );
              }
              setState(() {});
            },
            onTap: (position) {
              if (_isCircleToolActive) {
                setState(() {
                  _circleCenter = position;
                  _isDrawingCircle = true;
                  _circleRadius = 10000.0;
                  _updateCircle();
                });
              } else {
                setState(() {
                  _hoveredModel = null;
                  _selectedModel = null;
                });
                _animationController?.reverse();
              }
            },
            onCameraMove: (_) => setState(() {}),
          ),

          // Ping animations
          ...rawLocations.map(_buildPingOverlay),

          // Profile hover & popup
          ...rawLocations.map((m) => _buildProfileHoverArea(context, m)),

          // Sol köşe - Daire çizme butonu
          Positioned(
            top: 20,
            left: 20,
            child: PointerInterceptor(
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color:
                      _isCircleToolActive ? AColor.primaryColor : AColor.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: const [
                    BoxShadow(
                      color: Colors.black26,
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: InkWell(
                  child: Icon(
                    _isCircleToolActive
                        ? Icons.radio_button_checked
                        : Icons.radio_button_unchecked,
                    color: _isCircleToolActive
                        ? AColor.white
                        : AColor.primaryColor,
                  ),
                  onTap: () {
                    setState(() {
                      _isCircleToolActive = !_isCircleToolActive;
                      if (!_isCircleToolActive) {
                        _isDrawingCircle = false;
                        _isDraggingCircle = false;
                        _circleCenter = null;
                        _circles.clear();
                        _dragStartPosition = null;
                      }
                    });
                  },
                ),
              ),
            ),
          ),

          // Sağ köşe - Alanlar ve Müşteriler butonu
          Positioned(
            top: 20,
            right: 20,
            child: InkWell(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: AColor.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: const [
                    BoxShadow(
                      color: Colors.black26,
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    const Icon(Icons.layers, color: AColor.primaryColor),
                    const SizedBox(width: 8),
                    TextWidget(
                      'Alanlar ve Müşteriler',
                      style: ATextStyle.small
                          .copyWith(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
              onTap: () {
                AppDialog.show<void>(
                  context: context,
                  title: 'Bölge Listesi'.hardcoded,
                  width: context.width * .95,
                  height: context.height * .95,
                  child: const AreaListDialog(),
                );
              },
            ),
          ),

          // Daire çizimi bilgi kutusu
          if (_isDrawingCircle && _circleCenter != null)
            Positioned(
              top: 80,
              left: 20,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AColor.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: const [
                    BoxShadow(
                      color: Colors.black26,
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextWidget(
                      'Yarıçap: ${(_circleRadius / 1000).toStringAsFixed(1)} km'
                          .hardcoded,
                      style: ATextStyle.text14
                          .copyWith(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    TextWidget(
                      'Fareyi sürükleyerek boyutu değiştirin'.hardcoded,
                      style: ATextStyle.small,
                    ),
                  ],
                ),
              ),
            ),

          // Drag listener overlay
          if (_isDrawingCircle && _circleCenter != null)
            Positioned.fill(
              child: PointerInterceptor(
                child: Listener(
                  behavior: HitTestBehavior.opaque,
                  onPointerDown: (e) {
                    setState(() {
                      _isDraggingCircle = true;
                      _dragStartPosition = e.position;
                    });
                  },
                  onPointerMove: (e) {
                    if (_isDraggingCircle) {
                      _handleCircleDrag(e.position);
                    }
                  },
                  onPointerUp: (_) async {
                    setState(() {
                      _isDraggingCircle = false;
                      _dragStartPosition = null;
                      _isDrawingCircle = false;
                      _isCircleToolActive = false;
                      _circles.clear();
                    });

                    // naming dialog’u
                    await AppDialog.show<void>(
                      context: context,
                      title: 'Yeni Alan'.hardcoded,
                      width: context.width * .95,
                      height: context.height * .95,
                      child: AddNewAreaWidget(
                        center: _circleCenter,
                        radius: _circleRadius,
                      ),
                    );
                  },
                  onPointerCancel: (_) {
                    setState(() {
                      _isDraggingCircle = false;
                      _dragStartPosition = null;
                    });
                  },
                  child: const MouseRegion(
                    cursor: SystemMouseCursors.resizeLeftRight,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _handleCircleDrag(Offset screenPos) async {
    final center = _circleCenter;
    final map = _mapController;
    if (center == null || map == null) return;

    // Pikseli LatLng'ye çevir
    final sc = ScreenCoordinate(
      x: screenPos.dx.round(),
      y: screenPos.dy.round(),
    );
    final dragLatLng = await map.getLatLng(sc);

    // Gerçek mesafeyi metre cinsinden al
    final meters = _distanceBetween(center, dragLatLng);

    setState(() {
      _circleRadius = meters;
      _updateCircle();
    });
  }

  void _updateCircle() {
    final center = _circleCenter;
    if (center == null) return;

    final c = Circle(
      circleId: const CircleId('userCircle'),
      center: center,
      radius: _circleRadius,
      fillColor: AColor.primaryColor.withValues(alpha: 0.2),
      strokeColor: AColor.primaryColor,
      strokeWidth: 2,
    );

    setState(() {
      _circles = {c};
    });
  }

  /// Haversine formülüyle iki LatLng arası mesafe (metre).
  double _distanceBetween(LatLng a, LatLng b) {
    const double earthRadius = 6371000; // metre
    final lat1Rad = a.latitude * pi / 180;
    final lat2Rad = b.latitude * pi / 180;
    final deltaLat = (b.latitude - a.latitude) * pi / 180;
    final deltaLng = (b.longitude - a.longitude) * pi / 180;

    final sinDLat = sin(deltaLat / 2);
    final sinDLng = sin(deltaLng / 2);

    final h =
        sinDLat * sinDLat + cos(lat1Rad) * cos(lat2Rad) * sinDLng * sinDLng;
    final c = 2 * atan2(sqrt(h), sqrt(1 - h));
    return earthRadius * c;
  }

  Future<Offset> _getPixelFromLatLng(LatLng latLng) async {
    if (_mapController == null) return Offset.zero;
    try {
      final coord = await _mapController!.getScreenCoordinate(latLng);
      return Offset(coord.x.toDouble(), coord.y.toDouble());
    } catch (_) {
      return Offset.zero;
    }
  }

  Widget _buildPingOverlay(TeamMemberLocationModel model) {
    return FutureBuilder<Offset>(
      future: _getPixelFromLatLng(LatLng(model.lat, model.lng)),
      builder: (context, snap) {
        if (!snap.hasData) return const SizedBox.shrink();
        final pos = snap.data!;
        final img = model.speed > 0
            ? SmartTeamAssets.images.driving.path
            : SmartTeamAssets.images.walking.path;
        return Positioned(
          left: pos.dx - (model.speed > 0 ? 30 : 30) / 2,
          top: pos.dy - (model.speed > 0 ? 30 : 30) / 2,
          child: PingImage(imgPath: img, size: 30),
        );
      },
    );
  }

  Widget _buildProfileHoverArea(
    BuildContext context,
    TeamMemberLocationModel model,
  ) {
    return FutureBuilder<Offset>(
      future: _getPixelFromLatLng(LatLng(model.lat, model.lng)),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const SizedBox.shrink();
        final pos = snapshot.data!;
        final state = model.speed > 0 ? 'sürüş' : 'durma';
        return Positioned(
          left: pos.dx + 16,
          top: pos.dy - 16,
          child: MouseRegion(
            onEnter: (_) {
              setState(() {
                _hoveredModel = model;
                _selectedModel = model;
              });
              _animationController?.forward();
            },
            onExit: (_) {
              Future.delayed(const Duration(milliseconds: 300), () {
                if (_hoveredModel == model && _selectedModel != model) {
                  setState(() => _hoveredModel = null);
                  _animationController?.reverse();
                }
              });
            },
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedModel = model;
                });
                _animationController?.forward();
              },
              child: _hoveredModel == model
                  ? _buildExpandingProfilePopup(context, model, state)
                  : Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AColor.white,
                        border: Border.all(color: AColor.white),
                      ),
                      child: Icon(
                        Icons.account_circle,
                        color: CustomInfoWindow.getStateColor(state),
                        size: 40,
                      ),
                    ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildExpandingProfilePopup(
    BuildContext context,
    TeamMemberLocationModel model,
    String state,
  ) {
    return AnimatedBuilder(
      animation: _animationController!,
      builder: (context, child) {
        final width = _widthAnimation!.value;
        const padding = 8.0;
        const maxAvatar = 60.0;
        final available = width - padding * 2;
        final avatarSize =
            available < maxAvatar ? available.clamp(0.0, maxAvatar) : maxAvatar;
        final dateStr = DTUtil.dtToString(
          model.updatedAt ?? DateTime.now(),
          format: DTFormat.human1,
        );
        return ClipRect(
          child: Container(
            width: width,
            decoration: BoxDecoration(
              color: AColor.white,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Padding(
              padding: const EdgeInsets.all(padding),
              child: Row(
                children: [
                  Container(
                    width: avatarSize,
                    height: avatarSize,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: CustomInfoWindow.getStateColor(state),
                    ),
                    child:
                        const Icon(Icons.person, size: 24, color: AColor.white),
                  ),
                  if (width > 70) ...[
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TextWidget(
                            model.userName,
                            textAlign: TextAlign.right,
                            style: ATextStyle.text12.copyWith(
                              color: AColor.black,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                          ),
                          const SizedBox(height: 4),
                          TextWidget(
                            dateStr,
                            textAlign: TextAlign.right,
                            style: ATextStyle.small,
                            maxLines: 1,
                          ),
                          const SizedBox(height: 2),
                          TextWidget(
                            '${model.speed.toStringAsFixed(1)} ${context.tr.kmh}',
                            textAlign: TextAlign.right,
                            style: ATextStyle.small,
                            maxLines: 1,
                          ),
                          const SizedBox(height: 2),
                          TextWidget(
                            model.address,
                            textAlign: TextAlign.right,
                            style: ATextStyle.small,
                            maxLines: 1,
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

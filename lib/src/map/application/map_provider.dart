import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/map/domain/team_member_location_model.dart';

/// Haritada göster kutusunun durumu
final showAreasOnMapProvider = StateProvider<bool>((ref) => false);

/// Seçili üyenin güncel konumu
final teamMemberLocationProvider =
    StateProvider<TeamMemberLocationModel?>((ref) => null);

/// Dialog’da seçilen “son X saat” değeri (1, 2, 3, 6, …)
final selectedTimeRangeProvider = StateProvider<int?>((ref) => 1);

final teamRouteProvider = StateProvider<List<LatLng>>((ref) => []);

/// Seçili zaman aralığına göre oluşturulmuş rota noktaları
final routeLocationsProvider =
    StateProvider<List<TeamMemberLocationModel>>((ref) {
  final member = ref.watch(teamMemberLocationProvider);
  final range = ref.watch(selectedTimeRangeProvider) ?? 1;

  if (member == null) return [];

  final now = DateTime.now();

  // mock 5 konum
  return List.generate(5, (i) {
    final offset = (i + 1) * 0.001 * range;
    //tekillerde sürüş, çiftlerde durma
    final speed = i.isOdd ? (member.speed > 0 ? member.speed : 10.0) : 0.0;
    return member.copyWith(
      lat: member.lat + offset,
      lng: member.lng + offset,
      speed: speed,
      updatedAt: now.subtract(Duration(hours: i + 1)),
    );
  });
});

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/map/domain/area_model.dart';

final areaListProvider =
    StateNotifierProvider<AreaListNotifier, List<AreaModel>>((ref) {
  return AreaListNotifier();
});

class AreaListNotifier extends StateNotifier<List<AreaModel>> {
  AreaListNotifier() : super([]);

  void addArea(AreaModel area) {
    state = [...state, area];
  }

  void removeArea(String id) {
    state = state.where((a) => a.id != id).toList();
  }

  void updateArea(AreaModel updated) {
    state = [
      for (final a in state)
        if (a.id == updated.id) updated else a,
    ];
  }
}

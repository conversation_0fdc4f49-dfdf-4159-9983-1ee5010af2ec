import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

part 'area_model.freezed.dart';
part 'area_model.g.dart';

@freezed
abstract class AreaModel with _$AreaModel {
  const factory AreaModel({
    required String id,
    required String name,
    @JsonKey(
      fromJson: _latLngFromJson,
      toJson: _latLngToJson,
    )
    required LatLng center,
    required double radius,
    String? type,
  }) = _AreaModel;

  factory AreaModel.fromJson(Map<String, dynamic> json) =>
      _$AreaModelFromJson(json);
}

/// JSON’dan LatLng’e:
LatLng _latLngFromJson(Map<String, dynamic> json) =>
    LatLng(json['lat'] as double, json['lng'] as double);

/// LatLng’den JSON’a:
Map<String, dynamic> _latLngToJson(LatLng center) => {
      'lat': center.latitude,
      'lng': center.longitude,
    };

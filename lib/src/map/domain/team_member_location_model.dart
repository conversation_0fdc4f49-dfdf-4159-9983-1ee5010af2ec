import 'package:freezed_annotation/freezed_annotation.dart';

part 'team_member_location_model.freezed.dart';
part 'team_member_location_model.g.dart';

@freezed
abstract class TeamMemberLocationModel with _$TeamMemberLocationModel {
  const factory TeamMemberLocationModel({
    required String id,
    required String userName,
    required double lat,
    required double lng,
    @Default(0.0) double speed,
    @Default('') String address,
    DateTime? updatedAt,
    String? imageUrl,
  }) = _TeamMemberLocationModel;

  factory TeamMemberLocationModel.fromJson(Map<String, dynamic> json) =>
      _$TeamMemberLocationModelFromJson(json);
}

import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/home/<USER>/dashboard_provider.dart';
import 'package:smart_team_web/src/shared/enums/calendar_type_enum.dart';

final taskPrioritiesProvider = FutureProvider<List<TaskPriority>>((ref) async {
  final repository = ref.read(taskPriorityRepositoryProvider);
  return repository.fetchAll();
});

final taskProvider = StateNotifierProvider<TaskNotifier, List<Task>>((ref) {
  final repo = ref.watch(taskRepositoryProvider);
  return TaskNotifier(repo);
});

class TaskNotifier extends StateNotifier<List<Task>> {
  TaskNotifier(this._repository) : super([]) {
    _init();
  }

  final TaskRepository _repository;

  Future<void> _init() async {
    final fetched = await _repository.fetchAll();
    state = fetched;
  }

  Future<void> addTask(Task task) async {
    await _repository.upsert(task);
    final all = await _repository.fetchAll();
    state = all;
  }

  Future<void> deleteTaskAt(int index) async {
    final removed = state[index];
    await _repository.deleteById(removed.id!);
    final all = await _repository.fetchAll();
    state = all;
  }
}

final selectedTeamMemberProvider = StateProvider<TeamMemberModel?>((_) => null);

final filteredTasksProvider = Provider<List<Task>>((ref) {
  final allTasks = ref.watch(taskProvider);
  final query = ref.watch(queryProvider).toLowerCase();
  final sortAscending = ref.watch(sortAscendingProvider);
  final selectedMember = ref.watch(selectedTeamMemberProvider);

  final filtered = allTasks.where((task) {
    final matchesQuery = task.title.toLowerCase().contains(query) ||
        (task.createdByUser.name ?? '').toLowerCase().contains(query);

    final matchesMember =
        selectedMember == null || (task.assignedToUser.id == selectedMember.id);

    return matchesQuery && matchesMember;
  }).toList()
    ..sort((a, b) {
      final aDate = a.dueDate ?? a.createdAt!;
      final bDate = b.dueDate ?? b.createdAt!;
      return sortAscending ? aDate.compareTo(bDate) : bDate.compareTo(aDate);
    });

  return filtered;
});

final calendarTypeProvider =
    StateProvider<CalendarType>((_) => CalendarType.week);
final calendarSelectedDayProvider =
    StateProvider<DateTime>((_) => DateTime.now());
final selectedMembersProvider = StateProvider<List<TeamMemberModel>>((_) => []);

final tasksForCalendarViewProvider = Provider<List<Task>>((ref) {
  final allTasks = ref.watch(taskProvider);
  final selectedMembers = ref.watch(selectedMembersProvider);
  final calType = ref.watch(calendarTypeProvider);
  final day = ref.watch(calendarSelectedDayProvider);

  var tasks =
      allTasks.where((t) => t.status == TaskStatusEnum.accepted).toList();

  if (selectedMembers.isNotEmpty) {
    final ids = selectedMembers.map((m) => m.id).toSet();
    tasks = tasks.where((t) => ids.contains(t.assignedToUser.id)).toList();
  }

  switch (calType) {
    case CalendarType.day:
      tasks = tasks.where((t) => _isSameDay(_taskDate(t), day)).toList();

    case CalendarType.week:
      final monday = _startOfWeek(day);
      final sunday = monday.add(const Duration(days: 6));
      tasks = tasks.where((t) {
        final dt = _taskDate(t);
        return !dt.isBefore(monday) && !dt.isAfter(sunday);
      }).toList();

    case CalendarType.month:
      tasks = tasks.where((t) {
        final dt = _taskDate(t);
        return dt.year == day.year && dt.month == day.month;
      }).toList();
  }

  return tasks;
});

/// ------------------------------------------------------------
/// Helper functions
/// ------------------------------------------------------------
bool _isSameDay(DateTime a, DateTime b) =>
    a.year == b.year && a.month == b.month && a.day == b.day;

DateTime _startOfWeek(DateTime d) => d.subtract(Duration(days: d.weekday - 1));

DateTime _taskDate(Task t) => t.dueDate ?? t.createdAt!;

part of '../calendar_view.dart';

class CalendarMonthView extends HookConsumerWidget {
  const CalendarMonthView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final current = useState<DateTime>(DateTime.now());
    final tasks = ref.watch(tasksForCalendarViewProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildHeader(context, ref, current),
        const SizedBox(height: 16),
        _buildMonthGrid(context, current.value, tasks),
      ],
    );
  }

  // ---------- HEADER ----------
  Widget _buildHeader(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<DateTime> current,
  ) {
    final m = current.value;
    final lbl = '${monthName(context, m.month)} ${m.year}';

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 30),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              _monthNavButton(
                context,
                onToday: () => current.value = DateTime.now(),
                onPrev: () => current.value = DateTime(m.year, m.month - 1),
                onNext: () => current.value = DateTime(m.year, m.month + 1),
              ),
              const SizedBox(width: 16),
              ACalendarDropDown(
                initialDate: m,
                displayText: lbl,
                onDateSelected: (d) => current.value = d,
              ),
            ],
          ),
          buildCalendarTypeSelector(ref, context, selectedDate: m),
        ],
      ),
    );
  }

  Widget _monthNavButton(
    BuildContext context, {
    required VoidCallback onToday,
    required VoidCallback onPrev,
    required VoidCallback onNext,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(2),
      ),
      child: Row(
        children: [
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(2),
              ),
              backgroundColor: Colors.grey.shade50,
            ),
            onPressed: onToday,
            child: TextWidget(context.tr.today),
          ),
          _arrowBtn(Icons.arrow_left, onPrev),
          _arrowBtn(Icons.arrow_right, onNext),
        ],
      ),
    );
  }

  Widget _arrowBtn(IconData icn, VoidCallback cb) => SizedBox(
        width: 25,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(2),
            ),
            backgroundColor: Colors.grey.shade50,
            padding: EdgeInsets.zero,
          ),
          onPressed: cb,
          child: Icon(icn, size: 20, color: AColor.doveGray),
        ),
      );

  // ---------- GRID ----------
  Widget _buildMonthGrid(
    BuildContext context,
    DateTime month,
    List<Task> tasks,
  ) {
    final matrix = _generateMonthDays(month);

    return Column(
      children: [
        _weekdayHeader(context),
        const SizedBox(height: 8),
        for (final row in matrix)
          IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                for (final day in row)
                  Expanded(
                    child: _dayCell(context, day, month, tasks),
                  ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _weekdayHeader(BuildContext context) => Row(
        children: [
          for (final w in [
            context.tr.monday,
            context.tr.tuesday,
            context.tr.wednesday,
            context.tr.thursday,
            context.tr.friday,
            context.tr.saturday,
            context.tr.sunday,
          ])
            Expanded(
              child: Center(
                child: TextWidget(
                  w,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),
        ],
      );

  Widget _dayCell(
    BuildContext context,
    DateTime day,
    DateTime month,
    List<Task> tasks,
  ) {
    final inMonth = day.month == month.month;
    final textColor = inMonth ? Colors.black : Colors.grey;
    final tasksOfDay = tasks.where((t) => _isSameDay(t.dueDate!, day)).toList();

    return ConstrainedBox(
      constraints: const BoxConstraints(minHeight: 60),
      child: GestureDetector(
        onDoubleTap: () =>
            showAddNewJobDialog(context: context, initialDate: day),
        child: Container(
          margin: const EdgeInsets.all(1),
          color: Colors.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: const EdgeInsets.all(4),
                  child: TextWidget(
                    day.day.toString().padLeft(2, '0'),
                    style: TextStyle(color: textColor),
                  ),
                ),
              ),
              for (final t in tasksOfDay)
                Container(
                  width: double.infinity,
                  margin:
                      const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: AColor.primaryColor.withAlpha(100),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: CustomTooltip(
                    tooltipContent: buildCustomTooltipContent({
                      'Kullanıcı'.hardcoded: t.createdByUser.name,
                      'Başlık'.hardcoded: t.title,
                    }),
                    child: TextWidget(
                      t.title,
                      style: ATextStyle.small.copyWith(color: AColor.white),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // ----- yardımcılar -----
  List<List<DateTime>> _generateMonthDays(DateTime month) {
    final firstDayOfMonth = DateTime(month.year, month.month);
    final lastDayOfMonth = DateTime(month.year, month.month + 1, 0);
    final daysInMonth = lastDayOfMonth.day;
    final firstWeekday = firstDayOfMonth.weekday;
    final leadingDays = firstWeekday - 1;

    final days = <DateTime>[];
    final prevMonthLastDay = DateTime(month.year, month.month, 0);
    final prevMonthDays = prevMonthLastDay.day;
    for (var i = leadingDays; i > 0; i--) {
      days.add(DateTime(month.year, month.month - 1, prevMonthDays - i + 1));
    }
    for (var d = 1; d <= daysInMonth; d++) {
      days.add(DateTime(month.year, month.month, d));
    }
    const totalCells = 42;
    final trailingDays = totalCells - days.length;
    for (var i = 1; i <= trailingDays; i++) {
      days.add(DateTime(month.year, month.month + 1, i));
    }

    final matrix = <List<DateTime>>[];
    for (var i = 0; i < 6; i++) {
      matrix.add(days.sublist(i * 7, (i + 1) * 7));
    }
    return matrix;
  }

  bool _isSameDay(DateTime a, DateTime b) =>
      a.year == b.year && a.month == b.month && a.day == b.day;
}

part of '../calendar_view.dart';

String monthName(BuildContext context, int month) {
  switch (month) {
    case 1:
      return context.tr.jan;
    case 2:
      return context.tr.feb;
    case 3:
      return context.tr.march;
    case 4:
      return context.tr.april;
    case 5:
      return context.tr.may;
    case 6:
      return context.tr.june;
    case 7:
      return context.tr.july;
    case 8:
      return context.tr.aug;
    case 9:
      return context.tr.sep;
    case 10:
      return context.tr.oct;
    case 11:
      return context.tr.nov;
    case 12:
      return context.tr.dec;
    default:
      return '';
  }
}

String weekName(BuildContext context, int weekday) {
  switch (weekday) {
    case 1:
      return context.tr.monday;
    case 2:
      return context.tr.tuesday;
    case 3:
      return context.tr.wednesday;
    case 4:
      return context.tr.thursday;
    case 5:
      return context.tr.friday;
    case 6:
      return context.tr.saturday;
    case 7:
      return context.tr.sunday;
    default:
      return '';
  }
}

Widget buildCalendarTypeSelector(
  WidgetRef ref,
  BuildContext context, {
  DateTime? selectedDate,
}) {
  final current = ref.watch(calendarTypeProvider);

  return Row(
    // mainAxisSize: MainAxisSize.min,
    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
    children: [
      ToggleButtons(
        borderRadius: BorderRadius.circular(2),
        selectedColor: AColor.white,
        fillColor: AColor.primaryColor,
        color: AColor.textColor,
        constraints: const BoxConstraints(minHeight: 35),
        isSelected: [
          current == CalendarType.day,
          current == CalendarType.week,
          current == CalendarType.month,
        ],
        onPressed: (idx) {
          ref.read(calendarTypeProvider.notifier).state =
              CalendarType.values[idx];
        },
        borderColor: Colors.grey.shade400,
        selectedBorderColor: AColor.primaryColor,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Text(context.tr.day, style: const TextStyle(fontSize: 12)),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Text(context.tr.week, style: const TextStyle(fontSize: 12)),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Text(context.tr.month, style: const TextStyle(fontSize: 12)),
          ),
        ],
      ),
      SizedBox(width: context.width * .03),
      LoadingElevatedButton(
        height: 35,
        onPressed: () async => showAddNewJobDialog(
          context: context,
          initialDate: selectedDate!,
        ),
        text: 'Takvim Oluştur'.hardcoded,
      ),
    ],
  );
}

Widget permitsCalendarTypeSelector(
  WidgetRef ref,
  BuildContext context, {
  DateTime? selectedDate,
  bool showAddButton = true,
}) {
  final current = ref.watch(permitsCalendarTypeProvider);

  return Row(
    // mainAxisSize: MainAxisSize.min,
    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
    children: [
      ToggleButtons(
        borderRadius: BorderRadius.circular(2),
        selectedColor: AColor.white,
        fillColor: AColor.primaryColor,
        color: AColor.textColor,
        constraints: const BoxConstraints(minHeight: 35),
        isSelected: [
          current == CalendarType.day,
          current == CalendarType.week,
          current == CalendarType.month,
        ],
        onPressed: (idx) {
          ref.read(permitsCalendarTypeProvider.notifier).state =
              CalendarType.values[idx];
        },
        borderColor: Colors.grey.shade400,
        selectedBorderColor: AColor.primaryColor,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Text(context.tr.day, style: const TextStyle(fontSize: 12)),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Text(context.tr.week, style: const TextStyle(fontSize: 12)),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Text(context.tr.month, style: const TextStyle(fontSize: 12)),
          ),
        ],
      ),
    ],
  );
}

/// “Şu anki saate” denk gelen kırmızı çizginin dikey pozisyonunu hesaplar
double calculateRedLineOffset(
  DateTime now,
  DateTime startTime,
  double slotHeight,
  List<DateTime> timeSlots,
) {
  final diffMinutes = now.difference(startTime).inMinutes;
  return (diffMinutes / 30.0) * slotHeight;
}

void showAddNewJobDialog({
  required BuildContext context,
  required DateTime initialDate,
}) {
  AppDialog.show<void>(
    context: context,
    title: context.tr.newJob,
    width: context.width * .95,
    height: context.height * .95,
    child: const AddNewJobWidgets(),
  );
}

Widget buildCustomTooltipContent(
  Map<String, String> fields, {
  double labelWidth = 80.0,
  double colonWidth = 8.0,
  double maxTooltipWidth = 300.0,
}) {
  return Container(
    padding: const EdgeInsets.all(12),
    color: Colors.transparent,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: fields.entries.map((entry) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: labelWidth,
                alignment: Alignment.centerLeft,
                child: Text(
                  entry.key,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
              Container(
                width: colonWidth,
                alignment: Alignment.center,
                child: const Text(
                  ':',
                  style: TextStyle(color: Colors.white),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  entry.value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    ),
  );
}

JobPriority? mapTaskPriorityToJobPriority(TaskPriority? taskPriority) {
  if (taskPriority == null) return null;
  switch (taskPriority.name.toLowerCase()) {
    case 'low':
      return JobPriority.low;
    case 'mid':
      return JobPriority.mid;
    case 'high':
      return JobPriority.high;
    case 'critical':
      return JobPriority.critical;
    default:
      return null;
  }
}

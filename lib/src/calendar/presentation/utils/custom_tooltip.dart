part of '../calendar_view.dart';

class CustomTooltip extends StatefulWidget {
  const CustomTooltip({
    required this.child,
    required this.tooltipContent,
    super.key,
    this.preferredDirection = TooltipDirection.top,
    this.tooltipOffset = 10.0,
    this.showDelay = const Duration(milliseconds: 300),
    this.hideDelay = const Duration(milliseconds: 100),
  });

  final Widget child;
  final Widget tooltipContent;
  final TooltipDirection preferredDirection;
  final double tooltipOffset;
  final Duration showDelay;
  final Duration hideDelay;

  @override
  CustomTooltipState createState() => CustomTooltipState();
}

enum TooltipDirection { top, bottom, left, right }

class CustomTooltipState extends State<CustomTooltip> {
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();
  Size _lastSize = Size.zero;
  Timer? _showTimer;
  Timer? _hideTimer;
  Timer? _autoHideTimer;
  bool _isPointerInside = false;
  bool _isTooltipVisible = false;

  OverlayEntry _createOverlayEntry() {
    final renderBox = context.findRenderObject()! as RenderBox;
    final size = renderBox.size;
    _lastSize = size;

    final screenSize = MediaQuery.of(context).size;

    return OverlayEntry(
      builder: (context) {
        return Stack(
          children: [
            Positioned(
              width: screenSize.width,
              height: screenSize.height,
              child: MouseRegion(
                opaque: false,
                onHover: (_) {
                  // Tooltip dışında fare hareketi
                  if (!_isPointerInside) {
                    _scheduleHide();
                  }
                },
                child: GestureDetector(
                  onTap: _hideOverlay,
                  behavior: HitTestBehavior.translucent,
                  child: Container(color: Colors.transparent),
                ),
              ),
            ),
            CompositedTransformFollower(
              link: _layerLink,
              targetAnchor: Alignment.topCenter,
              followerAnchor: Alignment.bottomCenter,
              offset: Offset(0, -widget.tooltipOffset),
              child: MouseRegion(
                onEnter: (_) {
                  // Fare tooltip üzerinde
                  _cancelHideTimer();
                },
                onExit: (_) {
                  // Fare tooltip dışında
                  _scheduleHide();
                },
                child: Material(
                  color: Colors.transparent,
                  child: Container(
                    constraints: const BoxConstraints(maxWidth: 300),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Tooltip içeriği
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.black87,
                            borderRadius: BorderRadius.circular(4),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 5,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: widget.tooltipContent,
                        ),
                        // Aşağı bakan üçgen
                        SizedBox(
                          width: 16,
                          height: 8,
                          child: CustomPaint(
                            painter: TrianglePainter(color: Colors.black87),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _scheduleShow() {
    _cancelTimers();

    _showTimer = Timer(widget.showDelay, () {
      if (_isPointerInside && !_isTooltipVisible) {
        _overlayEntry = _createOverlayEntry();
        Overlay.of(context).insert(_overlayEntry!);
        _isTooltipVisible = true;

        // Güvenlik amaçlı kapanma zamanlayıcısı
        _autoHideTimer = Timer(const Duration(seconds: 10), () {
          _hideOverlay(immediately: true);
        });
      }
    });
  }

  void _scheduleHide() {
    _cancelShowTimer();

    _hideTimer = Timer(widget.hideDelay, () {
      _hideOverlay(immediately: true);
    });
  }

  void _cancelShowTimer() {
    _showTimer?.cancel();
    _showTimer = null;
  }

  void _cancelHideTimer() {
    _hideTimer?.cancel();
    _hideTimer = null;
  }

  void _cancelAutoHideTimer() {
    _autoHideTimer?.cancel();
    _autoHideTimer = null;
  }

  void _cancelTimers() {
    _cancelShowTimer();
    _cancelHideTimer();
    _cancelAutoHideTimer();
  }

  void _hideOverlay({bool immediately = false}) {
    if (immediately) {
      _performHide();
    } else {
      _scheduleHide();
    }
  }

  void _performHide() {
    _cancelTimers();
    _overlayEntry?.remove();
    _overlayEntry = null;
    _isTooltipVisible = false;
  }

  @override
  void dispose() {
    _cancelTimers();
    _performHide();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: MouseRegion(
        onEnter: (_) {
          _isPointerInside = true;
          _scheduleShow();
        },
        onExit: (_) {
          _isPointerInside = false;
          _scheduleHide();
        },
        child: widget.child,
      ),
    );
  }
}

class TrianglePainter extends CustomPainter {
  TrianglePainter({required this.color});
  final Color color;

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path()

      // Aşağı bakan üçgen
      ..moveTo(size.width / 2, size.height)
      ..lineTo(0, 0)
      ..lineTo(size.width, 0)
      ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(TrianglePainter oldDelegate) {
    return color != oldDelegate.color;
  }
}

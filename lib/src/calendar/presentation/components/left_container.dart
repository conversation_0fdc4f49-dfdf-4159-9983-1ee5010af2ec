part of '../calendar_view.dart';

class LeftContainer extends HookConsumerWidget {
  const LeftContainer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedMembers = ref.watch(selectedMembersProvider);

    return Container(
      width: TeamListTreeConstants.leftContainerWidth(context),
      padding: context.responsive(
        desktop: const EdgeInsets.symmetric(horizontal: 30).copyWith(top: 24),
        tablet: const EdgeInsets.symmetric(horizontal: 20).copyWith(top: 16),
      ),
      child: Align(
        alignment: Alignment.topCenter,
        child: EmployeeTreeView(
          selectedMembers: selectedMembers,
          onTeamMemberSelected: (members) {
            ref.read(selectedMembersProvider.notifier).state = members;
          },
        ),
      ),
    );
  }
}

part of '../calendar_view.dart';

class _JobCalendar extends HookConsumerWidget {
  const _JobCalendar();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentViewType = ref.watch(calendarTypeProvider);

    return Container(
      padding: const EdgeInsets.only(top: 30),
      decoration: BoxDecoration(
        border: Border.all(color: AColor.loginBorderColor),
      ),
      child: <PERSON>um<PERSON>(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildCalendarView(context, ref, currentViewType),
        ],
      ),
    );
  }

  Widget _buildCalendarView(
    BuildContext context,
    WidgetRef ref,
    CalendarType viewType,
  ) {
    switch (viewType) {
      case CalendarType.day:
        return const CalendarDayView();
      case CalendarType.week:
        return const CalendarWeekView();
      case CalendarType.month:
        return const CalendarMonthView();
    }
  }
}

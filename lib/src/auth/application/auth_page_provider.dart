import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/auth/domain/auth_state.dart';
import 'package:smart_team_web/src/auth/repository/auth_repository.dart';

final authPageProvider = Provider.autoDispose<AuthProvider>(AuthProvider.new);

class AuthProvider {
  AuthProvider(this.ref);
  final Ref ref;

  AuthState authState = const AuthState();

  void setEmail(String email) {
    authState = authState.copyWith(email: email);
  }

  void setPassword(String password) {
    authState = authState.copyWith(password: password);
  }

  Future<void> signIn() async {
    final authRepository = ref.read(authRepositoryProvider);
    final userId = await authRepository.signIn(authState.email, authState.password);
    
  }
}

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/auth/domain/panel_user_model.dart';
import 'package:smart_team_web/src/auth/repository/auth_repository.dart';

final currentOnlineUserProvider =
    AsyncNotifierProvider<CurrentOnlineUserProvider, PanelUserModel?>(
        CurrentOnlineUserProvider.new);

class CurrentOnlineUserProvider extends AsyncNotifier<PanelUserModel?> {
  @override
  Future<PanelUserModel?> build() async {
    return ref.read(authRepositoryProvider).getCurrentOnlineUser();
  }

  Future<void> setCurrentOnlineUser(PanelUserModel user) async {
    state = AsyncValue.data(user);
  }
}

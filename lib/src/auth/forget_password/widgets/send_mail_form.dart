part of '../forget_password_page.dart';

class _SendMailForm extends HookConsumerWidget {
  const _SendMailForm();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = ref.watch(formKeyProvider(FormType.sendMail));

    Future<void> onSendMailButtonPressed() async {
      if (formKey.currentState!.validate()) {
        final isMailSent =
            await ref.read(forgetPasswordPageProvider).sendMail();
        if (isMailSent) {
          ref.read(toastManagerProvider).showToast('Mail gönderildi'.hardcoded);
        } else {
          ref
              .read(toastManagerProvider)
              .showToastErrorWithMessage('Mail gönderilemedi'.hardcoded);
        }
      }
    }

    return Form(
      key: formKey,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          spacing: 24,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            CustomTextFormField(
              headerText:
                  'Ş<PERSON>renizi yenilemek için mail adresinizi giriniz:'.hardcoded,
              hintText: context.tr.email,
              keyboardType: TextInputType.emailAddress,
              textInputAction: TextInputAction.next,
              suffixIcon: const Icon(
                Icons.mail_outline,
                size: 22,
              ),
              validator: (value) => value.isValidMail(context),
              onChanged: ref.read(forgetPasswordPageProvider).setEmail,
            ),
            LoadingElevatedButton(
              width: context.width,
              height: 45,
              onPressed: onSendMailButtonPressed,
              text: 'Mail Gönder'.hardcoded,
            ),
          ],
        ),
      ),
    );
  }
}

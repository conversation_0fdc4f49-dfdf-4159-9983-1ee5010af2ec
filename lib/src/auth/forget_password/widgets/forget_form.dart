part of '../forget_password_page.dart';

class _ForgetForm extends HookConsumerWidget {
  const _ForgetForm();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const iconSize = 22.0;
    final formKey = ref.watch(formKeyProvider(FormType.changePassword));
    final passwordController = useTextEditingController();
    final passwordAgainController = useTextEditingController();

    Future<void> onUpdatePasswordButtonPressed() async {
      if (formKey.currentState!.validate()) {
        final isPasswordUpdated = await ref.read(forgetPasswordPageProvider).updatePassword();
        if (!context.mounted) return;
        if (isPasswordUpdated) {
          ref.read(toastManagerProvider).showToast('<PERSON><PERSON>re güncellendi. Yeni şifreniz ile giriş yapabilirsiniz.'.hardcoded);
          await context.router.replaceAll([const AuthRoute()]);
        } else {
          ref.read(toastManagerProvider).showToastErrorWithMessage('<PERSON><PERSON><PERSON> güncellenemedi'.hardcoded);
        }
      }
    }

    return Form(
      key: form<PERSON><PERSON>,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              spacing: 24,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                CustomTextFormField(
                  controller: passwordController,
                  hintText: context.tr.newPass,
                  keyboardType: TextInputType.visiblePassword,
                  textInputAction: TextInputAction.next,
                  obscureText: true,
                  suffixIcon: const Icon(
                    Icons.lock,
                    size: iconSize,
                  ),
                  onChanged: ref.read(forgetPasswordPageProvider).setNewPassword,
                  validator: (value) => value.isValidPasswordAgain(context, passwordAgainController.text),
                ),
                CustomTextFormField(
                  controller: passwordAgainController,
                  hintText: context.tr.passAgain,
                  keyboardType: TextInputType.visiblePassword,
                  textInputAction: TextInputAction.next,
                  obscureText: true,
                  suffixIcon: const Icon(
                    Icons.lock,
                    size: iconSize,
                  ),
                  onChanged: ref.read(forgetPasswordPageProvider).setNewPasswordAgain,
                  validator: (value) => value.isValidPasswordAgain(context, passwordController.text),
                ),
                LoadingElevatedButton(
                  width: context.width,
                  height: 45,
                  onPressed: onUpdatePasswordButtonPressed,
                  text: 'Şifreyi Güncelle'.hardcoded,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

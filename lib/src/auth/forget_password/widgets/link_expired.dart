part of '../forget_password_page.dart';

class _LinkExpired extends HookConsumerWidget {
  const _LinkExpired();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              spacing: 24,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  'Link süresi doldu. Lütfen bir daha mail gönderiniz.'.hardcoded,
                  style: ATextStyle.text14SemiBold,
                  textAlign: TextAlign.center,
                ),
                LoadingElevatedButton(
                  width: context.width,
                  height: 45,
                  onPressed: () async {
                    await context.replaceRoute( ForgetPasswordRoute());
                  },
                  text: 'Bir daha dene'.hardcoded,
                ),
              ],
            ),
          ),
        ],
      );
  }
}

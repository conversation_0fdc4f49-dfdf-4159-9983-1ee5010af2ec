import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/auth/forget_password/domain/forget_password_state.dart';
import 'package:smart_team_web/src/auth/repository/auth_repository.dart';

final forgetPasswordPageProvider =
    Provider.autoDispose<_ForgetPasswordPageProvider>(_ForgetPasswordPageProvider.new);

class _ForgetPasswordPageProvider {
  _ForgetPasswordPageProvider(this.ref);
  final Ref ref;
  ForgetPasswordState state = const ForgetPasswordState();

  void setEmail(String email) {
    state = state.copyWith(email: email);
  }

  void setNewPassword(String newPassword) {
    state = state.copyWith(newPassword: newPassword);
  }

  void setNewPasswordAgain(String newPasswordAgain) {
    state = state.copyWith(newPasswordAgain: newPasswordAgain);
  }

  Future<bool> sendMail() async {
    return ref.read(authRepositoryProvider).sendPasswordResetMail(state.email);
  }

  Future<bool> updatePassword() async {
    return ref.read(authRepositoryProvider).updateUserPassword(state.newPassword);
  }
}

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/router/auto_router.gr.dart';
import 'package:smart_team_web/src/auth/forget_password/application/forget_password_page_provider.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/enums/form_type.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/shared/providers/form_key_provider.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/toast/toast_manager.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/scaffold/app_scaffold.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';

part 'widgets/forget_form.dart';
part 'widgets/send_mail_form.dart';
part 'widgets/link_expired.dart';

@RoutePage(name: 'ForgetPasswordRoute')
class ForgetPasswordPage extends HookConsumerWidget {
  const ForgetPasswordPage({
    super.key,
    @queryParam this.code,
    @queryParam this.error,
  });

  final String? code;
  final String? error;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isRecoveryMode = code != null;
    final isErrorMode = error != null;
    ref.watch(forgetPasswordPageProvider);

    Widget buildContent() {
      if (isErrorMode) return const _LinkExpired();
      if (isRecoveryMode) return const _ForgetForm();
      return const _SendMailForm();
    }

    return SelectionArea(
      child: AppScaffold(
        denyAppBar: true,
        denyLeading: true,
        denyActions: true,
        body: Center(
          child: Container(
            width: 400,
            decoration: BoxDecoration(
              border: Border.all(color: AColor.loginBorderColor),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: double.infinity,
                  height: 132,
                  color: AColor.primaryColor,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    child: SmartTeamAssets.images.beyaz.image(),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: buildContent(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

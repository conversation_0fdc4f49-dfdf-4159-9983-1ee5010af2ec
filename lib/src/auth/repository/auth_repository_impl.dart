part of 'auth_repository.dart';

class _AuthRepository implements IAuthRepository {
  _AuthRepository(this._supabaseClient, this._environment);

  final SupabaseClient _supabaseClient;
  final IEnvironment _environment;

  @override
  Future<String?> signIn(String email, String password) async {
    try {
      final response = await _supabaseClient.auth.signInWithPassword(
        email: email,
        password: password,
      );

      return response.user?.id;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> logOut() async {
    try {
      await _supabaseClient.auth.signOut();
    } catch (e) {
      throw Exception("Çık<PERSON>ş yapılamadı: ${e.toString()}");
    }
  }

  @override
  bool isLoggedIn() {
    return _supabaseClient.auth.currentSession != null;
  }

  @override
  Future<PanelUserModel?> getCurrentOnlineUser() async {
    final user = _supabaseClient.auth.currentUser;
    if (user == null) return null;
    try {
      return await fetchPanelUser(user.id).timeout(
        const Duration(seconds: 10),
        onTimeout: () => null,
      );
    } catch (e) {
      return null;
    }
  }

   @override
  String? getCurrentOnlineUserId() {
    final user = _supabaseClient.auth.currentUser;
    return user?.id;
  }

  @override
  Future<PanelUserModel?> fetchPanelUser(String userId) async {
    try {
      final userResponse = await _supabaseClient
          .from(PanelUsersCollection.collectionName)
          .select()
          .eq(PanelUsersCollection.id, userId)
          .single();
      return PanelUserModel.fromJson(userResponse);
    } catch (e) {
      throw Exception("Error fetching panel user: $e");
    }
  }
  
  Stream<AuthState> get authStateStream {
    return _supabaseClient.auth.onAuthStateChange;
  }
  
  @override
  Future<bool> sendPasswordResetMail(String email) async {
    try {
      await _supabaseClient.auth.resetPasswordForEmail(email, redirectTo: '${_environment.webUrl}${RoutePaths.forgetPassword}');
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> updateUserPassword(String newPassword) async {
    try {
      await _supabaseClient.auth.updateUser(UserAttributes(password: newPassword));
      return true;
    } catch (e) {
      return false;
    }
  }
}

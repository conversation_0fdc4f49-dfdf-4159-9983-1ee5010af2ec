import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/environment/environment.dart';
import 'package:smart_team_web/core/environment/environment_interface.dart';
import 'package:smart_team_web/core/router/app_router.dart';
import 'package:smart_team_web/src/auth/domain/panel_user_model.dart';
import 'package:smart_team_web/src/shared/constants/constants.dart';
import 'package:smart_team_web/src/shared/supabase_collections/panel_users_collection.dart';

part 'auth_repository_impl.dart';

final authRepositoryProvider = Provider.autoDispose<IAuthRepository>(
  (ref) {
    final supabaseClient = ref.watch(supabaseClientProvider);
    final environment = ref.watch(environmentProvider);
    return _AuthRepository(supabaseClient, environment);
  },
);

abstract class IAuthRepository {
  Future<String?> signIn(String email, String password);

  Future<void> logOut();

  bool isLoggedIn();

  Future<PanelUserModel?> getCurrentOnlineUser();

  String? getCurrentOnlineUserId();

  Future<PanelUserModel?> fetchPanelUser(String userId);

  Future<bool> sendPasswordResetMail(String email);

  Future<bool> updateUserPassword(String newPassword);
}

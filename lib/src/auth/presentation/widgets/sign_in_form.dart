part of '../auth_page.dart';

class _SignInForm extends HookConsumerWidget {
  const _SignInForm();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const iconSize = 22.0;
    final authProvider = ref.watch(authPageProvider);
    final formKey = ref.watch(formKeyProvider(FormType.login));

    Future<void> onLoginButtonPressed() async {
      if (formKey.currentState!.validate()) {
        final isLoggedIn = await ref
            .read(onlineUserManagerProvider.notifier)
            .signIn(
                authProvider.authState.email, authProvider.authState.password);
        if (!context.mounted) return;
        if (isLoggedIn && context.mounted) {
          ref.read(toastManagerProvider).showToast('<PERSON><PERSON><PERSON> başarılı'.hardcoded);
          await context.router.replaceAll([const AdminRoute()]);
        } else {
          ref
              .read(toastManagerProvider)
              .showToastErrorWithMessage('<PERSON><PERSON><PERSON> başarısız'.hardcoded);
        }
      }
    }

    return Form(
      key: formK<PERSON>,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              spacing: 24,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                CustomTextFormField(
                  hintText: 'Mail'.hardcoded,
                  keyboardType: TextInputType.emailAddress,
                  textInputAction: TextInputAction.next,
                  suffixIcon: const Icon(
                    Icons.mail_outline,
                    size: iconSize,
                  ),
                  onChanged: ref.read(authPageProvider).setEmail,
                  validator: (value) => value.isValidMail(context),
                  regexType: RegexType.eMail,
                ),
                CustomTextFormField(
                  hintText: context.tr.password,
                  keyboardType: TextInputType.visiblePassword,
                  textInputAction: TextInputAction.next,
                  obscureText: true,
                  suffixIcon: const Icon(
                    Icons.lock,
                    size: iconSize,
                  ),
                  onChanged: ref.read(authPageProvider).setPassword,
                  validator: (value) => value.isValidPassword(context),
                ),
                LoadingElevatedButton(
                  width: context.width,
                  height: 45,
                  onPressed: onLoginButtonPressed,
                  text: context.tr.enter,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

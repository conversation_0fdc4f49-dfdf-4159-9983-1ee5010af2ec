import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/online_user_manager/online_user_manager.dart';
import 'package:smart_team_web/core/router/auto_router.gr.dart';
import 'package:smart_team_web/src/auth/application/auth_page_provider.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/enums/form_type.dart';
import 'package:smart_team_web/src/shared/enums/regex_type.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/shared/providers/form_key_provider.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/toast/toast_manager.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/language_switcher.dart';
import 'package:smart_team_web/src/widgets/scaffold/app_scaffold.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';

part 'widgets/sign_in_form.dart';

@RoutePage()
class AuthPage extends HookConsumerWidget {
  const AuthPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppScaffold(
      denyAppBar: true,
      denyLeading: true,
      denyActions: true,
      body: Center(
        child: Container(
          width: 400,
          decoration: BoxDecoration(
            border: Border.all(color: AColor.loginBorderColor),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: double.infinity,
                height: 132,
                color: AColor.primaryColor,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  child: SmartTeamAssets.images.beyaz.image(),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const _SignInForm(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const LanguageSwitcher(),
                        InkWell(
                          onTap: () {
                            context.router.push(ForgetPasswordRoute());
                          },
                          child: Text(
                            context.tr.forgetPass,
                            style: ATextStyle.text14SemiBold.copyWith(
                              color: AColor.primaryColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

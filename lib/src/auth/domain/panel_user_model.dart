// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:smart_team_web/src/devices/domain/user_device_model.dart';
import 'package:smart_team_web/src/shared/converters/timestamp_converter.dart';
import 'package:smart_team_web/src/shared/enums/panel_user_role_enum.dart';

part 'panel_user_model.freezed.dart';
part 'panel_user_model.g.dart';

@freezed
abstract class PanelUserModel with _$PanelUserModel {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory PanelUserModel({
    required String id,
    required PanelUserRole role,
    required String username,
    String? email,
    String? phone,
    String? nameSurname,
    UserDeviceModel? userDevice,
    @TimestampConverter.auto() required DateTime createdAt,
    @TimestampConverter.auto() DateTime? updatedAt,
  }) = _PanelUserModel;

  const PanelUserModel._();

  factory PanelUserModel.fromJson(Map<String, dynamic> json) =>
      _$PanelUserModelFromJson(json);

  bool get isAdmin => role == PanelUserRole.admin;
}

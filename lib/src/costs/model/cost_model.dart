import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:smart_team_web/src/shared/converters/cost_status_converter.dart';
import 'package:smart_team_web/src/shared/enums/cost_status_enum.dart';

part 'cost_model.freezed.dart';
part 'cost_model.g.dart';

@freezed
abstract class Cost with _$Cost {
  const factory Cost({
    required String employee,
    required String costType,
    @CostStatusConverter() required CostStatus status,
    required String amount,
    required String expenseDate,
    required String approvalDate,
    required String createdDate,
    required String documentStatus,
    required String action,
  }) = _Cost;

  factory Cost.fromJson(Map<String, dynamic> json) => _$CostFromJson(json);
}

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ATextStyle {
  // New text styles
  static TextStyle text32 = GoogleFonts.rubik(
    fontSize: 32,
    fontWeight: FontWeight.w400,
  );

  static TextStyle text32SemiBold = GoogleFonts.rubik(
    fontSize: 32,
    fontWeight: FontWeight.w600,
  );

  static TextStyle text32Bold = GoogleFonts.rubik(
    fontSize: 32,
    fontWeight: FontWeight.w700,
  );

  static TextStyle text32ExtraBold = GoogleFonts.rubik(
    fontSize: 32,
    fontWeight: FontWeight.w800,
  );

  // 24px text styles
  static TextStyle text24 = GoogleFonts.rubik(
    fontSize: 24,
    fontWeight: FontWeight.w400,
  );

  static TextStyle text24SemiBold = GoogleFonts.rubik(
    fontSize: 24,
    fontWeight: FontWeight.w600,
  );

  static TextStyle text24Bold = GoogleFonts.rubik(
    fontSize: 24,
    fontWeight: FontWeight.w700,
  );

  static TextStyle text24ExtraBold = GoogleFonts.rubik(
    fontSize: 24,
    fontWeight: FontWeight.w800,
  );

  // 20px text styles
  static TextStyle text20 = GoogleFonts.rubik(
    fontSize: 20,
    fontWeight: FontWeight.w400,
  );

  static TextStyle text20SemiBold = GoogleFonts.rubik(
    fontSize: 20,
    fontWeight: FontWeight.w600,
  );

  static TextStyle text20Bold = GoogleFonts.rubik(
    fontSize: 20,
    fontWeight: FontWeight.w700,
  );

  // 18px text styles
  static TextStyle text18 = GoogleFonts.rubik(
    fontSize: 18,
    fontWeight: FontWeight.w400,
  );

  static TextStyle text18SemiBold = GoogleFonts.rubik(
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );

  static TextStyle text18Bold = GoogleFonts.rubik(
    fontSize: 18,
    fontWeight: FontWeight.w700,
  );

  static TextStyle text18ExtraBold = GoogleFonts.rubik(
    fontSize: 18,
    fontWeight: FontWeight.w800,
  );

  // 16px text styles
  static TextStyle text16 = GoogleFonts.rubik(
    fontSize: 16,
    fontWeight: FontWeight.w400,
  );

  static TextStyle text16SemiBold = GoogleFonts.rubik(
    fontSize: 16,
    fontWeight: FontWeight.w600,
  );

  static TextStyle text16Bold = GoogleFonts.rubik(
    fontSize: 16,
    fontWeight: FontWeight.w700,
  );

  static TextStyle text16ExtraBold = GoogleFonts.rubik(
    fontSize: 16,
    fontWeight: FontWeight.w800,
  );

  // 13px text styles
  static TextStyle text13 = GoogleFonts.rubik(
    fontSize: 13,
    fontWeight: FontWeight.w400,
  );

  static TextStyle text13SemiBold = GoogleFonts.rubik(
    fontSize: 13,
    fontWeight: FontWeight.w600,
  );

  static TextStyle text13Bold = GoogleFonts.rubik(
    fontSize: 13,
    fontWeight: FontWeight.w700,
  );

  static TextStyle text13ExtraBold = GoogleFonts.rubik(
    fontSize: 13,
    fontWeight: FontWeight.w800,
  );

  // 14px text styles
  static TextStyle text14 = GoogleFonts.rubik(
    fontSize: 14,
    fontWeight: FontWeight.w400,
  );

  static TextStyle text14SemiBold = GoogleFonts.rubik(
    fontSize: 14,
    fontWeight: FontWeight.w600,
  );

  static TextStyle text14Bold = GoogleFonts.rubik(
    fontSize: 14,
    fontWeight: FontWeight.w700,
  );

  static TextStyle text14ExtraBold = GoogleFonts.rubik(
    fontSize: 14,
    fontWeight: FontWeight.w800,
  );

  // 12px text styles
  static TextStyle text12 = GoogleFonts.rubik(
    fontSize: 12,
    fontWeight: FontWeight.w400,
  );

  static TextStyle text12SemiBold = GoogleFonts.rubik(
    fontSize: 12,
    fontWeight: FontWeight.w600,
  );

  static TextStyle text12Bold = GoogleFonts.rubik(
    fontSize: 12,
    fontWeight: FontWeight.w700,
  );

  static TextStyle text12ExtraBold = GoogleFonts.rubik(
    fontSize: 12,
    fontWeight: FontWeight.w800,
  );

  static TextStyle text11 = GoogleFonts.rubik(
    fontSize: 11,
    fontWeight: FontWeight.w400,
  );

//-------------------------------------------

  static TextStyle headerRegular = GoogleFonts.rubik(
    fontSize: 32,
    // fontWeight: FontWeight.w400,
  );

  static TextStyle large = GoogleFonts.rubik(
    fontSize: 18,
    // // fontWeight: FontWeight.w600,
  );

  static TextStyle small = GoogleFonts.rubik(
    fontSize: 10,
    // fontWeight: FontWeight.w600,
  );

  static TextStyle mediumRegular = GoogleFonts.rubik(
    fontSize: 14,
    // // fontWeight: FontWeight.w400,
  );

  static TextStyle medium = GoogleFonts.rubik(
    fontSize: 14,
    // // fontWeight: FontWeight.w600,
  );

  static TextStyle semiLargeRegular = GoogleFonts.rubik(
    fontSize: 16,
    // // fontWeight: FontWeight.w400,
  );

  static TextStyle semiLarge = GoogleFonts.rubik(
    fontSize: 16,
    // // fontWeight: FontWeight.w700,
  );

  static TextStyle xLarge = GoogleFonts.rubik(
    fontSize: 20,
    // // fontWeight: FontWeight.w800,
  );

  static TextStyle xxLarge = GoogleFonts.rubik(
    fontSize: 22,
    // // fontWeight: FontWeight.w700,
  );

  static TextStyle semiSmallRegular = GoogleFonts.rubik(
    fontSize: 13,
    // // fontWeight: FontWeight.w400,
  );

  static TextStyle semiSmall = GoogleFonts.rubik(
    fontSize: 12,
    // // fontWeight: FontWeight.w700,
  );
}

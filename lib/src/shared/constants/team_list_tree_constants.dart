import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/constants.dart';

class TeamListTreeConstants {
  const TeamListTreeConstants._();

  static double leftContainerWidth(BuildContext ctx) {
    final w = ctx.width;

    final ratio = w > kBigScreenBreakpoint ? 0.15 : 0.25;
    return (w * ratio).clamp(
      ctx.responsive(desktop: 200, tablet: 150),
      ctx.responsive(desktop: 300, tablet: 250),
    );
  }

  static double leftContainerHeight(BuildContext ctx) {
    return ctx.responsive(
      desktop: 800,
      tablet: 600,
    );
  }
}

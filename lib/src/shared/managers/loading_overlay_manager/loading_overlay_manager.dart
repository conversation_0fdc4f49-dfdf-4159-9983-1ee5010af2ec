import 'package:hooks_riverpod/hooks_riverpod.dart';

final loadingOverlayManagerProvider = NotifierProvider<LoadingOverlayManager, bool>(
  LoadingOverlayManager.new,
);

class LoadingOverlayManager extends Notifier<bool> {
  @override
  bool build() => false;

  void showLoadingOverlay() {
    state = true;
  }

  void hideLoadingOverlay() {
    state = false;
  }

  void toggleLoadingOverlay() {
    state = !state;
  }
}

import 'package:dio/dio.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/core/environment/environment.dart';

final networkManagerProvider = Provider.autoDispose<NetworkManager>(NetworkManager.new);

class NetworkManager {
  NetworkManager(this.ref) {
    init();
  }

  final Ref ref;

  final Dio _dio = Dio();

  void init() {
    final environment = ref.read(environmentProvider);
    _dio.options.baseUrl = environment.firebaseFunctionsUrl;
  }

  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
  }) =>
      _dio.get<T>(path, queryParameters: queryParameters, cancelToken: cancelToken);
}

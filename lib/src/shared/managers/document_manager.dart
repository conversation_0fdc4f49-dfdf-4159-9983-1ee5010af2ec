import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/shared/services/document_service.dart';
import 'package:smart_team_web/src/toast/toast_manager.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:syncfusion_flutter_datagrid_export/export.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart';

final documentManagerProvider = Provider.autoDispose<DocumentManager>(DocumentManager.new);

/// Manager for document operations
class DocumentManager {
  DocumentManager(this.ref);

  final Ref ref;

  Future<void> createExcelFromWorkbook({required Workbook workbook, String? fileName}) async {
    final result = await ref.read(documentServiceProvider).createExcelFromWorkbook(
          workbook: workbook,
          fileName: fileName,
        );
    result.pick(
      onError: (error) {
        ref.read(toastManagerProvider).showToastError(error);
      },
    );
  }

  Future<void> createAndDownloadPDF({required PdfGrid grid, String? fileName}) async {
    final result = await ref.read(documentServiceProvider).createAndDownloadPDF(
          grid: grid,
          fileName: fileName,
        );
    result.pick(
      onError: (error) {
        ref.read(toastManagerProvider).showToastError(error);
      },
    );
  }
}

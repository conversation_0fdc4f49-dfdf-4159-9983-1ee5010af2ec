import 'package:json_annotation/json_annotation.dart';
import 'package:smart_team_web/src/shared/enums/cost_status_enum.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';

class CostStatusConverter implements JsonConverter<CostStatus, String> {
  const CostStatusConverter();

  @override
  CostStatus fromJson(String json) {
    switch (json) {
      case 'Onay Bekliyor':
        return CostStatus.pending;
      case 'Onaylandı':
        return CostStatus.approved;
      case 'Reddedildi':
        return CostStatus.rejected;
      default:
        throw Exception('Bilinmeyen cost status: $json');
    }
  }

  @override
  String toJson(CostStatus object) {
    switch (object) {
      case CostStatus.pending:
        return 'Onay Bekliyor'.hardcoded;
      case CostStatus.approved:
        return 'Onaylandı'.hardcoded;
      case CostStatus.rejected:
        return 'Reddedildi'.hardcoded;
    }
  }
}

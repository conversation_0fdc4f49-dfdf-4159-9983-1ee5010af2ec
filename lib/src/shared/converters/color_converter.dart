import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

class ColorConverter implements JsonConverter<Color?, String?> {
  const ColorConverter();

  @override
  Color? fromJson(String? json) {
    if (json == null) return null;
    // Assuming the color is in the format "#RRGGBB"
    return Color(int.parse(json.replaceAll('#', '0x')));
  }

  @override
  String? toJson(Color? object) {
    if (object == null) return null;
    // Convert the double components to integers by multiplying by 255 and rounding
    final int alpha = (object.a * 255).round();
    final int red = (object.r * 255).round();
    final int green = (object.g * 255).round();
    final int blue = (object.b * 255).round();

    // Convert each component to a two-digit hex string
    return '#${alpha.toRadixString(16).padLeft(2, '0')}${red.toRadixString(16).padLeft(2, '0')}${green.toRadixString(16).padLeft(2, '0')}${blue.toRadixString(16).padLeft(2, '0')}';
  }
}

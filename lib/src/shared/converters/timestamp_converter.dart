import 'package:json_annotation/json_annotation.dart';
import 'package:smart_team_web/src/shared/utils/dt_format.dart';
import 'package:smart_team_web/src/shared/utils/dt_util.dart';

/// A JSON converter that converts between [DateTime] objects and JSON strings,
/// with optional automatic timestamp.
class TimestampConverter implements JsonConverter<DateTime?, String?> {
  /// Creates a new [TimestampConverter] instance.
  ///
  /// [autoTimestamp] determines whether to automatically add the current
  /// timestamp when converting to JSON.
  const TimestampConverter({
    required this.autoTimestamp,
  });

  /// Whether to automatically add the current timestamp when converting to JSON.
  final bool autoTimestamp;

  /// The format used for date and time conversions.
  final DTFormat format = DTFormat.json;

  /// Creates a new [TimestampConverter] instance with automatic timestamp.
  const TimestampConverter.auto() : autoTimestamp = true;

  /// Creates a new [TimestampConverter] instance without automatic timestamp.
  const TimestampConverter.manual() : autoTimestamp = false;

  /// Converts a JSON string to a [DateTime] object.
  ///
  /// Returns `null` if the conversion fails.
  @override
  DateTime? fromJson(String? json) {
    try {
      return DTUtil.stringToDT(
        json,
        format: format,
        utc: true,
      );
    } catch (e) {
      return null;
    }
  }

  /// Converts a [DateTime] object to a JSON string.
  ///
  /// If [object] is `null` and [autoTimestamp] is `true`, uses the current
  /// timestamp instead. Returns `null` if the conversion fails.
  @override
  String? toJson(DateTime? object) {
    try {
      final time = object ?? (autoTimestamp ? DateTime.now() : null);
      if (time == null) return null;

      return time.toUtc().toIso8601String();
    } catch (_) {
      return null;
    }
  }
}

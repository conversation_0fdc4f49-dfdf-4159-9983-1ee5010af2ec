import 'dart:async';
import 'dart:convert';
// ignore: avoid_web_libraries_in_flutter
import 'dart:html';

import 'package:data_channel/data_channel.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/error/domain/alert_model.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart';

final documentServiceProvider =
    Provider.autoDispose<IDocumentService>((_) => _DocumentService());

abstract class IDocumentService {
  Future<DC<AlertModel, void>> createExcelFromWorkbook(
      {required Workbook workbook, String? fileName});
  Future<DC<AlertModel, void>> createAndDownloadPDF(
      {required PdfGrid grid, String? fileName});
}

class _DocumentService extends IDocumentService {
  @override
  Future<DC<AlertModel, void>> createExcelFromWorkbook(
      {required Workbook workbook, String? fileName}) async {
    try {
      final bytes = workbook.saveSync();
      workbook.dispose();

      _downloadFile(bytes: bytes, fileName: '${fileName ?? 'output'}.xlsx');
      return DC.data(null);
    } catch (e) {
      return DC.error(AlertModel.fromExceptionObject(exception: e));
    }
  }

  @override
  Future<DC<AlertModel, void>> createAndDownloadPDF(
      {required PdfGrid grid, String? fileName}) async {
    try {
      final pdfDocument = PdfDocument();

      final pdfPage = pdfDocument.pages.add();
      // Required to render Turkish characters
      final fontFamily = await _readFontData();
      final font = PdfTrueTypeFont(fontFamily, 12);

      grid.style.font = font;
      grid.draw(
        page: pdfPage,
        bounds: Rect.zero,
      );

      final bytes = pdfDocument.saveSync();
      pdfDocument.dispose();
      _downloadFile(bytes: bytes, fileName: '${fileName ?? 'output'}.pdf');
      return DC.data(null);
    } catch (e) {
      return DC.error(AlertModel.fromExceptionObject(exception: e));
    }
  }

  Future<List<int>> _readFontData() async {
    final data = await rootBundle
        .load('packages/smart_team_common/assets/fonts/rubik.ttf');
    return data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);
  }

  void _downloadFile({
    required List<int> bytes,
    required String fileName,
  }) {
    AnchorElement(
      href:
          'data:application/octet-stream;charset=utf-16le;base64,${base64.encode(bytes)}',
    )
      ..setAttribute('download', fileName)
      ..click();
  }
}

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/customers/domain/country_state_city/country_state_city_model.dart';
import 'package:smart_team_web/src/customers/repository/add_new_customer_repository.dart';

final countryFutureProvider = FutureProvider<List<Country>>((ref) async {
  final repository = ref.read(addNewCustomerRepositoryProvider);
  return repository.getCountries();
});

import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/online_user_manager/online_user_manager.dart';
import 'package:smart_team_web/src/shared/extensions/riverpod_extensions.dart';

final usersForCompanyProvider = AutoDisposeAsyncNotifierProvider<UsersForCompany, PaginatedDataModel<User>>(
  UsersForCompany.new,
);

class UsersForCompany extends AutoDisposeAsyncNotifier<PaginatedDataModel<User>> with PaginationMixin<User> {
  @override
  Future<PaginatedDataModel<User>> build() async {
    final onlineUser = ref.futureValue(onlineUserManagerProvider);
    if (onlineUser == null) {
      return const PaginatedDataModel<User>();
    }
    final data = await getData(
      filters: {'company_id': onlineUser.user!.company.id},
    );
    return data;
  }

  Future<void> fetchNextUsers() async {
    final onlineUser = ref.futureValue(onlineUserManagerProvider);
    if (onlineUser == null) {
      return;
    }
    await getNextData(
      filters: {'company_id': onlineUser.user!.company.id},
    );
  }
}

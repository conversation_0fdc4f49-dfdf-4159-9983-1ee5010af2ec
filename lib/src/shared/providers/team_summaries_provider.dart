import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/global_state_manager/global_state_manager.dart';

final teamSummariesFutureProvider = FutureProvider.autoDispose<List<TeamSummaryModel>>((ref) async {
  final teamRepository = ref.read(teamRepositoryProvider);
  final companyId = ref.watch(globalStateManagerProvider.select((state) => state.selectedCompany?.id));
  if (companyId == null) {
    throw Exception('Company ID is null');
  }
  final teamSummaries = await teamRepository.fetchTeamSummariesByCompanyId(companyId);
  return teamSummaries;
});

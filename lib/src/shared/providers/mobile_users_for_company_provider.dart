import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/online_user_manager/online_user_manager.dart';
import 'package:smart_team_web/src/shared/extensions/riverpod_extensions.dart';

final mobileUsersForCompanyProvider = AutoDisposeAsyncNotifierProvider<MobileUsersForCompany, PaginatedDataModel<MobileUser>>(
  MobileUsersForCompany.new,
);

class MobileUsersForCompany extends AutoDisposeAsyncNotifier<PaginatedDataModel<MobileUser>> with PaginationMixin<MobileUser> {
  @override
  Future<PaginatedDataModel<MobileUser>> build() async {
    final onlineUser = ref.futureValue(onlineUserManagerProvider);
    if (onlineUser == null) {
      return const PaginatedDataModel<MobileUser>();
    }
    final data = await getData(
      filters: {'company_id': onlineUser.user!.company.id},
    );
    return data;
  }

  Future<void> fetchNextUsers() async {
    final onlineUser = ref.futureValue(onlineUserManagerProvider);
    if (onlineUser == null) {
      return;
    }
    await getNextData(
      filters: {'company_id': onlineUser.user!.company.id},
    );
  }
}

import 'package:flutter/material.dart';
import 'package:smart_team_web/src/theme/colors.dart';

class CommonDecorations {
  static BoxDecoration containerDecoration({
    Color? backgroundColor,
    double borderRadius = 4,
    Color? borderColor,
    List<BoxShadow>? boxShadow,
  }) {
    return BoxDecoration(
      color: backgroundColor ?? AColor.white,
      borderRadius: BorderRadius.circular(borderRadius),
      border: borderColor == null
          ? null
          : Border.all(
              color: borderColor,
            ),
      // boxShadow: boxShadow ?? defaultBoxShadow(),
    );
  }

  static BoxDecoration textFieldDecoration({
    double borderRadius = 4,
    Color? borderColor,
    List<BoxShadow>? boxShadow,
  }) {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(borderRadius),
      border: Border.all(
        color: borderColor ?? Colors.grey.shade300,
      ),
    );
  }
}

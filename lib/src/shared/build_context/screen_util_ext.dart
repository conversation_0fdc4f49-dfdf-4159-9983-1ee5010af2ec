import 'package:flutter/cupertino.dart';
import 'package:smart_team_web/src/shared/constants/constants.dart';

extension ScreenUtilExt on BuildContext {
  double get height => MediaQuery.sizeOf(this).height;
  double get width => MediaQuery.sizeOf(this).width;

  T responsive<T>({
    required T desktop,
    T? mobile,
    T? tablet,
    T? fourK,
  }) {
    final w = width;
    if (w > kFourKBreakpoint) return fourK ?? desktop;
    if (w > kBigScreenBreakpoint) return desktop;
    if (w > kMidScreenBreakpoint) return tablet ?? desktop;
    return mobile ?? tablet ?? desktop;
  }
}

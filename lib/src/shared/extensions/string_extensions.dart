import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:smart_team_web/src/shared/enums/regex_type.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';

/// Validation options for string validation
class ValidationOptions {

  const ValidationOptions({
    this.allowEmpty = false,
    this.trim = true,
    this.customErrorMessage,
  });
  final bool allowEmpty;
  final bool trim;
  final String? customErrorMessage;
}

/// Extension for basic string operations
extension StringX on String? {
  String? get capitalizeFirst {
    if (this == null || this!.trim().isEmpty) {
      return this;
    }
    return this!.substring(0, 1).toUpperCase() + this!.substring(1);
  }
}

/// Extension for string validation operations
extension ValidationExtensions on String? {
  /// Base validation method that handles common validation logic
  String? _validate(
    BuildContext context, {
    required RegexType regexType,
    ValidationOptions options = const ValidationOptions(),
    String? customEmptyMessage,
  }) {
    final value = options.trim ? this?.trim() : this;
    
    if (value == null || value.isEmpty) {
      if (options.allowEmpty) return null;
      return customEmptyMessage ?? context.tr.fieldRequired;
    }

    if (options.customErrorMessage != null) {
      return regexType.regex.hasMatch(value) ? null : options.customErrorMessage;
    }

    return regexType.regex.hasMatch(value) ? null : regexType.getErrorMessage(context);
  }

  /// Validates if the string is a valid ID
  String? isValidId(
    BuildContext context, {
    ValidationOptions options = const ValidationOptions(),
  }) {
    return _validate(
      context,
      regexType: RegexType.id,
      options: options,
    );
  }

  /// Validates if the string is a valid email address
  String? isValidMail(
    BuildContext context, {
    ValidationOptions options = const ValidationOptions(),
  }) {
    return _validate(
      context,
      regexType: RegexType.eMail,
      options: options,
    );
  }

  /// Validates if the string is a valid password
  String? isValidPassword(
    BuildContext context, {
    ValidationOptions options = const ValidationOptions(),
  }) {
    return _validate(
      context,
      regexType: RegexType.password,
      options: options,
    );
  }

  /// Validates if the string matches the given password
  String? isValidPasswordAgain(
    BuildContext context,
    String password, {
    ValidationOptions options = const ValidationOptions(),
  }) {
    final validationResult = _validate(
      context,
      regexType: RegexType.password,
      options: options,
    );
    
    if (validationResult != null) return validationResult;
    if (this != password) return 'Şifreler eşleşmiyor'.hardcoded;
    return null;
  }

  /// Validates if the string is a valid OTP
  String? isValidOTP(
    BuildContext context, {
    ValidationOptions options = const ValidationOptions(),
  }) {
    return _validate(
      context,
      regexType: RegexType.otp,
      options: options,
    );
  }

  /// Validates if the string is a valid postal code
  String? isValidPostalCode(
    BuildContext context, {
    ValidationOptions options = const ValidationOptions(),
  }) {
    return _validate(
      context,
      regexType: RegexType.postalCode,
      options: options,
    );
  }

  /// Validates if the string is a valid name
  String? isValidName(
    BuildContext context, {
    ValidationOptions options = const ValidationOptions(),
  }) {
    return _validate(
      context,
      regexType: RegexType.name,
      options: options,
    );
  }

  /// Validates if the string is a valid phone number
  String? isValidPhone(
    BuildContext context, {
    ValidationOptions options = const ValidationOptions(),
  }) {
    return _validate(
      context,
      regexType: RegexType.phone,
      options: options,
    );
  }

  String? isValidBrand(
    BuildContext context, {
    ValidationOptions options = const ValidationOptions(),
  }) {
    return _validate(
      context,
      regexType: RegexType.brand,
      options: options,
    );
  }

  String? isValidModel(
    BuildContext context, {
    ValidationOptions options = const ValidationOptions(),
  }) {
    return _validate(
      context,
      regexType: RegexType.model,
      options: options,
    );
  }

  String? isValidDecimal(
    BuildContext context, {
    ValidationOptions options = const ValidationOptions(),
  }) {
    return _validate(
      context,
      regexType: RegexType.decimal,
      options: options,
    );
  }

  /// Validates if the string is not empty
  String? isValidField(
    BuildContext context, {
    ValidationOptions options = const ValidationOptions(),
  }) {
    final value = options.trim ? this?.trim() : this;
    if (value == null || value.isEmpty) {
      if (options.allowEmpty) return null;
      return options.customErrorMessage ?? context.tr.fieldRequired;
    }
    return null;
  }

  /// Validates if the string is a valid web address
  String? isValidWebAddress(
    BuildContext context, {
    ValidationOptions options = const ValidationOptions(),
  }) {
    return _validate(
      context,
      regexType: RegexType.webAddress,
      options: options,
    );
  }

  /// Validates if the string is a valid IP address
  String? isValidIpAddress(
    BuildContext context, {
    ValidationOptions options = const ValidationOptions(),
  }) {
    return _validate(
      context,
      regexType: RegexType.ipAddress,
      options: options,
    );
  }

  /// Validates if the string is a valid server port
  String? isValidServerPort(
    BuildContext context, {
    ValidationOptions options = const ValidationOptions(),
  }) {
    return _validate(
      context,
      regexType: RegexType.serverPort,
      options: options,
    );
  }

  /// Validates if the string is a valid latitude/longitude
  String? isValidLatLng(
    BuildContext context, {
    ValidationOptions options = const ValidationOptions(),
  }) {
    return _validate(
      context,
      regexType: RegexType.latLng,
      options: options,
    );
  }

  /// Validates if the string is a valid building door number
  String? isValidBuildingDoor(
    BuildContext context, {
    ValidationOptions options = const ValidationOptions(),
  }) {
    return _validate(
      context,
      regexType: RegexType.buildingDoor,
      options: options,
    );
  }
}

/// Extension for string conversion operations
extension ConverterExtensions on String? {
  /// Converts a string in format "latitude,longitude" to a LatLng object
  LatLng? toLatLng() {
    if (this == null || this!.trim().isEmpty) {
      return null;
    }
    final parts = this!.trim().split(',');
    final lat = double.tryParse(parts[0].trim());
    final lng = double.tryParse(parts[1].trim());
    if (lat == null || lng == null) {
      return null;
    }
    return LatLng(lat, lng);
  }
}

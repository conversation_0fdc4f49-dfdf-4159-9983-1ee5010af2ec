extension ListX on List<dynamic> {
  /// 1. `hasDataAtIndex`:
  ///    - Checks if a given index exists in the list.
  ///    - Returns `true` if the index is valid (exists within the bounds of the list), otherwise `false`.
  ///
  ///    Example Usage:
  ///    ```dart
  ///    final list = [1, 2, 3];
  ///    print(list.hasDataAtIndex(1)); // Output: true
  ///    print(list.hasDataAtIndex(5)); // Output: false
  ///    ```
  ///
  bool hasDataAtIndex(int index) {
    return asMap().containsKey(index);
  }

  ///`replaceAt`:
  ///    - Replaces the element at the specified index with the provided value.
  ///    - If the index is valid, the element at that index will be updated.
  ///    - Note: Throws an `IndexOutOfRange` exception if the index is out of bounds.
  ///
  ///    Example Usage:
  ///    ```dart
  ///    final list = [1, 2, 3];
  ///    list.replaceAt(1, 42);
  ///    print(list); // Output: [1, 42, 3]
  ///    ```
  void replaceAt<T>(int index, T replacement) {
    this[index] = replacement;
  }
}

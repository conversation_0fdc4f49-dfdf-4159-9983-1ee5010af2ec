import 'package:smart_team_web/src/shared/enums/panel_user_role_enum.dart';

extension PanelUserRoleExtension on PanelUserRole {
  String get name {
    switch (this) {
      case PanelUserRole.superAdmin:
        return 'Super Admin';
      case PanelUserRole.admin:
        return 'Admin';
      case PanelUserRole.moderator:
        return 'Moderator';
      case PanelUserRole.user:
        return 'User';
    }
  }

  static PanelUserRole fromString(String role) {
    switch (role) {
      case 'Super Admin':
        return PanelUserRole.superAdmin;
      case 'Admin':
        return PanelUserRole.admin;
      case 'Moderator':
        return PanelUserRole.moderator;
      case 'User':
        return PanelUserRole.user;
      default:
        throw ArgumentError('Invalid user role: $role');
    }
  }
}

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:smart_team_web/src/widgets/date_time_picker/date_time_picker.dart';

extension DateTimeX on DateTime {
  /// Converts the date to the format dd.MM.yyyy (25.05.2025)
  String getFormValue({DateTimePickerMode mode = DateTimePickerMode.date}) {
    switch (mode) {
      case DateTimePickerMode.date:
        return DateFormat('dd.MM.yyyy').format(this);
      case DateTimePickerMode.time:
        return DateFormat('HH:mm').format(this);
      case DateTimePickerMode.dateAndTime:
        return DateFormat('dd.MM.yyyy HH:mm').format(this);
    }
  }

  TimeOfDay toTimeOfDay() => TimeOfDay(hour: hour, minute: minute);
}

extension TimeOfDayX on TimeOfDay {
  String get formValue => DateFormat('HH:mm').format(toDateTime());

  DateTime toDateTime({DateTime? date}) => DateTime(date?.year ?? 0, date?.month ?? 0, date?.day ?? 0, hour, minute);
}

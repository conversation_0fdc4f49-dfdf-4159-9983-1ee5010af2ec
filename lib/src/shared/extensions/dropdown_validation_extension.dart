import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';

extension DropdownValidationExtensions on Object? {
  String? isNotEmptyDropdown(BuildContext context) {
    if (this == null) {
      return context.tr.fieldRequired;
    }
    return null;
  }

  String? isValidDropdownOption(
    BuildContext context,
    List<dynamic> validOptions,
  ) {
    if (this == null) {
      return context.tr.fieldRequired;
    }
    if (!validOptions.contains(this)) {
      return 'context.tr.pleaseSelectValid'.hardcoded;
    }
    return null;
  }
}

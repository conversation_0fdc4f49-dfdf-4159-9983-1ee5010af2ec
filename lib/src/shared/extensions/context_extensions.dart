import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:smart_team_web/i18n/strings.g.dart';

extension BuildContextX on BuildContext {
  Translations get tr {
    return Translations.of(this);
  }

  String? getQueryRouteParameter(String key) {
    return routeData.queryParams.optString(key);
  }

  String? getPathRouteParameter(String key) {
    return routeData.params.optString(key);
  }
}

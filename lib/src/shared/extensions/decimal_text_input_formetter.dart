import 'package:flutter/services.dart';
import 'package:smart_team_web/src/shared/enums/regex_type.dart';

class DecimalTextInputFormatter extends TextInputFormatter {
  DecimalTextInputFormatter({this.allowNegative = false});
  final bool allowNegative;

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    final regex = allowNegative
        ? RegexType.decimalAllowNegative.regex
        : RegexType.decimalNoNegative.regex;

    if (regex.hasMatch(text) || text.isEmpty) {
      return newValue;
    }

    return oldValue;
  }
}

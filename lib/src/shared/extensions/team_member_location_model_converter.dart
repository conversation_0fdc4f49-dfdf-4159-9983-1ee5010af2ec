import 'package:smart_team_web/src/home/<USER>/team_member.dart';
import 'package:smart_team_web/src/map/domain/team_member_location_model.dart';

extension TeamMemberExtensions on WebTeamMemberModel {
  TeamMemberLocationModel toLocationModel({
    required double lat,
    required double lng,
    double speed = 0.0,
    String address = '',
    DateTime? updatedAt,
  }) {
    return TeamMemberLocationModel(
      id: id,
      userName: userName,
      lat: lat,
      lng: lng,
      speed: speed,
      address: address,
      updatedAt: updatedAt,
    );
  }
}

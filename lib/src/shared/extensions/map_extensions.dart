import 'package:google_maps_flutter/google_maps_flutter.dart';

extension LatLngBoundsExtension on Set<Marker> {
  LatLngBounds createLatLngBounds() {
    assert(isNotEmpty);
    double x0 = first.position.latitude;
    double x1 = first.position.latitude;
    double y0 = first.position.longitude;
    double y1 = first.position.longitude;

    for (Marker marker in this) {
      if (marker.position.latitude > x1) x1 = marker.position.latitude;
      if (marker.position.latitude < x0) x0 = marker.position.latitude;
      if (marker.position.longitude > y1) y1 = marker.position.longitude;
      if (marker.position.longitude < y0) y0 = marker.position.longitude;
    }

    return LatLngBounds(
      southwest: LatLng(x0, y0),
      northeast: LatLng(x1, y1),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';

enum CostStatus {
  pending,
  approved,
  rejected,
}

extension CostStatusX on CostStatus {
  String label(BuildContext context) {
    switch (this) {
      case CostStatus.pending:
        return 'Onay Bekliyor'.hardcoded;
      case CostStatus.approved:
        return 'Onaylandı'.hardcoded;
      case CostStatus.rejected:
        return 'Reddedildi'.hardcoded;
    }
  }
}

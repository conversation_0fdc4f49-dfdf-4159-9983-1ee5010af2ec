import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';

enum RegexType {
  eMail(
    r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+",
  ),
  password(
    r'^.{6,20}$',
  ),
  otp(
    r'^[0-9]{6}$',
  ),

  // This regex allows alphanumeric characters, spaces, and hyphens (adjust as needed).
  postalCode(
    r'^[A-Za-z0-9\s-]+$',
  ),

  // A simple name regex that allows letters (including accented), spaces, apostrophes, and hyphens.
  name(
    r"^[A-Za-zÀ-ÖØ-öø-ÿ\s'-]{2,}$",
  ),

  // A phone number regex that allows an optional leading plus, numbers, spaces, and hyphens.
  phone(
    r'^\+?[0-9\s-]{7,15}$',
  ),

  // Regex for ID: Only numbers, no length restriction
  id(
    r'^[0-9]+$',
  ),

  // Regex for web address http/https validation
  webAddress(
    r'^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$',
  ),

  // Regex for IPv4 address
  ipAddress(
    r'^((25[0-5]|(2[0-4]\d|1\d\d|[1-9]?\d))\.){3}(25[0-5]|(2[0-4]\d|1\d\d|[1-9]?\d))$',
  ),

  // Regex for server port between 1 and 65535
  serverPort(
    r'^(6553[0-5]|655[0-2]\d|65[0-4]\d{2}|6[0-4]\d{3}|[1-5]\d{4}|[1-9]\d{0,3})$',
  ),

  // Regex for latitude and longitude: '41.12345,29.98765'
  latLng(
    r'^-?\d+(\.\d+)?\s*,\s*-?\d+(\.\d+)?$',
  ),

  // Regex for searching no in search field for add new customer
  buildingDoor(r'^(no|nu)([.:])?( ?\d+(\/\d+)?)?$'),

  // Decimal values
  decimal(r'^\d+([.,]?\d+)?$'),
  decimalNoNegative(r'^\d+([.,]\d*)?$'),
  decimalAllowNegative(r'^-?\d+([.,]\d*)?$'),

  // Vehicle
  brand(r"^[A-Za-zÀ-ÖØ-öø-ÿ\s'\-\.]{2,}$"),
  model(r"^[A-Za-z0-9À-ÖØ-öø-ÿ\s'\-\.]{1,}$"),
  ;

  const RegexType(this.pattern);
  final String pattern;
  
  RegExp get regex => RegExp(pattern);
  
  String getErrorMessage(BuildContext context) {
    switch (this) {
      case RegexType.eMail:
        return context.tr.enterValidEmail;
      case RegexType.password:
        return context.tr.enterValidPass;
      case RegexType.otp:
        return context.tr.enterValidOTP;
      case RegexType.postalCode:
        return context.tr.enterValidPostalCode;
      case RegexType.name:
        return context.tr.enterValidName;
      case RegexType.phone:
        return context.tr.enterValidPhone;
      case RegexType.id:
        return context.tr.enterValidId;
      case RegexType.webAddress:
        return context.tr.enterValidWebAddress;
      case RegexType.ipAddress:
        return context.tr.enterValidIpAddress;
      case RegexType.serverPort:
        return context.tr.enterValidServerPort;
      case RegexType.latLng:
        return context.tr.enterValidLatLng;
      case RegexType.buildingDoor:
        return context.tr.enterValidBuildingDoor;
      case RegexType.decimal:
        return 'Lütfen geçerli bir sayı giriniz.'.hardcoded;
      case RegexType.decimalNoNegative:
        return 'Lütfen geçerli bir sayı giriniz.'.hardcoded;
      case RegexType.decimalAllowNegative:
        return 'Lütfen geçerli bir sayı giriniz.'.hardcoded;
      case RegexType.brand:
        return 'Lütfen geçerli bir marka giriniz.'.hardcoded;
      case RegexType.model:
        return 'Lütfen geçerli bir model giriniz.'.hardcoded;
    }
  }
}

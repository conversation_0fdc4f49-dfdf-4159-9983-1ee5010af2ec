import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';

enum PanelUserRole {
  superAdmin,
  admin,
  moderator,
  user,
}

extension PanelUserRoleX on PanelUserRole {
  String label(BuildContext context) {
    switch (this) {
      case PanelUserRole.superAdmin:
        return 'Super Admin'.hardcoded;
      case PanelUserRole.admin:
        return 'Admin'.hardcoded;
      case PanelUserRole.moderator:
        return 'Moderator'.hardcoded;
      case PanelUserRole.user:
        return 'User'.hardcoded;
    }
  }
}

class DisplayPanelUserRole {
  DisplayPanelUserRole({
    required this.panelUserRole,
    required BuildContext context,
  }) : label = panelUserRole.label(context);

  final PanelUserRole panelUserRole;
  final String label;

  @override
  String toString() => label;

  static List<DisplayPanelUserRole> getList(BuildContext context) =>
      PanelUserRole.values
          .map(
            (role) => DisplayPanelUserRole(
              panelUserRole: role,
              context: context,
            ),
          )
          .toList();
}

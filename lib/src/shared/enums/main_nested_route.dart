import 'package:auto_route/auto_route.dart';
import 'package:smart_team_web/core/router/auto_router.gr.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';

enum MainNestedRoute {
  dashboard,
  reports,
  calendar,
  jobList,
  forms,
  settings,
  performance,
  permits,
  costs,
  personalManagement,
  admin,
  myProfile,
  mapScreen,
  personalDetail;

  PageRouteInfo get route {
    return switch (this) {
      dashboard => const DashboardRoute(),
      reports => const ReportsRoute(),
      calendar => const CalendarRoute(),
      jobList => const JobListRoute(),
      forms => const FormsRoute(),
      settings => const SettingsRoute(),
      performance => const PerformanceRoute(),
      permits => const PermitsRoute(),
      costs => const CostsRoute(),
      personalManagement => const PersonalManagementRoute(),
      admin => const AdminRoute(),
      myProfile => const MyProfileRoute(),
      mapScreen => DynamicMapRoute(),
      personalDetail => const PersonalDetailRoute(),
    };
  }

  String? getTitle() {
    return switch (this) {
      reports => 'Reports'.hardcoded,
      calendar => 'Calendar'.hardcoded,
      jobList => 'Job List'.hardcoded,
      forms => 'Forms'.hardcoded,
      settings => 'Settings'.hardcoded,
      performance => 'Performance'.hardcoded,
      permits => 'Permits'.hardcoded,
      costs => 'Costs'.hardcoded,
      personalManagement => 'Personal Management'.hardcoded,
      admin => 'Admin'.hardcoded,
      myProfile => 'My Profile'.hardcoded,
      mapScreen => 'Map Screen'.hardcoded,
      personalDetail => 'Personal Detail'.hardcoded,
      _ => null,
    };
  }
}

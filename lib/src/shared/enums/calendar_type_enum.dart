import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';

enum CalendarType {
  day,
  week,
  month,
}

extension CalendarTypeExtension on CalendarType {
  String displayName(BuildContext context) {
    switch (this) {
      case CalendarType.day:
        return context.tr.day;
      case CalendarType.week:
        return context.tr.week;
      case CalendarType.month:
        return context.tr.month;
    }
  }
}

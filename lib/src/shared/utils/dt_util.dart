import 'package:intl/intl.dart';
import 'package:smart_team_web/src/shared/utils/dt_format.dart';

class DTUtil {
  static DateTime? stringToDT(
    String? stringDateTime, {
    DTFormat format = DTFormat.json,
    bool utc = false,
  }) =>
      stringDateTime == null
          ? null
          : DateFormat(format.key).parse(stringDateTime, utc).toLocal();

  static String dtToString(
    DateTime? date, {
    DTFormat format = DTFormat.json,
    String languageCode = 'tr',
  }) {
    if (date == null) {
      return '';
    }
    return DateFormat(
            format.key,
            // appLocale.languageCode,//TODO(Ali): s=>warning , u=>ali , m=> check this
            languageCode)
        .format(date);
  }
}

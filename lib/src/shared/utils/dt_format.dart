enum DTFormat {
  ///2023-07-07T10:31:15.344Z
  /// [yyyy-MM-ddTHH:mm:ss]
  json(key: 'yyyy-MM-ddTHH:mm:ss'),

  ///2023-07-07 10:31:15
  /// [yyyy-MM-dd HH:mm:ss]
  simple(key: 'yyyy-MM-dd HH:mm:ss'),

  /// [dd.MM.yyyy HH:mm]
  human1(key: 'dd.MM.yyyy HH:mm'),

  /// [dd.MM.yyyy]
  dayMonthYear(key: 'dd.MM.yyyy'),

  /// [HH:mm]
  time(key: 'HH:mm'),

  /// [yyyy-MM-dd]
  date(key: 'yyyy-MM-dd'),

  /// readable
  /// [MMMM yyyy]
  rMonthYear(key: 'MMMM yyyy');

  const DTFormat({required this.key});

  final String key;
}

//accepted formats
// "2012-02-27 13:27:00"
// "2012-02-27 13:27:00.123456789z"
// "2012-02-27 13:27:00,123456789z"
// "20120227 13:27:00"
// "20120227T132700"
// "20120227"
// "+20120227"
// "2012-02-27T14Z"
// "2012-02-27T14+00:00"
// "-123450101 00:00:00 Z": in the year -12345.
// "2002-02-27T14:00:00-0500": Same as "2002-02-27T19:00:00Z"
// print(new DateFormat('').parse('')); // 1970-01-01 00:00:00.000
// print(new DateFormat('MM').parse('02')); // 1970-02-01 00:00:00.000
// print(new DateFormat('yyyy').parse('2020')); // 2020-01-01 00:00:00.000
// print(new DateFormat('MM/yyyy').parse('01/2020')); // 2020-01-01 00:00:00.000
// print(new DateFormat('MMM yyyy').parse('Feb 2020')); // 2020-02-01 00:00:00.000
// print(new DateFormat('MMMM yyyy').parse('March 2020')); // 2020-03-01 00:00:00.000
// print(new DateFormat('yyyy/MM/dd').parse('2020/04/03')); // 2020-04-03 00:00:00.000
// print(new DateFormat('yyyy/MM/dd HH:mm:ss').parse('2020/04/03 17:03:02')); // 2020-04-03 17:03:02.000
// print(new DateFormat('EEEE dd MMMM yyyy HH:mm:ss \'GMT\'').parse('Wednesday 28 October 2020 01:02:03 GMT')); // 2020-10-28 01:02:03.000
// print(new DateFormat('EEE d/LLLL/yyyy hh:mm:ss G').parse('Wed 10/January/2020 15:02:03 BC')); // 2020-01-10 15:02:03.000
// print(new DateFormat('yyyyy.MMMM.dd GGG hh:mm aaa').parse('02020.July.10 AD 11:09 PM')); // 2020-07-10 23:09:00.000

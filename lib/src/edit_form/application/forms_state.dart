import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:smart_team_common/smart_team_common.dart';

part 'forms_state.freezed.dart';

@freezed
abstract class FormsState with _$FormsState {
  const factory FormsState({
    required FormTemplate formTemplate,
    required FormSubmission formSubmission,
    @Default([]) List<FormTemplateField> fields,
    @Default([]) List<FormFieldOption> fieldOptions,
    @Default([]) List<FormUser> assignedUsers,
    @Default([]) List<FormSubmissionAnswer> answers,
  }) = _FormsState;

  const FormsState._();

  List<FormFieldOption> getOptionsForField(FormTemplateField field) {
    return fieldOptions.where((option) => option.field.id == field.id).toList();
  }

  ({int index, FormSubmissionAnswer? answer}) getAnswerForField(
      FormTemplateField field) {
    final answerIndex =
        answers.indexWhere((answer) => answer.field.id == field.id);
    if (answerIndex == -1) return (index: answerIndex, answer: null);
    return (
      index: answerIndex,
      answer: answerIndex == -1 ? null : answers[answerIndex]
    );
  }

  /// Duplicates the form state with new IDs for all the entities.
  FormsState duplicateWithNewId() {
    final newTemplate = formTemplate.copyWith(id: UUIDGenerator.generate(),createdAt: null,updatedAt: null);

    // Create new fields with new IDs and store a mapping of old ID to new ID
    final newFieldsWithMapping = fields.map((field) {
      final newId = UUIDGenerator.generate();
      return (
        oldId: field.id,
        newField: field.copyWith(formTemplate: newTemplate, id: newId, createdAt: null, updatedAt: null)
      );
    }).toList();

    final newFields = newFieldsWithMapping.map((f) => f.newField).toList();

    final fieldIdMap = {
      for (final item in newFieldsWithMapping) item.oldId: item.newField,
    };

    final newFieldOptions = fieldOptions.map((option) {
      final newField = fieldIdMap[option.field.id];
      return option.copyWith(
        id: null,
        field: newField ?? option.field,
        
      );
    }).toList();

    return copyWith(
      formTemplate: newTemplate,
      fields: newFields,
      fieldOptions: newFieldOptions,
      assignedUsers: assignedUsers
          .map((user) => user.copyWith(formTemplate: newTemplate, id: null, createdAt: null))
          .toList(),
    );
  }
}

import 'dart:async';

import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/media_manager/media_manager.dart';
import 'package:smart_team_web/core/managers/online_user_manager/online_user_manager.dart';
import 'package:smart_team_web/src/edit_form/application/forms_state.dart';
import 'package:smart_team_web/src/shared/extensions/list_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/riverpod_extensions.dart';

final editFormAsyncNotifierProvider = AutoDisposeAsyncNotifierProviderFamily<
    EditFormAsyncNotifier, FormsState, String>(
  EditFormAsyncNotifier.new,
);

class EditFormAsyncNotifier
    extends AutoDisposeFamilyAsyncNotifier<FormsState, String> {
  bool isEditing = false;
  @override
  FutureOr<FormsState> build(String arg) async {
    final onlineUser = ref.futureValue(onlineUserManagerProvider)!;
    final formTemplate =
        await ref.read(formTemplateRepositoryProvider).fetchFormTemplate(arg);
    if (formTemplate == null) {
      final generatedFormTemplate = _generateFormTemplate();
      isEditing = false;
      return FormsState(
        formTemplate: generatedFormTemplate,
        formSubmission:
            _generateFormSubmission(formTemplate: generatedFormTemplate),
      );
    }
    final formTemplateFields = await ref
        .read(formTemplateFieldRepositoryProvider)
        .fetchFormTemplateFields(formTemplate.id!);
    final formTemplateFieldOptions = await ref
        .read(formFieldOptionRepositoryProvider)
        .fetchOptionsForFields(formTemplateFields);
    FormSubmission? formSubmission;
    if (onlineUser.user != null) {
      formSubmission = await ref
          .read(formSubmissionRepositoryProvider)
          .fetchFormSubmissionForUser(formTemplate.id!, onlineUser.user!.id!);
    }
    var formAnswers = <FormSubmissionAnswer>[];
    if (formSubmission != null) {
      formAnswers = await ref
          .read(formSubmissionAnswerRepositoryProvider)
          .fetchAnswersForSubmission(formSubmission.id!);
    }
    isEditing = true;
    return FormsState(
      formTemplate: formTemplate,
      fields: formTemplateFields,
      fieldOptions: formTemplateFieldOptions,
      formSubmission:
          formSubmission ?? _generateFormSubmission(formTemplate: formTemplate),
      answers: formAnswers,
    );
  }

  List<FormTemplateField> _adjustDisplayOrderForFields(
    List<FormTemplateField> fields,
  ) =>
      fields.indexed.map((entry) {
        return entry.$2.copyWith(displayOrder: entry.$1);
      }).toList();

  void updateTitle(String title) {
    if (state.value == null) return;
    state = AsyncData(state.value!.copyWith.formTemplate(title: title));
  }

  void updateDescription(String description) {
    if (state.value == null) return;
    state =
        AsyncData(state.value!.copyWith.formTemplate(description: description));
  }

  void updatePriority(FormPriorityEnum priority) {
    if (state.value == null) return;
    state = AsyncData(state.value!.copyWith.formTemplate(priority: priority));
  }

  void updateStartDate(DateTime startDate) {
    if (state.value == null) return;
    var endDate = state.value!.formTemplate.endDate;
    if (endDate != null && endDate.isBefore(startDate)) {
      endDate = null;
    }
    state = AsyncData(
      state.value!.copyWith
          .formTemplate(startDate: startDate, endDate: endDate),
    );
  }

  void updateEndDate(DateTime endDate) {
    if (state.value == null) return;
    state = AsyncData(state.value!.copyWith.formTemplate(endDate: endDate));
  }

  void setFormPublic({required bool isPublic}) {
    if (state.value == null) return;
    final newFormTemplate =
        state.value!.formTemplate.copyWith(isPublic: isPublic);
    state = AsyncData(
      state.value!.copyWith(formTemplate: newFormTemplate, assignedUsers: []),
    );
  }

  void toggleAssignedUser(User user) {
    if (state.value == null) return;
    final userIndex =
        state.value!.assignedUsers.indexWhere((u) => u.user.id == user.id);
    final containsUser = userIndex != -1;
    if (containsUser) {
      state = AsyncData(
        state.value!.copyWith(
          assignedUsers: [...state.value!.assignedUsers]..removeAt(userIndex),
        ),
      );
    } else {
      final newFormUser = FormUser(
        id: UUIDGenerator.generate(),
        user: user,
        formTemplate: state.value!.formTemplate,
      );
      state = AsyncData(
        state.value!.copyWith(
          assignedUsers: [...state.value!.assignedUsers, newFormUser],
        ),
      );
    }
  }

  void addField() {
    if (state.value == null) return;
    final formTemplateField = FormTemplateField(
      id: UUIDGenerator.generate(),
      formTemplate: state.value!.formTemplate,
      label: '',
      fieldType: FormFieldTypeEnum.text,
      displayOrder: state.value!.fields.length,
    );
    final updatedFields = [...state.value!.fields, formTemplateField];
    state = AsyncData(
      state.value!
          .copyWith(fields: _adjustDisplayOrderForFields(updatedFields)),
    );
  }

  void addOptionToField(FormTemplateField field) {
    if (state.value == null) return;
    final formFieldOption = FormFieldOption(
      id: UUIDGenerator.generate(),
      field: field,
      optionLabel: '',
      optionValue: '',
    );
    state = AsyncData(
      state.value!.copyWith(
        fieldOptions: [...state.value!.fieldOptions, formFieldOption],
      ),
    );
  }

  void removeOption(String optionId) {
    if (state.value == null) return;
    final fieldOptions = state.value!.fieldOptions;
    final updatedFieldOptions =
        [...fieldOptions].where((option) => option.id != optionId).toList();
    state = AsyncData(
      state.value!.copyWith(fieldOptions: updatedFieldOptions),
    );
  }

  void setValueForOption(String optionId, String value) {
    if (state.value == null) return;
    final fieldOptions = state.value!.fieldOptions;
    final optionIndex =
        fieldOptions.indexWhere((option) => option.id == optionId);
    final updatedOption =
        fieldOptions[optionIndex].copyWith(optionValue: value);
    final updatedFieldOptions = [...fieldOptions]
      ..replaceAt(optionIndex, updatedOption);
    state = AsyncData(
      state.value!.copyWith(fieldOptions: updatedFieldOptions),
    );
  }

  void removeAllOptionsForField(FormTemplateField field) {
    if (state.value == null) return;
    final fieldOptions = state.value!.fieldOptions;
    final updatedFieldOptions =
        fieldOptions.where((option) => option.field.id != field.id).toList();
    state = AsyncData(state.value!.copyWith(fieldOptions: updatedFieldOptions));
  }

  void reorderField(int oldIndex, int newIndex) {
    if (state.value == null) return;

    var indexToMove = newIndex;
    if (newIndex > oldIndex) {
      indexToMove = newIndex - 1;
    }

    final fields = [...state.value!.fields];

    final movedField = fields.removeAt(oldIndex);

    fields.insert(indexToMove, movedField);

    state = AsyncData(
      state.value!.copyWith(fields: _adjustDisplayOrderForFields(fields)),
    );
  }

  void toggleFieldRequired(int fieldIndex) {
    final fields = state.value!.fields;
    final oldField = fields[fieldIndex];
    final updatedField = oldField.copyWith(isRequired: !oldField.isRequired!);
    final updatedFields = [...fields]..replaceAt(fieldIndex, updatedField);
    state = AsyncData(state.value!.copyWith(fields: updatedFields));
  }

  void updateFieldLabel(int fieldIndex, String label) {
    final fields = state.value!.fields;
    final updatedField = fields[fieldIndex].copyWith(label: label);
    final updatedFields = [...fields]..replaceAt(fieldIndex, updatedField);
    state = AsyncData(state.value!.copyWith(fields: updatedFields));
  }

  List<FormSubmissionAnswer> deleteAnswerForField(
    FormTemplateField field, {
    bool updateState = true,
  }) {
    final answers = state.value!.answers;
    final answerInfo = state.value!.getAnswerForField(field);
    if (answerInfo.answer == null) return answers;
    final updatedAnswers = [...answers]..removeAt(answerInfo.index);
    if (updateState) {
      state = AsyncData(state.value!.copyWith(answers: updatedAnswers));
    }
    return updatedAnswers;
  }

  void updateFieldType(int fieldIndex, FormFieldTypeEnum fieldType) {
    final fields = state.value!.fields;
    final oldField = fields[fieldIndex];
    if (oldField.fieldType == fieldType) return;
    final updatedField = oldField.copyWith(fieldType: fieldType);
    final updatedFields = [...fields]..replaceAt(fieldIndex, updatedField);
    final updatedAnswers = deleteAnswerForField(oldField, updateState: false);
    if (!fieldType.hasOptions) {
      final updatedFieldOptions = state.value!.fieldOptions
          .where((option) => option.field.id != updatedField.id)
          .toList();
      state = AsyncData(
        state.value!.copyWith(
          fields: updatedFields,
          fieldOptions: updatedFieldOptions,
          answers: updatedAnswers,
        ),
      );
    } else {
      state = AsyncData(
        state.value!.copyWith(fields: updatedFields, answers: updatedAnswers),
      );
    }
  }

  void deleteField(int fieldIndex) {
    final fields = state.value!.fields;
    final fieldToDelete = fields[fieldIndex];
    final updatedFieldOptions = [...state.value!.fieldOptions]
        .where((option) => option.field.id != fieldToDelete.id)
        .toList();
    final updatedFields = [...fields]..removeAt(fieldIndex);
    state = AsyncData(
      state.value!.copyWith(
        fields: _adjustDisplayOrderForFields(updatedFields),
        fieldOptions: updatedFieldOptions,
      ),
    );
  }

  void duplicateField(int fieldIndex) {
    final fields = state.value!.fields;
    final fieldToDuplicate = fields[fieldIndex];
    final fieldOptions = state.value!.getOptionsForField(fieldToDuplicate);
    final duplicatedFieldUUID = UUIDGenerator.generate();
    final duplicatedField = fieldToDuplicate.copyWith(id: duplicatedFieldUUID);
    final updatedFields = [
      ...fields,
      duplicatedField,
    ];
    final updatedFieldOptions = [
      ...state.value!.fieldOptions,
      ...fieldOptions.map(
        (option) => option.copyWith(field: duplicatedField, id: UUIDGenerator.generate()),
      ),
    ];
    state = AsyncData(
      state.value!.copyWith(
        fields: _adjustDisplayOrderForFields(updatedFields),
        fieldOptions: updatedFieldOptions,
      ),
    );
  }

  void updateValidationRule(
    int fieldIndex,
    ValidationRuleEnum? validationRule,
  ) {
    final fields = state.value!.fields;
    final oldField = fields[fieldIndex];
    final updatedField = oldField.copyWith(validationRule: validationRule);
    final updatedFields = [...fields]..replaceAt(fieldIndex, updatedField);
    final updatedAnswers = deleteAnswerForField(oldField, updateState: false);
    state = AsyncData(
      state.value!.copyWith(fields: updatedFields, answers: updatedAnswers),
    );
  }

  void setAnswerForField(FormTemplateField field, List<String> newAnswers) {
    final answers = state.value!.answers;
    final answerValue =
        newAnswers.length == 1 ? newAnswers.first : newAnswers.toString();
    final answerIndex =
        answers.indexWhere((answer) => answer.field.id == field.id);
    if (answerIndex == -1) {
      final answer = FormSubmissionAnswer(
        id: UUIDGenerator.generate(),
        field: field,
        answerValue: answerValue,
        submission: state.value!.formSubmission,
      );
      final updatedAnswers = [...answers, answer];
      state = AsyncData(state.value!.copyWith(answers: updatedAnswers));
    } else {
      final answer = answers[answerIndex].copyWith(answerValue: answerValue);
      final updatedAnswers = [...answers]..replaceAt(answerIndex, answer);
      state = AsyncData(state.value!.copyWith(answers: updatedAnswers));
    }
  }

  Future<List<XFile>> pickFilesForField(FormTemplateField field) async {
    final pickedFiles = await ref.read(mediaManagerProvider).pickFile(
      allowMultiple: field.validationRule != null,
      allowExtensions: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'png'],
    );
    return pickedFiles ?? [];
  }

  Future<bool> createTemplate() async {
    if (state.value == null) return false;
    final formState =
        isEditing ? state.value!.duplicateWithNewId() : state.value!;
    final formTemplateCreated = await ref
        .read(formTemplateRepositoryProvider)
        .createFormTemplate(formState.formTemplate);
    if (!formTemplateCreated) return false;
    final fieldsCreated = await ref
        .read(formTemplateFieldRepositoryProvider)
        .createTemplateFields(formState.fields);
    if (!fieldsCreated) return false;
    final optionsCreated = await ref
        .read(formFieldOptionRepositoryProvider)
        .createFormFieldOptions(formState.fieldOptions);
    if (!optionsCreated) return false;
    final formUsersCreated = await ref
        .read(formUserRepositoryProvider)
        .createFormUsers(formState.assignedUsers);
    if (!formUsersCreated) return false;
    return true;
  }

  Future<bool> postFormAnswers() async {
    if (state.value == null) return false;
    final formState = state.value!;
    final isSubmissionCreated = await ref
        .read(formSubmissionRepositoryProvider)
        .create(state.value!.formSubmission);
    if (!isSubmissionCreated) return false;
    return ref
        .read(formSubmissionAnswerRepositoryProvider)
        .createList(formState.answers);
  }

  FormTemplate _generateFormTemplate() {
    final onlineUser = ref.futureValue(onlineUserManagerProvider)!;
    return FormTemplate(
      id: arg,
      title: 'Untitled form',
      company: onlineUser.user!.company,
      createdByUser: onlineUser.user!,
      priority: FormPriorityEnum.high,
    );
  }

  FormSubmission _generateFormSubmission({required FormTemplate formTemplate}) {
    final onlineUser = ref.futureValue(onlineUserManagerProvider)!;
    return FormSubmission(
      id: UUIDGenerator.generate(),
      formTemplate: formTemplate,
      submittedByUser: onlineUser.user,
      status: FormSubmissionStatusEnum.submitted,
    );
  }
}

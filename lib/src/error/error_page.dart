import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/scaffold/app_scaffold.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

class ErrorPage extends StatelessWidget {
  const ErrorPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      title: context.tr.error,
      denyLeading: true,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            TextWidget(
              context.tr.error,
              style: ATextStyle.medium.copyWith(
                color: AColor.textColor,
              ),
            ),
            const Sized<PERSON>ox(
              height: 20,
            ),
            ElevatedButton(
              onPressed: () {
                // context.replaceRoute(const AdminAuthRoute());
              },
              child: TextWidget(
                context.tr.cancel,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

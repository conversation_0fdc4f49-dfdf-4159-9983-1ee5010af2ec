import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

part 'alert_model.freezed.dart';

enum AlertType {
  constructive,
  destructive,
  error,
  notification,
  exception,
  quiet
}

@freezed
abstract class AlertModel with _$AlertModel {
  const factory AlertModel({
    required String message,
    required AlertType type,
    int? code,
  }) = _AlertModel;

  factory AlertModel.alert({
    required String message,
    required AlertType type,
    int? code,
  }) {
    return AlertModel(
      message: message,
      type: type,
      code: code,
    );
  }

  factory AlertModel.exception({
    required Exception exception,
    int? code,
  }) {
    return AlertModel(
      message: exception.toString(),
      type: AlertType.exception,
      code: code,
    );
  }

  factory AlertModel.fromExceptionObject({
    required Object exception,
  }) {
    if (exception is AuthException) {
      return AlertModel.alert(
        message: exception.toString(),
        type: AlertType.error,
      );
    }

    if (exception is Exception) {
      return AlertModel.exception(exception: exception);
    }

    return AlertModel.alert(
      message: exception.toString(),
      type: AlertType.error,
    );
  }
}

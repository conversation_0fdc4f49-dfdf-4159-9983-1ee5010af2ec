import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/home/<USER>/team_member.dart';
import 'package:smart_team_web/src/home/<USER>/mock_data.dart';
import 'package:smart_team_web/src/rules/application/add_new_rules_provider.dart';
import 'package:smart_team_web/src/rules/application/area_entry_exit_settings_provider.dart';
import 'package:smart_team_web/src/rules/enum/rule_type.dart';
import 'package:smart_team_web/src/rules/mock/devices_provider.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/enums/regex_type.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/shared/utils/dt_format.dart';
import 'package:smart_team_web/src/shared/utils/dt_util.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/toast/toast_manager.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/button/press_icon_button.dart';
import 'package:smart_team_web/src/widgets/form_fields/dropdown_formfield.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_column_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_row_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/st_data_table.dart';
import 'package:smart_team_web/src/widgets/multi_select_dropdown/multi_select_dropdown_widget.dart';
import 'package:smart_team_web/src/widgets/switch/custom_switch.dart';
import 'package:smart_team_web/src/widgets/team_select_dropdown/team_select_dropdown_widget.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

part 'widgets/area_entry_exit_body.dart';
part 'widgets/notifications_body.dart';
part 'widgets/speed_violation_body.dart';

class AddNewRuleWidgets extends HookConsumerWidget {
  const AddNewRuleWidgets({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = GlobalKey<FormState>();
    final selectedRuleType = useState<RuleType?>(null);

    final defaultTitle =
        '${context.tr.newRule} ${DTUtil.dtToString(DateTime.now(), format: DTFormat.simple)}';
    final titleController = useTextEditingController(text: defaultTitle);
    final isTitleEdited = useState<bool>(false);
    final isTitleValid =
        !isTitleEdited.value || titleController.text.trim().isNotEmpty;

    final selectedMembers = useState<List<WebTeamMemberModel>>([]);

    final isSpeedViolationFormValid = useState<bool>(false);
    final isNotificationsFormValid = useState<bool>(false);
    final isAreaEntryExitFormValid = useState<bool>(false);

    return Form(
      key: formKey,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          spacing: 16,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              spacing: 8,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: TeamSelectDropdownWidget<WebTeamMemberModel>(
                    header: context.tr.selectDevice,
                    placeholder: context.tr.select,
                    itemList: DashboardMockData.getMembers(),
                    selectedItems: selectedMembers.value,
                    onSelected: (members) {
                      selectedMembers.value = members;
                    },
                    width: double.infinity,
                  ),
                ),
                const Spacer(),
              ],
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: CustomTextFormField(
                    headerText: context.tr.title,
                    controller: titleController,
                    hintText: defaultTitle,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ANewDropdownFormField<DisplayRuleType>(
                    header: context.tr.type,
                    placeholder: context.tr.select,
                    itemList: [
                      DisplayRuleType(
                        rule: RuleType.speedViolation,
                        context: context,
                      ),
                      DisplayRuleType(
                        rule: RuleType.areaEntryExitViolation,
                        context: context,
                      ),
                      DisplayRuleType(
                        rule: RuleType.notifications,
                        context: context,
                      ),
                    ],
                    validator: (value) {
                      if (value == null) {
                        return context.tr.required;
                      }
                      return null;
                    },
                    onSelected: (displayRuleType) {
                      selectedRuleType.value = displayRuleType.rule;
                    },
                    selectedItems: selectedRuleType.value == null
                        ? []
                        : [
                            DisplayRuleType(
                              rule: selectedRuleType.value!,
                              context: context,
                            ),
                          ],
                    itemBuilder: (displayRule) => Text(
                      displayRule == null
                          ? context.tr.select
                          : displayRule.toString(),
                    ),
                  ),
                ),
              ],
            ),
            Builder(
              builder: (context) {
                if (selectedRuleType.value == null) {
                  return const SizedBox.shrink();
                }
                switch (selectedRuleType.value!) {
                  case RuleType.speedViolation:
                    return SpeedViolationBody(
                      onFormValidityChanged: (isValid) {
                        isSpeedViolationFormValid.value = isValid;
                      },
                    );
                  case RuleType.areaEntryExitViolation:
                    return AreaEntryExitBody(
                      onFormValidityChanged: (isValid) {
                        isAreaEntryExitFormValid.value = isValid;
                      },
                    );
                  case RuleType.notifications:
                    return NotificationsBody(
                      onFormValidityChanged: (isValid) {
                        isNotificationsFormValid.value = isValid;
                      },
                    );
                }
              },
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                LoadingElevatedButton(
                  height: 45,
                  onPressed: () async {
                    if (selectedMembers.value.isEmpty) {
                      ref
                          .read(toastManagerProvider)
                          .showToast('Cihaz seçiniz.'.hardcoded);
                      return;
                    }
                    if (formKey.currentState!.validate()) {}
                  },
                  text: context.tr.save,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

part of '../add_new_rules_widgets.dart';

class SpeedViolationBody extends HookConsumerWidget {
  const SpeedViolationBody({
    super.key,
    this.onFormValidityChanged,
  });

  final ValueChanged<bool>? onFormValidityChanged;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(notificationsSettingsProvider);

    final mailAddressesController = useTextEditingController();
    final speedController = useTextEditingController(text: '0');
    final durationController = useTextEditingController(text: '0');
    final selectedDevices = useState<List<String>>([]);

    return Container(
      decoration: CommonDecorations.containerDecoration(),
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 16,
        children: [
          TextWidget(
            'Bu ihlali kullanabilmeniz için seçilecek cihazın sürüş verisinin açık olması gerekmektedir.'
                .hardcoded,
            style: ATextStyle.text13,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 16,
            children: [
              CustomSwitch(
                header: 'Mobil Bildirim'.hardcoded,
                value: controller.mobileNotification,
                onChanged: (value) {
                  ref
                      .read(notificationsSettingsProvider.notifier)
                      .toggleMobileNotification(value);
                },
              ),
              CustomSwitch(
                header: 'Mail Bildirim'.hardcoded,
                value: controller.mailNotification,
                onChanged: (value) {
                  ref
                      .read(notificationsSettingsProvider.notifier)
                      .toggleMailNotification(value);
                },
              ),
              const Spacer(),
            ],
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 16,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 8,
                  children: [
                    TextWidget(
                      'Mail Adresleri (adres arasinda ; kullanınız)'.hardcoded,
                      style: ATextStyle.text13,
                    ),
                    Container(
                      decoration: CommonDecorations.containerDecoration(),
                      child: Visibility(
                        visible: controller.mailNotification,
                        replacement: Container(
                          decoration: CommonDecorations.containerDecoration(
                            backgroundColor: Colors.grey.shade300,
                          ),
                          alignment: Alignment.center,
                          child: CustomTextFormField(
                            fillcolor: Colors.grey.shade300,
                            controller: useTextEditingController(
                              text: 'Mail bildirimi kapalı'.hardcoded,
                            ),
                            enabled: false,
                          ),
                        ),
                        child: CustomTextFormField(
                          controller: mailAddressesController,
                          enabled: controller.mailNotification,
                          regexType: RegexType.eMail,
                          keyboardType: TextInputType.emailAddress,
                          hintText: '',
                          validator: (value) {
                            if (controller.mailNotification) {
                              return value.isValidMail(context);
                            }
                            return null;
                          },
                        ),
                      ),
                    ),
                    Container(
                      decoration: CommonDecorations.containerDecoration(),
                      child: CustomTextFormField(
                        headerText: '${context.tr.speed} (${context.tr.kmh})',
                        controller: speedController,
                        hintText: '',
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(3),
                        ],
                        regexType: RegexType.id,
                        suffixIcon: Padding(
                          padding: const EdgeInsets.all(1),
                          child: Container(
                            decoration: const BoxDecoration(
                              color: AColor.mercury,
                              borderRadius: BorderRadius.only(
                                topRight: Radius.circular(2),
                                bottomRight: Radius.circular(2),
                              ),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                PressIconButton(
                                  icon: const Icon(Icons.arrow_drop_up),
                                  onTap: () {
                                    final currentValue =
                                        int.tryParse(speedController.text) ?? 0;
                                    if (currentValue < 999) {
                                      speedController.text =
                                          (currentValue + 1).toString();
                                    }
                                  },
                                ),
                                PressIconButton(
                                  icon: const Icon(Icons.arrow_drop_down),
                                  onTap: () {
                                    final currentValue =
                                        int.tryParse(speedController.text) ?? 0;
                                    speedController.text =
                                        (currentValue - 1).toString();
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Hız alanı boş kalamaz'.hardcoded;
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 8,
                  children: [
                    TextWidget(
                      context.tr.devices,
                      style: ATextStyle.text13,
                    ),
                    Container(
                      decoration: CommonDecorations.containerDecoration(),
                      child: Visibility(
                        visible: controller.mobileNotification,
                        replacement: Container(
                          decoration: CommonDecorations.containerDecoration(),
                          alignment: Alignment.center,
                          child: CustomTextFormField(
                            fillcolor: Colors.grey.shade300,
                            controller: useTextEditingController(
                              text: 'Mobil bildirim kapalı'.hardcoded,
                            ),
                            enabled: false,
                          ),
                        ),
                        child: MultiSelectDropDown<String>(
                          placeholder: context.tr.select,
                          itemList: devices,
                          selectedItems: selectedDevices.value,
                          onSelected: (devices) {
                            selectedDevices.value = devices;
                          },
                        ),
                      ),
                    ),
                    CustomTextFormField(
                      headerText: 'Süre (Saniye)'.hardcoded,
                      controller: durationController,
                      hintText: '',
                      keyboardType: TextInputType.number,
                      regexType: RegexType.id,
                      suffixIcon: Padding(
                        padding: const EdgeInsets.all(1),
                        child: Container(
                          decoration: const BoxDecoration(
                            color: AColor.mercury,
                            borderRadius: BorderRadius.only(
                              topRight: Radius.circular(2),
                              bottomRight: Radius.circular(2),
                            ),
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              PressIconButton(
                                icon: const Icon(Icons.arrow_drop_up),
                                onTap: () {
                                  final currentValue =
                                      int.tryParse(durationController.text) ??
                                          0;
                                  durationController.text =
                                      (currentValue + 1).toString();
                                },
                              ),
                              PressIconButton(
                                icon: const Icon(Icons.arrow_drop_down),
                                onTap: () {
                                  final currentValue =
                                      int.tryParse(durationController.text) ??
                                          0;
                                  durationController.text =
                                      (currentValue - 1).toString();
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

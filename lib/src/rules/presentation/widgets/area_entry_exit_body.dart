part of '../add_new_rules_widgets.dart';

class AreaEntryExitBody extends HookConsumerWidget {
  const AreaEntryExitBody({super.key, this.onFormValidityChanged});

  final ValueChanged<bool>? onFormValidityChanged;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(notificationsSettingsProvider);
    final areaSettings = ref.watch(areaEntryExitSettingsProvider);

    final mailAddressesController = useTextEditingController();
    final selectedDevices = useState<List<String>>([]);
    final durationController = useTextEditingController(text: '0');
    final selectedField = useState<String?>(null);

    const defs = <({String name, String type})>[
      (name: 'areaName', type: 'areaType'),
    ];

    final checkboxValues = useState(List<bool>.filled(defs.length, false));

    final columns = [
      const TableColumnModel(
        columnName: 'select',
        sortable: false,
        filterable: false,
        minimumWidth: 60,
      ),
      const TableColumnModel(
        columnName: 'Alan Adı',
        sortable: false,
        filterable: false,
        minimumWidth: 150,
      ),
      const TableColumnModel(
        columnName: 'Alan Tipi',
        sortable: false,
        filterable: false,
        minimumWidth: 120,
      ),
    ];

    List<List<RowDataModel>> buildRows() => defs.indexed.map((e) {
          final i = e.$1;
          final fd = e.$2;
          return [
            RowDataModel(
              columnName: 'select',
              cellBuilder: () => Checkbox(
                value: checkboxValues.value[i],
                onChanged: (b) {
                  final l = List<bool>.from(checkboxValues.value)
                    ..[i] = b ?? false;
                  checkboxValues.value = l;
                },
                checkColor: AColor.white,
                activeColor: AColor.primaryColor,
                side: const BorderSide(width: 1.5, color: AColor.grey),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
            RowDataModel(columnName: 'Alan Adı', value: fd.name),
            RowDataModel(columnName: 'Alan Tipi', value: fd.type),
          ];
        }).toList();

    return Container(
      decoration: CommonDecorations.containerDecoration(),
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 16,
        children: [
          TextWidget(
            'Bu ihlali kullanabilmeniz için seçilecek cihazın sürüş verisinin açık olması gerekmektedir.'
                .hardcoded,
            style: ATextStyle.text13,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 16,
            children: [
              CustomSwitch(
                header: 'Mobil Bildirim'.hardcoded,
                value: controller.mobileNotification,
                onChanged: ref
                    .read(notificationsSettingsProvider.notifier)
                    .toggleMobileNotification,
              ),
              CustomSwitch(
                header: 'Mail Bildirim'.hardcoded,
                value: controller.mailNotification,
                onChanged: ref
                    .read(notificationsSettingsProvider.notifier)
                    .toggleMailNotification,
              ),
              const Spacer(),
            ],
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 16,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 8,
                  children: [
                    TextWidget(
                      'Mail Adresleri (adres arasinda ; kullanınız)'.hardcoded,
                      style: ATextStyle.text13,
                    ),
                    Container(
                      decoration: CommonDecorations.containerDecoration(),
                      child: Visibility(
                        visible: controller.mailNotification,
                        replacement: Container(
                          decoration: CommonDecorations.containerDecoration(
                            backgroundColor: Colors.grey.shade300,
                          ),
                          alignment: Alignment.center,
                          child: CustomTextFormField(
                            fillcolor: Colors.grey.shade300,
                            controller: useTextEditingController(
                              text: 'Mail bildirimi kapalı'.hardcoded,
                            ),
                            enabled: false,
                          ),
                        ),
                        child: CustomTextFormField(
                          controller: mailAddressesController,
                          enabled: controller.mailNotification,
                          regexType: RegexType.eMail,
                          keyboardType: TextInputType.emailAddress,
                          hintText: '',
                          validator: (v) => controller.mailNotification
                              ? v.isValidMail(context)
                              : null,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 8,
                  children: [
                    TextWidget(context.tr.devices, style: ATextStyle.text13),
                    Container(
                      decoration: CommonDecorations.containerDecoration(),
                      child: Visibility(
                        visible: controller.mobileNotification,
                        replacement: Container(
                          decoration: CommonDecorations.containerDecoration(
                            backgroundColor: Colors.grey.shade300,
                          ),
                          alignment: Alignment.center,
                          child: CustomTextFormField(
                            fillcolor: Colors.grey.shade300,
                            controller: useTextEditingController(
                              text: 'Mobil bildirim kapalı'.hardcoded,
                            ),
                            enabled: false,
                          ),
                        ),
                        child: MultiSelectDropDown<String>(
                          placeholder: context.tr.select,
                          itemList: devices,
                          selectedItems: selectedDevices.value,
                          onSelected: (d) => selectedDevices.value = d,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 16,
            children: [
              Expanded(
                child: Row(
                  spacing: 8,
                  children: [
                    TextWidget(
                      'Alana Giriş'.hardcoded,
                      style: ATextStyle.text13,
                    ),
                    CustomSwitch(
                      value: areaSettings.areaEntry,
                      onChanged: (v) => ref
                          .read(areaEntryExitSettingsProvider.notifier)
                          .toggleAreaEntry(v),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Row(
                  spacing: 8,
                  children: [
                    TextWidget(
                      'Alandan Çıkış'.hardcoded,
                      style: ATextStyle.text13,
                    ),
                    CustomSwitch(
                      value: areaSettings.areaExit,
                      onChanged: (v) => ref
                          .read(areaEntryExitSettingsProvider.notifier)
                          .toggleAreaExit(v),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Row(
                  spacing: 8,
                  children: [
                    TextWidget(
                      'Alanda Bekleme'.hardcoded,
                      style: ATextStyle.text13,
                    ),
                    CustomSwitch(
                      value: areaSettings.areaWait,
                      onChanged: (v) => ref
                          .read(areaEntryExitSettingsProvider.notifier)
                          .toggleAreaWait(v),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Row(
                  spacing: 8,
                  children: [
                    Flexible(
                      child: TextWidget(
                        'Süre (Dakika)'.hardcoded,
                        style: ATextStyle.text13,
                      ),
                    ),
                    Flexible(
                      child: CustomTextFormField(
                        controller: durationController,
                        keyboardType: TextInputType.number,
                        regexType: RegexType.id,
                        suffixIcon: Padding(
                          padding: const EdgeInsets.all(1),
                          child: Container(
                            decoration: const BoxDecoration(
                              color: AColor.mercury,
                              borderRadius: BorderRadius.only(
                                topRight: Radius.circular(2),
                                bottomRight: Radius.circular(2),
                              ),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                PressIconButton(
                                  icon: const Icon(Icons.arrow_drop_up),
                                  onTap: () {
                                    final v = int.tryParse(
                                          durationController.text,
                                        ) ??
                                        0;
                                    durationController.text =
                                        (v + 1).toString();
                                  },
                                ),
                                PressIconButton(
                                  icon: const Icon(Icons.arrow_drop_down),
                                  onTap: () {
                                    final v = int.tryParse(
                                          durationController.text,
                                        ) ??
                                        0;
                                    durationController.text =
                                        (v - 1).toString();
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(
            height: 300,
            width: double.infinity,
            child: ValueListenableBuilder<List<bool>>(
              valueListenable: checkboxValues,
              builder: (_, __, ___) => SmartTeamDataTable(
                showCheckboxes: true,
                columns: columns,
                rowData: buildRows(),
                checkboxValues: checkboxValues,
                onHeaderCheckboxChanged: (bool? v) {
                  checkboxValues.value =
                      List<bool>.filled(defs.length, v ?? false);
                },
                showExportButtons: false,
                availableRowsPerPage: const [5],
                initialRowsPerPage: 5,
                gridColumnWidthMode: ColumnWidthMode.auto,
                onRowClick: (i) => selectedField.value = defs[i].name,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

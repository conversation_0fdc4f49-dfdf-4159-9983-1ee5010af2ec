part of '../add_new_rules_widgets.dart';

class NotificationsBody extends HookConsumerWidget {
  const NotificationsBody({
    super.key,
    this.onFormValidityChanged,
  });

  final ValueChanged<bool>? onFormValidityChanged;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(notificationsSettingsProvider);

    final mailAddressesController = useTextEditingController();
    final selectedDevices = useState<List<String>>([]);

    return Container(
      decoration: CommonDecorations.containerDecoration(),
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 16,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 16,
            children: [
              CustomSwitch(
                header: 'Mobil Bildirim'.hardcoded,
                value: controller.mobileNotification,
                onChanged: (value) {
                  ref
                      .read(notificationsSettingsProvider.notifier)
                      .toggleMobileNotification(value);
                },
              ),
              CustomSwitch(
                header: 'Mail Bildirim'.hardcoded,
                value: controller.mailNotification,
                onChanged: (value) {
                  ref
                      .read(notificationsSettingsProvider.notifier)
                      .toggleMailNotification(value);
                },
              ),
              const Spacer(),
            ],
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 16,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 8,
                  children: [
                    TextWidget(
                      'Mail Adresleri (adres arasinda ; kullanınız)'.hardcoded,
                      style: ATextStyle.small,
                    ),
                    Container(
                      decoration: CommonDecorations.containerDecoration(),
                      child: Visibility(
                        visible: controller.mailNotification,
                        replacement: Container(
                          decoration: CommonDecorations.containerDecoration(
                            backgroundColor: Colors.grey.shade300,
                          ),
                          alignment: Alignment.center,
                          child: CustomTextFormField(
                            fillcolor: Colors.grey.shade300,
                            controller: useTextEditingController(
                              text: 'Mail bildirimi kapalı'.hardcoded,
                            ),
                            enabled: false,
                          ),
                        ),
                        child: CustomTextFormField(
                          controller: mailAddressesController,
                          enabled: controller.mailNotification,
                          regexType: RegexType.eMail,
                          keyboardType: TextInputType.emailAddress,
                          hintText: '',
                          validator: (value) {
                            if (controller.mailNotification) {
                              return value.isValidMail(context);
                            }
                            return null;
                          },
                        ),
                      ),
                    ),
                    CustomSwitch(
                      header: 'Yeni Takvim Eklendiğinde'.hardcoded,
                      value: controller.newCalendar,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      onChanged: (value) => ref
                          .read(notificationsSettingsProvider.notifier)
                          .toggleNewCalendar(value),
                    ),
                    CustomSwitch(
                      header: 'Atanan Takvimi Kabul Ettiğinde'.hardcoded,
                      value: controller.assignedCalendarAccepted,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      onChanged: (value) => ref
                          .read(notificationsSettingsProvider.notifier)
                          .toggleAssignedCalendarAccepted(value),
                    ),
                    CustomSwitch(
                      header: 'Atanan Takvimi Reddettiğinde'.hardcoded,
                      value: controller.assignedCalendarRejected,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      onChanged: (value) => ref
                          .read(notificationsSettingsProvider.notifier)
                          .toggleAssignedCalendarRejected(value),
                    ),
                    CustomSwitch(
                      header: 'Yeni Müşteri Eklendiğinde'.hardcoded,
                      value: controller.newCustomer,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      onChanged: (value) => ref
                          .read(notificationsSettingsProvider.notifier)
                          .toggleNewCustomer(value),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 8,
                  children: [
                    TextWidget(
                      context.tr.devices,
                      style: ATextStyle.text13,
                    ),
                    Container(
                      decoration: CommonDecorations.containerDecoration(),
                      child: Visibility(
                        visible: controller.mobileNotification,
                        replacement: Container(
                          decoration: CommonDecorations.containerDecoration(
                            backgroundColor: Colors.grey.shade300,
                          ),
                          alignment: Alignment.center,
                          child: CustomTextFormField(
                            fillcolor: Colors.grey.shade300,
                            controller: useTextEditingController(
                              text: 'Mobil bildirim kapalı'.hardcoded,
                            ),
                            enabled: false,
                          ),
                        ),
                        child: MultiSelectDropDown<String>(
                          placeholder: context.tr.select,
                          itemList: devices,
                          selectedItems: selectedDevices.value,
                          onSelected: (devices) {
                            selectedDevices.value = devices;
                          },
                        ),
                      ),
                    ),
                    CustomSwitch(
                      header: 'Yeni İş Eklendiğinde'.hardcoded,
                      value: controller.newJob,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      onChanged: (value) => ref
                          .read(notificationsSettingsProvider.notifier)
                          .toggleNewJob(value),
                    ),
                    CustomSwitch(
                      header: 'Atanan İşi Kabul Ettiğinde'.hardcoded,
                      value: controller.assignedJobAccepted,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      onChanged: (value) => ref
                          .read(notificationsSettingsProvider.notifier)
                          .toggleAssignedJobAccepted(value),
                    ),
                    CustomSwitch(
                      header: 'Atanan İşi Reddettiğinde'.hardcoded,
                      value: controller.assignedJobRejected,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      onChanged: (value) => ref
                          .read(notificationsSettingsProvider.notifier)
                          .toggleAssignedJobRejected(value),
                    ),
                    CustomSwitch(
                      header: 'Müşteriye Etkinlik Girdiğinde'.hardcoded,
                      value: controller.customerEventAdded,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      onChanged: (value) => ref
                          .read(notificationsSettingsProvider.notifier)
                          .toggleCustomerEventAdded(value),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

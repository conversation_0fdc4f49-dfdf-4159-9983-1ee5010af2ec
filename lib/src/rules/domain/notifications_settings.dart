import 'package:freezed_annotation/freezed_annotation.dart';

part 'notifications_settings.freezed.dart';

@freezed
abstract class NotificationsSettings with _$NotificationsSettings {
  const factory NotificationsSettings({
    @Default(false) bool mobileNotification,
    @Default(false) bool mailNotification,
    @Default(false) bool newCalendar,
    @Default(false) bool assignedCalendarAccepted,
    @Default(false) bool assignedCalendarRejected,
    @Default(false) bool newCustomer,
    @Default(false) bool newJob,
    @Default(false) bool assignedJobAccepted,
    @Default(false) bool assignedJobRejected,
    @Default(false) bool customerEventAdded,
  }) = _NotificationsSettings;
}

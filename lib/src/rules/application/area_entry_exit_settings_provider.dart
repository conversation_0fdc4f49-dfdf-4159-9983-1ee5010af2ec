import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/rules/domain/area_entry_exit/area_entry_exit_settings.dart';

final areaEntryExitSettingsProvider =
    NotifierProvider<AreaEntryExitSettingsController, AreaEntryExitSettings>(
  AreaEntryExitSettingsController.new,
);

class AreaEntryExitSettingsController extends Notifier<AreaEntryExitSettings> {
  @override
  AreaEntryExitSettings build() => const AreaEntryExitSettings();

  void toggleAreaEntry(bool val) {
    state = state.copyWith(areaEntry: val);
  }

  void toggleAreaExit(bool val) {
    state = state.copyWith(areaExit: val);
  }

  void toggleAreaWait(bool val) {
    state = state.copyWith(areaWait: val);
  }
}

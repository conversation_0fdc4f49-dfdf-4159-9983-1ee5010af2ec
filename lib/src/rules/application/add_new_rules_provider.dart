import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/rules/domain/notifications_settings.dart';

final notificationsSettingsProvider =
    NotifierProvider<NotificationsSettingsController, NotificationsSettings>(
  NotificationsSettingsController.new,
);

class NotificationsSettingsController extends Notifier<NotificationsSettings> {
  @override
  NotificationsSettings build() {
    return const NotificationsSettings();
  }

  void toggleMobileNotification(bool val) {
    state = state.copyWith(mobileNotification: val);
  }

  void toggleMailNotification(bool val) {
    state = state.copyWith(mailNotification: val);
  }

  void toggleNewCalendar(bool val) {
    state = state.copyWith(newCalendar: val);
  }

  void toggleAssignedCalendarAccepted(bool val) {
    state = state.copyWith(assignedCalendarAccepted: val);
  }

  void toggleAssignedCalendarRejected(bool val) {
    state = state.copyWith(assignedCalendarRejected: val);
  }

  void toggleNewCustomer(bool val) {
    state = state.copyWith(newCustomer: val);
  }

  void toggleNewJob(bool val) {
    state = state.copyWith(newJob: val);
  }

  void toggleAssignedJobAccepted(bool val) {
    state = state.copyWith(assignedJobAccepted: val);
  }

  void toggleAssignedJobRejected(bool val) {
    state = state.copyWith(assignedJobRejected: val);
  }

  void toggleCustomerEventAdded(bool val) {
    state = state.copyWith(customerEventAdded: val);
  }
}

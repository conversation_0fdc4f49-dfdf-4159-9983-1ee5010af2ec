import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';

enum RuleType {
  speedViolation,
  areaEntryExitViolation,
  notifications,
}

extension RuleTypeX on RuleType {
  String label(BuildContext context) {
    switch (this) {
      case RuleType.speedViolation:
        return context.tr.speedViolation;
      case RuleType.areaEntryExitViolation:
        return context.tr.areaEntryExitViolation;
      case RuleType.notifications:
        return context.tr.notifications;
    }
  }
}

class DisplayRuleType {
  DisplayRuleType({
    required this.rule,
    required BuildContext context,
  }) : label = rule.label(context);

  final RuleType rule;
  final String label;

  @override
  String toString() => label;

  static List<DisplayRuleType> getList(BuildContext context) => RuleType.values
      .map((rule) => DisplayRuleType(rule: rule, context: context))
      .toList();
}

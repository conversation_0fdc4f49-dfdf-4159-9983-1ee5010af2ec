part of '../presentation/forms_view.dart';

extension FormTemplateAnswersExtensions on FormTemplateAnswers {
  List<RowDataModel<dynamic>> getRowData(WidgetRef ref) {
    return questions.indexed.map((question) {
      final answer = getAnswerForQuestion(question.$2.fieldId);
      final columnTitle = question.$2.title;
      if (answer == null) {
        return RowDataModel<void>(
          columnName: columnTitle,
        );
      }
      return switch (question.$2.type) {
        FormFieldTypeEnum.date ||
        FormFieldTypeEnum.time =>
          RowDataModel<DateTime>(
            columnName: columnTitle,
            value: DateTime.parse(answer.answerText),
            cellBuilder: () {
              final dateTime = DateTime.parse(answer.answerText);
              final value = dateTime.getFormValue(mode: question.$2.type == FormFieldTypeEnum.date ? DateTimePickerMode.date : DateTimePickerMode.time);
              return Text(
                value,
                style: ATextStyle.text13,
              );
            },
          ),
        FormFieldTypeEnum.number => RowDataModel<num>(
            columnName: columnTitle,
            value: num.parse(answer.answerText),
          ),
        FormFieldTypeEnum.file => RowDataModel<List<DownloadedFileModel>>(
            columnName: columnTitle,
            value: answer.answerText.toDownloadedFileModelList(),
            cellBuilder: () {
              final files = answer.answerText.toDownloadedFileModelList();
              return Wrap(
                spacing: 4,
                runSpacing: 4,
                children: files.map((file) {
                  return InkWell(
                    onTap: () {
                      if (file.url == null) return;
                      ref.read(urlManagerProvider).goToLink(file.url!);
                    },
                    child: Text(
                      file.name,
                      style: ATextStyle.text13.copyWith(
                        decoration: TextDecoration.underline,
                        background: Paint()
                          ..color = AColor.primaryColor.withValues(alpha: .4)
                          ..style = PaintingStyle.fill,
                      ),
                    ),
                  );
                }).toList(),
              );
            },
          ),
        _ => RowDataModel<String>(
            columnName: columnTitle,
            value: answer.answerText,
          ),
      };
    }).toList();
  }
}

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:gap/gap.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/url_manager/url_manager.dart';
import 'package:smart_team_web/core/router/auto_router.gr.dart';
import 'package:smart_team_web/src/forms/application/form_answers_provider.dart';
import 'package:smart_team_web/src/forms/application/form_templates_notifier.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/date_time_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/shared/utils/dt_format.dart';
import 'package:smart_team_web/src/shared/utils/dt_util.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/toast/toast_manager.dart';
import 'package:smart_team_web/src/widgets/app_dialog/app_dialog.dart';
import 'package:smart_team_web/src/widgets/app_segmented_switcher.dart';
import 'package:smart_team_web/src/widgets/base_async_provider_widget.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/date_time_picker/date_time_picker.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_column_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_row_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/st_data_table.dart';
import 'package:smart_team_web/src/widgets/question_builder/model/downloaded_file_model.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

part 'widgets/form_templates_table.dart';
part 'widgets/dialogs/delete_template_dialog.dart';
part 'widgets/dialogs/form_answers_dialog.dart';
part '../extensions/form_template_answers_extensions.dart';

@RoutePage(name: 'FormsRoute')
class FormsView extends HookConsumerWidget {
  const FormsView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formTemplatesNotifier = ref.watch(formTemplatesNotifierProvider);
    final tabController = useTabController(initialLength: 3);
    return Scaffold(
      body: SizedBox.expand(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 40)
              .copyWith(top: 35, bottom: 16),
          child: BaseAsyncProviderWidget<List<FormTemplateAndUsers>>(
            value: formTemplatesNotifier,
            builder: (formTemplatesInfo) {
              final allTemplates = formTemplatesInfo;
              final highPrioTemplates = useMemoized(
                () => formTemplatesInfo
                    .getTemplatesByPriority(FormPriorityEnum.high),
                [formTemplatesInfo],
              );
              final answeredTemplates = useMemoized(
                () => formTemplatesInfo.getTemplatesWithoutSubmissions(),
                [formTemplatesInfo],
              );
              final tabLists = [
                allTemplates,
                highPrioTemplates,
                answeredTemplates,
              ];
              return Column(
                spacing: 10,
                children: [
                  Row(
                    children: [
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: AppSegmentedSwitcher(
                          tabs: const ['Tümü', 'Önemli', 'Cevaplanmayan'],
                          width: context.responsive(
                            desktop: 1000,
                            tablet: 800,
                            mobile: 500,
                          ),
                          suffixWidgets: List.generate(
                            3,
                            (index) {
                              final templateCount = tabLists[index].length;
                              if (templateCount == 0) {
                                return const SizedBox.shrink();
                              }
                              return Container(
                                height: 20,
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                decoration: BoxDecoration(
                                  color: AColor.primaryColor,
                                  borderRadius: BorderRadius.circular(60),
                                ),
                                child: Center(
                                  child: Text(
                                    templateCount.toString(),
                                    style: ATextStyle.text12.copyWith(
                                      color: AColor.white,
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                          onTabChange: tabController.animateTo,
                        ),
                      ),
                      const Spacer(),
                      Align(
                        alignment: Alignment.centerRight,
                        child: LoadingElevatedButton(
                          text: 'Form Ekle'.hardcoded,
                          isLoadingEnabled: false,
                          suffix: const Icon(
                            Icons.add,
                            color: AColor.white,
                          ),
                          onPressed: () async {
                            await context.navigateTo(
                              EditFormRoute(
                                formTemplateId: UUIDGenerator.generate(),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  Expanded(
                    child: TabBarView(
                      controller: tabController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        _FormTemplatesTable(
                          key: const Key('allTemplates'),
                          formTemplatesAndUsers: allTemplates,
                        ),
                        _FormTemplatesTable(
                          key: const Key('highPrioTemplates'),
                          formTemplatesAndUsers: highPrioTemplates,
                        ),
                        _FormTemplatesTable(
                          key: const Key('answeredTemplates'),
                          formTemplatesAndUsers: answeredTemplates,
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/online_user_manager/online_user_manager.dart';
import 'package:smart_team_web/src/shared/extensions/riverpod_extensions.dart';

final formTemplatesNotifierProvider = AutoDisposeAsyncNotifierProvider<
    FormTemplatesNotifier, List<FormTemplateAndUsers>>(
  FormTemplatesNotifier.new,
);

class FormTemplatesNotifier extends AutoDisposeAsyncNotifier<
    List<FormTemplateAndUsers>> {
  @override
  Future<List<FormTemplateAndUsers>> build() async {
    final onlineUser = await ref.watch(onlineUserManagerProvider.future);
    final ownerId = onlineUser!.user!.id!;
    final formTemplateSubscription = ref
        .read(formTemplateRepositoryProvider)
        .formTemplatesChangesStream(ownerId)
        .listen(_onData);

    ref.onDispose(formTemplateSubscription.cancel);

    return ref.read(formTemplateRepositoryProvider).fetchFormTemplatesAndUsers(
          ownerId: ownerId,
        );
  }

  void _onData(List<Map<String, dynamic>> data) {
    if (state.value == null || data.length == state.value!.length) return;
    ref.invalidateSelf();
  }

  Future<bool> deleteFormTemplate(FormTemplate formTemplate) async {
    try {
      await ref.executeWithLoading(() async {
        await ref
            .read(formTemplateRepositoryProvider)
            .update(formTemplate.copyWith(isActive: false));
      });
      return true;
    } catch (e, stackTrace) {
      debugPrint(e.toString());
      debugPrint(stackTrace.toString());
      return false;
    }
  }
}

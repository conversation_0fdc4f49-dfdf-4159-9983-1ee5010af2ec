import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';

enum ReportType {
  movement,
  time,
}

extension ReportTypeX on ReportType {
  String label(BuildContext context) {
    switch (this) {
      case ReportType.movement:
        return context.tr.movementReport;
      case ReportType.time:
        return context.tr.timeReport;
    }
  }
}

class DisplayReportType {
  DisplayReportType({
    required this.report,
    required BuildContext context,
  }) : label = report.label(context);

  final ReportType report;
  final String label;

  @override
  String toString() => label;

  static List<DisplayReportType> getList(BuildContext context) {
    return ReportType.values
        .map((report) => DisplayReportType(report: report, context: context))
        .toList();
  }
}

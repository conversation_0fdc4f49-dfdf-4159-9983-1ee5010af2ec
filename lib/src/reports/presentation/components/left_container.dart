part of '../reports_view.dart';

class LeftContainer extends HookConsumerWidget {
  const LeftContainer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: ReportConstants.leftContainerWidth(context),
      padding: context.responsive(
        desktop: const EdgeInsets.symmetric(horizontal: 30).copyWith(top: 24),
        tablet: const EdgeInsets.symmetric(horizontal: 20).copyWith(top: 16),
      ),
      child: const Align(
        alignment: Alignment.topCenter,
        child: EmployeeTreeView(
          selectedMembers: <TeamMemberModel>[],
        ),
      ),
    );
  }
}

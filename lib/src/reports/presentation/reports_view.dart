import 'dart:math';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/reports/constants/report_contants.dart';
import 'package:smart_team_web/src/reports/enum/report_type.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/dropdown/a_new_dropdown.dart';
import 'package:smart_team_web/src/widgets/employee_tree_view/employee_tree_view.dart';
import 'package:smart_team_web/src/widgets/text_form_field/date_time_picker_field.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

part 'components/left_container.dart';

@RoutePage(name: 'ReportsRoute')
class ReportsView extends StatelessWidget {
  const ReportsView({super.key});

  @override
  Widget build(BuildContext context) {
    final spaceWidth = context.responsive(
          desktop: 24 * 2,
          tablet: 16 * 2,
        ) +
        16;
    return Scaffold(
      backgroundColor: AColor.backgroundColor,
      body: Padding(
        padding: context.responsive(
          desktop: const EdgeInsets.all(24),
          tablet: const EdgeInsets.all(16),
        ),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            spacing: 16,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const LeftContainer(),
              LayoutBuilder(
                builder: (context, constraints) {
                  final leftWidth = ReportConstants.leftContainerWidth(context);
                  return SizedBox(
                    width: max(
                      context.width - (leftWidth + spaceWidth),
                      ReportConstants.rightContainerMinWidth,
                    ),
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          spacing: 16,
                          children: [
                            SizedBox(
                              width: context.width / 6,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  TextWidget(
                                    context.tr.reportType,
                                    style: ATextStyle.small,
                                  ),
                                  HookConsumer(
                                    builder: (context, ref, _) {
                                      final displayReportTypes =
                                          DisplayReportType.getList(context);
                                      final selectedReportType =
                                          useState<DisplayReportType?>(null);
                                      return ANewDropDown<DisplayReportType>(
                                        placeholder: context.tr.select,
                                        itemList: displayReportTypes,
                                        decoration: CommonDecorations
                                                .containerDecoration()
                                            .copyWith(
                                          border: Border.all(
                                            color: Colors.grey.shade300,
                                          ),
                                        ),
                                        selectedItems:
                                            selectedReportType.value == null
                                                ? []
                                                : [selectedReportType.value!],
                                        onSelected: (selected) {
                                          selectedReportType.value = selected;
                                        },
                                        itemBuilder: (displayReport) => Text(
                                          displayReport?.toString() ??
                                              context.tr.select,
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              width: context.width / 5,
                              child: Container(
                                decoration:
                                    CommonDecorations.containerDecoration(),
                                child: DateTimePickerField(
                                  header: context.tr.startTime,
                                ),
                              ),
                            ),
                            SizedBox(
                              width: context.width / 5,
                              child: Container(
                                decoration:
                                    CommonDecorations.containerDecoration(),
                                child: DateTimePickerField(
                                  header: context.tr.endTime,
                                ),
                              ),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TextWidget(
                                  '',
                                  style: ATextStyle.small,
                                ),
                                LoadingElevatedButton(
                                  height: 35,
                                  width: 100,
                                  onPressed: () async {},
                                  text: context.tr.save,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

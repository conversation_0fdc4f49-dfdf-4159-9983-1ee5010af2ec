name: smart_team_web
description: "A new Flutter project."

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ^3.6.0


dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter

 ## Localization management
  flutter_localizations:
    sdk: flutter
  intl: any

  ## Smart Team Common
  smart_team_common:
    path: ../smart_team_common/flutter_lib

  ## Navigation
  auto_route: ^10.0.1
  url_launcher: ^6.3.1

  ## State Management
  hooks_riverpod: ^2.6.1
  flutter_hooks: ^0.21.2

  ## Animations
  flutter_animate: ^4.5.2
  animate_do: ^4.2.0

  ## Code design & performans tools
  data_channel: ^2.0.0+1
  json_annotation: ^4.9.0
  freezed_annotation: ^3.0.0

  ## Supabase
  supabase_flutter: ^2.9.0

  ## Maps
  google_maps_flutter: ^2.10.0

  ## UI tools
  gap: ^3.0.1
  fluttertoast: ^8.2.10
  fl_chart: ^0.70.2
  flutter_colorpicker: ^1.1.0
  google_fonts: ^6.2.1
  syncfusion_flutter_core: ^28.2.6
  syncfusion_flutter_datagrid: ^28.2.5
  syncfusion_flutter_datagrid_export: ^28.2.6
  syncfusion_flutter_xlsio: ^28.2.6
  syncfusion_flutter_pdf: ^28.2.6
  pointer_interceptor: ^0.10.1+2

  ## Calendar
  table_calendar: ^3.1.3 ##downgraded to fix intl error

  ## Favicons
  flutter_launcher_icons: ^0.14.3

  ## Network
  dio: ^5.8.0+1

  ## Phone area
  country_picker: ^2.0.27
  country_phone_validator: ^1.0.1

  ## Translation
  slang: ^4.7.2
  slang_flutter: ^4.7.0

  cupertino_icons: ^1.0.8
  styled_text: ^8.1.0
  flutter_svg: ^2.0.17
  flutter_portal: ^1.1.4
  uuid: ^4.5.1
  http: ^1.3.0


dev_dependencies:
  flutter_test:
    sdk: flutter

  riverpod_lint: ^2.6.4
  json_serializable: ^6.9.4
  freezed: ^3.0.6
  build_runner: ^2.4.15
  auto_route_generator: ^10.0.1
  flutter_gen_runner: ^5.9.0
  ## Linter
  very_good_analysis: ^7.0.0
  slang_build_runner: ^4.7.0

flutter_gen:
  output: lib/src/shared/constants/
  line_length: 120
  integrations:
    image: true
    flutter_svg: true
  assets:
    outputs:
      package_parameter_enabled: true
  
  fonts:
    outputs:
      package_parameter_enabled: true

slang:
  base_locale: en
  fallback_strategy: base_locale
  input_directory: lib/i18n
  input_file_pattern: .i18n.json
  output_directory: lib/i18n
  output_file_name: strings.g.dart
  locale_handling: true
  flutter_integration: true
  namespaces: false

flutter:
  uses-material-design: true

  # Enable generation of localized Strings from arb files.
  generate: true

